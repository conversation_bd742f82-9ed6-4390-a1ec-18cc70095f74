package com.metaorg.web.controller.library;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonPolitics;
import com.metaorg.library.service.IBizPersonPoliticsService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 政治情况Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/politics")
public class BizPersonPoliticsController extends BaseController
{
    private String prefix = "library/politics";

    @Autowired
    private IBizPersonPoliticsService bizPersonPoliticsService;

    @RequiresPermissions("library:politics:view")
    @GetMapping()
    public String politics()
    {
        return prefix + "/politics";
    }

    /**
     * 查询政治情况列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonPolitics bizPersonPolitics)
    {
        startPage();
        List<BizPersonPolitics> list = bizPersonPoliticsService.selectBizPersonPoliticsList(bizPersonPolitics);
        return getDataTable(list);
    }

    /**
     * 导出政治情况列表
     */
    @RequiresPermissions("library:politics:export")
    @Log(title = "政治情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonPolitics bizPersonPolitics)
    {
        List<BizPersonPolitics> list = bizPersonPoliticsService.selectBizPersonPoliticsList(bizPersonPolitics);
        ExcelUtil<BizPersonPolitics> util = new ExcelUtil<BizPersonPolitics>(BizPersonPolitics.class);
        return util.exportExcel(list, "政治情况数据");
    }

    /**
     * 新增政治情况
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存政治情况
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "政治情况", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "政治情况", tableName = PersonTable.BIZ_PERSON_POLITICS, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonPolitics bizPersonPolitics)
    {
        return toAjax(bizPersonPoliticsService.insertBizPersonPolitics(bizPersonPolitics));
    }

    /**
     * 修改政治情况
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonPolitics bizPersonPolitics = bizPersonPoliticsService.selectBizPersonPoliticsById(id);
        mmap.put("bizPersonPolitics", bizPersonPolitics);
        return prefix + "/edit";
    }

    /**
     * 修改保存政治情况
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "政治情况", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "政治情况", tableName = PersonTable.BIZ_PERSON_POLITICS, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonPolitics bizPersonPolitics)
    {
        return toAjax(bizPersonPoliticsService.updateBizPersonPolitics(bizPersonPolitics));
    }

    /**
     * 删除政治情况
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "政治情况", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "政治情况", tableName = PersonTable.BIZ_PERSON_POLITICS, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonPoliticsService.deleteBizPersonPoliticsByIds(ids));
    }

    /**
     * 排序政治情况
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "政治情况", businessType = BusinessType.UPDATE)
    @PostMapping( "/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids)
    {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++)
        {
            BizPersonPolitics bizPersonPolitics = bizPersonPoliticsService.selectBizPersonPoliticsById(idsArray[i]);
            bizPersonPolitics.setOrderNum(Convert.toLong(i + 1));
            bizPersonPoliticsService.updateBizPersonPolitics(bizPersonPolitics);
        }
        return success();
    }

    private long getMaxOrderNum(String personId)
    {
        BizPersonPolitics bizPersonPolitics = new BizPersonPolitics();
        bizPersonPolitics.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonPolitics> list = bizPersonPoliticsService.selectBizPersonPoliticsList(bizPersonPolitics);
        if(!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
