{"content": {"promo": {"title": "数字化运营，畅享商业新纪元", "subTitle": "地产和商业", "summary": "连接运营商、商户和消费者，打造全域营销场景，打造全域商业生态圈，数字化变革的有力武器", "image": "/assets/images/o2o/mobile-app.png"}, "advantage": {"title": "全域数字化商业营销工具", "subTitle": "商业广场", "summary": "商业广场数字化营销平台可以为运营方提供更好的客流量和数据支持，为入驻商户带来目标客户和优化营销机会，同时为客户提供个性化推荐和便捷购物体验，实现多方共赢的效果。", "trial": {"title": "联系我们详细了解", "url": "/about/#div_ContactUs"}, "items": [{"title": "为运营方", "image": "/assets/images/o2o/operator.png", "summary": ["提高运营管理效率", "降低运营成本", "实现数据驱动精准营销", "提高营销效果", "提升商业广场品牌形象", "增加商业价值"]}, {"title": "为客户", "image": "/assets/images/o2o/consumer.png", "summary": ["提高购物体验", "降低购物成本", "实现精准推荐", "提高购物满意度", "提供个性化服务", "提高客户忠诚度"]}, {"title": "为商家", "image": "/assets/images/o2o/store.png", "summary": ["提高经营效率", "降低经营成本", "提升商品销售量", "提高利润率", "拓展销售渠道", "增加曝光度"]}]}, "hero": {"title": "全域数字化商业营销工具", "subTitle": "商业广场", "summary": "为大型商业广场运营商提供的全面的数字化运营解决方案。平台主要涉及运营商、入驻商家和消费者三个主要角色。平台的核心目标是通过线上的营销策略，实现引流、蓄客、拓客、留存、裂变和变现，将线上客户转化为线下消费，帮助入驻商家获取更多流量和消费，提高商业广场的知名度和影响力，形成运营商、入驻商家和消费者的良性循环。", "items": [{"icon": "mdi-vector-triangle", "iconBgClass": "", "title": "多渠道客户获取", "summary": "通过线上广告、社交媒体推广、搜索引擎优化等多种渠道，吸引潜在客户关注商业广场，提高客户获取效率。", "aosDelay": "100"}, {"icon": "mdi-account-outline", "iconBgClass": "bg2", "title": "精准客户画像", "summary": "通过消费者数据分析、客户行为追踪、社交媒体互动等方式，深入了解客户需求和兴趣，建立精准客户画像。", "aosDelay": "200"}, {"icon": "mdi-account-star", "iconBgClass": "bg3", "title": "个性化营销", "summary": "根据客户画像和实时数据分析，提供个性化的营销方案，包括内容推荐、优惠券发放、定向广告投放等，提高营销效果和客户满意度。", "aosDelay": "300"}, {"icon": "mdi-chart-bar", "iconBgClass": "bg4", "title": "数据分析与监控", "summary": "通过数据仪表盘、实时报表、数据挖掘等方式，监控客户行为、销售数据、广告效果等指标，实时调整营销策略，提高效果和ROI。", "aosDelay": "400"}, {"icon": "mdi-cart-outline", "iconBgClass": "bg5", "title": "联合营销", "summary": "通过与入驻商家合作，开展联合营销活动，吸引更多客户关注商业广场和入驻商家，提高品牌知名度和影响力。", "aosDelay": "500"}, {"icon": "mdi-shopping", "iconBgClass": "bg6", "title": "电子商务", "summary": "通过线上商城、微信小程序、扫码购物等方式，提供多种电子商务服务，为消费者提供更加便捷的购物体验，促进线上线下互动和转化。", "aosDelay": "600"}]}, "feature": {"title": "具有核心竞争力的数字化营销平台", "summary": "平台可以更好地吸引潜在客户，提供定制化的营销策略，并根据数据洞察不断优化和改进营销活动，从而增加客户参与度、提升销售业绩，并为商业广场创造更大的商业价值。", "items": [{"title": "智能获取客户", "summary": "通过多种渠道，如社交媒体、搜索引擎营销、电子邮件和短信营销等方式，针对特定的目标受众进行广告投放和推广活动。通过精确的定位和广告投放策略，平台可以吸引到潜在客户，并将其引导到商业广场。", "image": "/assets/images/o2o/screenshots1_small.png", "items": [{"iconName": "fa-solid fa-eye", "iconBgClass": "", "title": "多渠道获客", "summary": "通过多种渠道获取客户，如线上广告、社交媒体、口碑营销等。", "aosDelay": "100"}, {"iconName": "fa-solid fa-layer-group", "iconBgClass": "bg2", "title": "数据挖掘和分析", "summary": "了解客户的偏好和需求，以便制定更加精准的营销策略。", "aosDelay": "200"}, {"iconName": "fa-solid fa-layer-group", "iconBgClass": "bg2", "title": " 营销活动管理", "summary": "创建各类营销活动，如满减、打折、赠品等，吸引更多的客户。", "aosDelay": "200"}]}, {"title": "个性化营销", "summary": "平台通过对客户画像和实时数据分析，提供个性化的营销方案，如内容推荐、优惠券发放等。平台能够为客户提供个性化的服务，提高客户满意度和忠诚度，促进转化。", "image": "/assets/images/o2o/screenshots2_small.png", "items": [{"iconName": "fa-solid fa-layer-group", "iconBgClass": "bg2", "title": "精准客户画像", "summary": "通过消费者数据分析、客户行为追踪、社交媒体互动等方式，深入了解客户需求和兴趣，建立精准客户画像。", "aosDelay": "200"}, {"iconName": "fa-solid fa-eye", "iconBgClass": "", "title": "客户分群", "summary": "通过客户分群，将客户划分为不同的群体，如学生、白领、家庭主妇等，并根据不同的群体特点，制定个性化的营销策略。", "aosDelay": "100"}, {"iconName": "fa-solid fa-layer-group", "iconBgClass": "bg2", "title": "个性化推荐", "summary": "通过分析客户的历史购买记录和偏好，向客户推荐相应的商品和服务，以提高客户的购买率和满意度", "aosDelay": "200"}]}, {"title": "数据驱动运营", "summary": "平台通过数据分析和监控，对客户行为、销售数据、广告效果等进行实时跟踪，以数据为驱动，优化营销策略和运营决策，提高客户留存率和复购率。平台的数据分析和挖掘能力，为商业广场提供了精准的决策支持，进一步提升运营效率和业绩。", "image": "/assets/images/o2o/screenshots3_small.png", "items": [{"iconName": "fa-solid fa-eye", "iconBgClass": "", "title": "数据采集和分析", "summary": "通过线上广告、社交媒体推广、搜索引擎优化等多种渠道，吸引潜在客户关注商业广场，提高客户获取效率。", "aosDelay": "100"}, {"iconName": "fa-solid fa-layer-group", "iconBgClass": "bg2", "title": "数据可视化", "summary": "通过消费者数据分析、客户行为追踪、社交媒体互动等方式，深入了解客户需求和兴趣，建立精准客户画像。", "aosDelay": "200"}, {"iconName": "fa-solid fa-layer-group", "iconBgClass": "bg2", "title": " 数据驱动决策", "summary": "通过消费者数据分析、客户行为追踪、社交媒体互动等方式，深入了解客户需求和兴趣，建立精准客户画像。", "aosDelay": "200"}]}]}, "funFacts": [{"iconName": "fa-solid fa-cloud-arrow-down", "title": "Total Downloads", "number": "10M", "aosDelay": "100", "bgColor": ""}, {"iconName": "fa-solid fa-star", "title": "Total Reviews", "number": "799K", "aosDelay": "200", "bgColor": "bg1"}, {"iconName": "fa-solid fa-globe", "title": "Worldwide Countries", "number": "150+", "aosDelay": "300", "bgColor": "bg2"}, {"iconName": "fa-solid fa-user-check", "title": "Active Users", "number": "5M", "aosDelay": "400", "bgColor": "bg3"}], "trial": {"title": "Let's Get Your Free Copy From Apple and Play Store", "subTitle": "DOWNLOAD APP", "description": "Instant free download from store Cloud based storage for your data backup just log in with your mail account from play store and using whatever you want for your business purpose orem ipsum dummy text."}, "screenshot": {"title": "为您的客户提供沉浸式体验", "subTitle": "专业的交互设计", "items": [{"image": "/images/app/screenshots1.png"}, {"image": "/images/app/screenshots2.png"}, {"image": "/images/app/screenshots3.png"}, {"image": "/images/app/screenshots4.png"}, {"image": "/images/app/screenshots5.png"}, {"image": "/images/app/screenshots4.png"}]}, "download": {"title": "", "subTitle": "", "description": ""}, "faq": {"title": "常见问题", "subTitle": "FAQ", "items": [{"question": "平台有哪些核心功能？", "answer": "智能客户获取、个性化营销和数据驱动运营是该平台的核心功能。"}, {"question": "平台的主要目标用户？", "answer": "主要目标用户是大型商业广场运营商。"}, {"question": "平台如何帮助商业广场运营商提高营销效率？", "answer": "提供智能客户获取、个性化营销和数据驱动运营等功能，帮助运营商提高营销效率和客户转化率。"}, {"question": "平台如何帮助商家吸引客户？", "answer": "通过线上的营销手段帮助商家获得更多的流量和消费者，并且能够提供个性化的消费服务，吸引更多的客户。"}, {"question": "平台如何提高商业广场的裂变效应？", "answer": "提供个性化营销和数据驱动运营等功能，帮助商业广场运营商和商家提高用户留存和转化率，从而实现裂变效应。"}, {"question": "如何实现数据驱动运营？", "answer": "通过数据分析和智能算法，能够实时监测用户行为和偏好，帮助商业广场运营商和商家进行数据驱动运营。"}, {"question": "平台如何保障客户数据安全？", "answer": "采用多层级数据安全保障措施，包括数据加密、权限管理和安全审计等，保障客户数据的安全和隐私。"}]}, "partner": {"items": [{"image": "/images/logos/tech/Axure_logo.jpg"}, {"image": "/images/logos/tech/figma_logo.jpg"}, {"image": "/images/logos/tech/ps_logo.jpg"}, {"image": "/images/logos/tech/React_logo.jpg"}, {"image": "/images/logos/tech/Flutter_logo.jpg"}, {"image": "/images/logos/tech/Sketch_logo.jpg"}, {"image": "/images/logos/tech/ae_logo.jpg"}, {"image": "/images/logos/tech/ai_logo.jpg"}]}}}