package com.metaorg.web.controller.library;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonAddress;
import com.metaorg.library.service.IBizPersonAddressService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 地址与通信Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/address")
public class BizPersonAddressController extends BaseController {
    private String prefix = "library/address";

    @Autowired
    private IBizPersonAddressService bizPersonAddressService;

    @GetMapping()
    public String address() {
        return prefix + "/address";
    }

    /**
     * 查询地址与通信列表
     */
    @RequiresPermissions("library:person:edit")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonAddress bizPersonAddress) {
        startPage();
        List<BizPersonAddress> list = bizPersonAddressService.selectBizPersonAddressList(bizPersonAddress);
        return getDataTable(list);
    }

    /**
     * 导出地址与通信列表
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "地址与通信", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonAddress bizPersonAddress) {
        List<BizPersonAddress> list = bizPersonAddressService.selectBizPersonAddressList(bizPersonAddress);
        ExcelUtil<BizPersonAddress> util = new ExcelUtil<BizPersonAddress>(BizPersonAddress.class);
        return util.exportExcel(list, "地址与通信数据");
    }

    /**
     * 新增地址与通信
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap) {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存地址与通信
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "地址与通信", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "地址与通信", tableName = PersonTable.BIZ_PERSON_ADDRESS, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonAddress bizPersonAddress) {
        return toAjax(bizPersonAddressService.insertBizPersonAddress(bizPersonAddress));
    }

    /**
     * 修改地址与通信
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPersonAddress bizPersonAddress = bizPersonAddressService.selectBizPersonAddressById(id);
        mmap.put("bizPersonAddress", bizPersonAddress);
        return prefix + "/edit";
    }

    /**
     * 修改保存地址与通信
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "地址与通信", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "地址与通信", tableName = PersonTable.BIZ_PERSON_ADDRESS, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonAddress bizPersonAddress) {
        return toAjax(bizPersonAddressService.updateBizPersonAddress(bizPersonAddress));
    }

    /**
     * 删除地址与通信
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "地址与通信", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "地址与通信", tableName = PersonTable.BIZ_PERSON_ADDRESS, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizPersonAddressService.deleteBizPersonAddressByIds(ids));
    }

    /**
     * 排序地址与通信
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "地址与通信", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++) {
            BizPersonAddress bizPersonAddress = bizPersonAddressService.selectBizPersonAddressById(idsArray[i]);
            bizPersonAddress.setOrderNum(Convert.toLong(i + 1));
            bizPersonAddressService.updateBizPersonAddress(bizPersonAddress);
        }
        return success();
    }

    private long getMaxOrderNum(String personId) {
        BizPersonAddress bizPersonAddress = new BizPersonAddress();
        bizPersonAddress.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonAddress> list = bizPersonAddressService.selectBizPersonAddressList(bizPersonAddress);
        if (!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
