package com.metaorg.web.controller.system;

import com.metaorg.common.annotation.Log;
import com.metaorg.common.constant.UserConstants;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.Ztree;
import com.metaorg.common.core.domain.entity.SysDictData;
import com.metaorg.system.domain.SysPersonPost;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.system.domain.SysPersonPostTerm;
import com.metaorg.system.service.ISysDictTypeService;
import com.metaorg.system.service.ISysPersonPostService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 职务信息
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/person_post")
public class SysPersonPostController extends BaseController
{
    private String prefix = "system/person_post";

    @Autowired
    private ISysPersonPostService personPostService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @RequiresPermissions("system:person_post:view")
    @GetMapping()
    public String person_post()
    {
        return prefix + "/person_post";
    }

    @RequiresPermissions("system:person_post:list")
    @PostMapping("/list")
    @ResponseBody
    public List<SysPersonPost> list(SysPersonPost personPost)
    {
        List<SysPersonPost> list = personPostService.selectPersonPostList(personPost);
        return list;
    }

    /**
     * 新增职务分类（一级职务）
     */
    @GetMapping("/add_first")
    public String add_first(ModelMap mmap)
    {
        mmap.put("maxOrderNum", getMaxOrderNum("0"));
        return prefix + "/add_first";
    }

    /**
     * 新增职务
     */
    @GetMapping("/add/{parentId}")
    public String add(@PathVariable("parentId") String parentId, ModelMap mmap)
    {
        List<SysDictData> deptLevellist = dictTypeService.selectDictDataByType("sys_dept_level");
        List<SysPersonPostTerm> terms = new ArrayList<SysPersonPostTerm>();
        if(deptLevellist !=  null) {
            for (SysDictData dict : deptLevellist) {
                SysPersonPostTerm term = new SysPersonPostTerm();
                term.setDeptLevelCode(dict.getDictValue());
                term.setDeptLevelName(dict.getDictLabel());
                term.setFlag(false);
                terms.add(term);
            }
        }

        mmap.put("terms", terms);
        mmap.put("personPost", personPostService.selectPersonPostById(parentId));
        mmap.put("maxOrderNum", getMaxOrderNum(parentId));

        return prefix + "/add";
    }

    /**
     * 新增保存职务
     */
    @Log(title = "职务管理", businessType = BusinessType.INSERT)
    @RequiresPermissions("system:person_post:add")
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam(value = "deptLevls", defaultValue = "") String[] deptLevls, @Validated SysPersonPost personPost)
    {
        if (!personPostService.checkPersonPostNameUnique(personPost))
        {
            return error("新增职务'" + personPost.getPostName() + "'失败，职务名称已存在");
        }
        personPost.setCreateBy(getLoginName());

        return toAjax(personPostService.insertPersonPost(personPost, deptLevls));
    }

    /**
     * 修改职务分类（一级职务）
     */
    @RequiresPermissions("system:person_post:edit")
    @GetMapping("/edit_first/{personPostId}")
    public String edit_first(@PathVariable("personPostId") String personPostId, ModelMap mmap)
    {
        SysPersonPost personPost = personPostService.selectPersonPostById(personPostId);
        mmap.put("personPost", personPost);

        return prefix + "/edit_first";
    }

    /**
     * 修改职务
     */
    @RequiresPermissions("system:person_post:edit")
    @GetMapping("/edit/{personPostId}")
    public String edit(@PathVariable("personPostId") String personPostId, ModelMap mmap)
    {
        SysPersonPost personPost = personPostService.selectPersonPostById(personPostId);
        if (StringUtils.isNotNull(personPost) && "0" == personPostId)
        {
            personPost.setParentName("无");
        }

        List<SysDictData> deptLevellist = dictTypeService.selectDictDataByType("sys_dept_level");
        List<SysPersonPostTerm> terms = new ArrayList<SysPersonPostTerm>();
        if(deptLevellist !=  null) {
            for (SysDictData dict : deptLevellist) {
                SysPersonPostTerm term = new SysPersonPostTerm();
                term.setDeptLevelCode(dict.getDictValue());
                term.setDeptLevelName(dict.getDictLabel());
                term.setFlag(false);
                term.setPostTerm(0);
                for (SysPersonPostTerm personPostTerm : personPost.getTerms())
                {
                    if (dict.getDictValue().equals(personPostTerm.getDeptLevelCode()))
                    {
                        term.setPostTerm(personPostTerm.getPostTerm());
                        term.setFlag(true);
                        break;
                    }
                }
                terms.add(term);
            }
        }

        mmap.put("terms", terms);
        mmap.put("personPost", personPost);
        return prefix + "/edit";
    }

    /**
     * 修改保存职务
     */
    @Log(title = "职务管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("system:person_post:edit")
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam(value = "deptLevls", defaultValue = "") String[] deptLevls, @Validated SysPersonPost personPost)
    {
        String personPostId = personPost.getPersonPostId();
        if (!personPostService.checkPersonPostNameUnique(personPost))
        {
            return error("修改职务'" + personPost.getPostName() + "'失败，职务名称已存在");
        }
        else if (personPost.getParentId() == personPostId)
        {
            return error("修改职务'" + personPost.getPostName() + "'失败，上级职务不能是自己");
        }
        else if (StringUtils.equals(UserConstants.PERSONPOST_DISABLE, personPost.getStatus()) && personPostService.selectNormalChildrenPersonPostById(personPostId) > 0)
        {
            return AjaxResult.error("该职务包含未停用的子职务！");
        }
        personPost.setUpdateBy(getLoginName());
        return toAjax(personPostService.updatePersonPost(personPost, deptLevls));
    }

    /**
     * 删除
     */
    @Log(title = "职务管理", businessType = BusinessType.DELETE)
    @RequiresPermissions("system:person_post:remove")
    @GetMapping("/remove/{personPostId}")
    @ResponseBody
    public AjaxResult remove(@PathVariable("personPostId") String personPostId)
    {
        if (personPostService.selectPersonPostCount(personPostId) > 0)
        {
            return AjaxResult.warn("存在下级职务,不允许删除");
        }
        return toAjax(personPostService.deletePersonPostById(personPostId));
    }

    /**
     * 校验职务名称
     */
    @PostMapping("/checkPersonPostNameUnique")
    @ResponseBody
    public boolean checkPersonPostNameUnique(SysPersonPost personPost)
    {
        return personPostService.checkPersonPostNameUnique(personPost);
    }

    /**
     * 选择职务树
     * 
     * @param personPostId 职务ID
     * @param excludeId 排除ID
     */
    @GetMapping(value = { "/selectPersonPostTree/{personPostId}", "/selectPersonPostTree/{personPostId}/{excludeId}" })
    public String selectDeptTree(@PathVariable("personPostId") String personPostId,
            @PathVariable(value = "excludeId", required = false) String excludeId, ModelMap mmap)
    {
        mmap.put("personPost", personPostService.selectPersonPostById(personPostId));
        mmap.put("excludeId", excludeId);
        return prefix + "/tree";
    }

    /**
     * 加载职务列表树（排除下级）
     */
    @GetMapping("/treeData/{excludeId}")
    @ResponseBody
    public List<Ztree> treeDataExcludeChild(@PathVariable(value = "excludeId", required = false) String excludeId)
    {
        SysPersonPost personPost = new SysPersonPost();
        personPost.setExcludeId(excludeId);
        List<Ztree> ztrees = personPostService.selectPersonPostTreeExcludeChild(personPost);
        return ztrees;
    }

    private long getMaxOrderNum(String parentId)
    {
        SysPersonPost personPost = new SysPersonPost();
        personPost.setParentId(parentId);
        List<SysPersonPost> personPostList = personPostService.selectPersonPostList(personPost);
        // 使用 Stream 排序
        personPostList = personPostList.stream().sorted(Comparator.comparing(SysPersonPost::getOrderNum).reversed())
                .collect(Collectors.toList());
        if(!personPostList.isEmpty()) {
            return personPostList.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
