package com.metaorg.library.domain;
import java.util.*;
import com.metaorg.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.metaorg.common.utils.uuid.IdUtils;

public class BizStatisticPositionAdjust extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 统计标识 */
    private String id;
    /** 批次代码 */
    private String batchNo;
    /** 部门标识 */
    private Long deptId;
    /** 部门名称 */
    private String deptName;
    /** 统计日期 */
    private String statisticDate;
    /** 调整详情 */
    private List<Map<String, Object>> adjustDetails;
    /** 描述信息 */
    private String description;
    /** 扩展信息 */
    private Map<String, Object> extras;

    public static BizStatisticPositionAdjust fromBizStatisticPositionAdjustTplVM(BizStatisticPositionAdjustTplVM pvm) {
        BizStatisticPositionAdjust positionAdjust = new BizStatisticPositionAdjust();

        String batchNo = pvm.getBatchNo();

        Long deptId = null;
        String deptName = "";
        if (pvm.getDept() != null){
            deptId = pvm.getDept().getDeptId();
            deptName = pvm.getDept().getDeptName();
        }

        positionAdjust.setId(IdUtils.fastUUID());
        positionAdjust.setBatchNo(batchNo);
        positionAdjust.setDeptId(deptId);
        positionAdjust.setDeptName(deptName);
        positionAdjust.setStatisticDate(pvm.getStatisticDate());

        List<Map<String, Object>> adjustDetails = new ArrayList<>();
        if (pvm.getAdjustDetails() != null) {
            for (Map.Entry<String, Integer> entry : pvm.getAdjustDetails().entrySet()) {
                String columnName = entry.getKey();
                Integer value = entry.getValue();
                Map<String, Object> adjustDetail = new HashMap<>();
                adjustDetail.put("name", columnName);
                adjustDetail.put("value", value);
                adjustDetails.add(adjustDetail);
            }
        }
        positionAdjust.setAdjustDetails(adjustDetails);
   
        return positionAdjust;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    
    public String getStatisticDate() {
        return statisticDate;
    }

    public void setStatisticDate(String statisticDate) {
        this.statisticDate = statisticDate;
    }
    
    public List<Map<String, Object>> getAdjustDetails() {
        return adjustDetails;
    }

    public void setAdjustDetails(List<Map<String, Object>> adjustDetails) {
        this.adjustDetails = adjustDetails;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    
    public Map<String, Object> getExtras() {
        return extras;
    }

    public void setExtras(Map<String, Object> extras) {
        this.extras = extras;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("batchNo", getBatchNo())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("statisticDate", getStatisticDate())
            .append("adjustDetails", getAdjustDetails())
            .append("description", getDescription())
            .append("extras", getExtras())
            .toString();
    }
}
