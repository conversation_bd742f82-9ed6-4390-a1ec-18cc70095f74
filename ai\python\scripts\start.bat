@echo off
REM 新疆金融监管局处罚信息爬虫启动脚本 (Windows)

echo === 新疆金融监管局处罚信息爬虫启动脚本 ===

REM 检查环境变量
if "%OPENAI_API_KEY%"=="" (
    echo 错误: 未设置 OPENAI_API_KEY 环境变量
    pause
    exit /b 1
)

REM 检查配置文件
if not exist ".env" (
    echo 警告: .env 文件不存在，使用默认配置
    copy .env.example .env
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "config" mkdir config

REM 安装依赖
echo 安装Python依赖...
uv sync

REM 安装playwright浏览器
echo 安装Playwright浏览器...
uv run playwright install

REM 运行健康检查
echo 执行健康检查...
uv run python -m nfra_crawler.cli health

REM 启动应用
echo 启动NFRA爬虫应用...
uv run python -m nfra_crawler.main

pause
