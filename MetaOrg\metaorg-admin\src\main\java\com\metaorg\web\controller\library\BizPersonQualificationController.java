package com.metaorg.web.controller.library;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.core.text.Convert;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonQualification;
import com.metaorg.library.service.IBizPersonQualificationService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;

/**
 * 任职资格Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/qualification")
public class BizPersonQualificationController extends BaseController
{
    private String prefix = "library/qualification";

    @Autowired
    private IBizPersonQualificationService bizPersonQualificationService;

    @RequiresPermissions("library:qualification:view")
    @GetMapping()
    public String qualification()
    {
        return prefix + "/qualification";
    }

    /**
     * 查询任职资格列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonQualification bizPersonQualification)
    {
        startPage();
        List<BizPersonQualification> list = bizPersonQualificationService.selectBizPersonQualificationList(bizPersonQualification);
        return getDataTable(list);
    }

    /**
     * 导出任职资格列表
     */
    @RequiresPermissions("library:qualification:export")
    @Log(title = "任职资格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonQualification bizPersonQualification)
    {
        List<BizPersonQualification> list = bizPersonQualificationService.selectBizPersonQualificationList(bizPersonQualification);
        ExcelUtil<BizPersonQualification> util = new ExcelUtil<BizPersonQualification>(BizPersonQualification.class);
        return util.exportExcel(list, "任职资格数据");
    }

    /**
     * 新增任职资格
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存任职资格
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "任职资格", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "任职资格", tableName = PersonTable.BIZ_PERSON_QUALIFICATION, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonQualification bizPersonQualification)
    {
        return toAjax(bizPersonQualificationService.insertBizPersonQualification(bizPersonQualification));
    }

    /**
     * 修改任职资格
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonQualification bizPersonQualification = bizPersonQualificationService.selectBizPersonQualificationById(id);
        mmap.put("bizPersonQualification", bizPersonQualification);
        return prefix + "/edit";
    }

    /**
     * 修改保存任职资格
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "任职资格", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "任职资格", tableName = PersonTable.BIZ_PERSON_QUALIFICATION, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonQualification bizPersonQualification)
    {
        return toAjax(bizPersonQualificationService.updateBizPersonQualification(bizPersonQualification));
    }

    /**
     * 删除任职资格
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "任职资格", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "任职资格", tableName = PersonTable.BIZ_PERSON_QUALIFICATION, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonQualificationService.deleteBizPersonQualificationByIds(ids));
    }

    /**
     * 排序任职资格
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "任职资格", businessType = BusinessType.UPDATE)
    @PostMapping( "/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids)
    {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++)
        {
            BizPersonQualification bizPersonQualification = bizPersonQualificationService.selectBizPersonQualificationById(idsArray[i]);
            bizPersonQualification.setOrderNum(Convert.toLong(i + 1));
            bizPersonQualificationService.updateBizPersonQualification(bizPersonQualification);
        }
        return success();
    }

    private long getMaxOrderNum(String personId)
    {
        BizPersonQualification bizPersonQualification = new BizPersonQualification();
        bizPersonQualification.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonQualification> list = bizPersonQualificationService.selectBizPersonQualificationList(bizPersonQualification);
        if(!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
