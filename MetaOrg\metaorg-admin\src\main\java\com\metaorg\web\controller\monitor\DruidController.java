package com.metaorg.web.controller.monitor;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import com.metaorg.common.core.controller.BaseController;

/**
 * druid 监控
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/monitor/data")
public class DruidController extends BaseController
{
//    private String prefix = "/druid";
//
//    @RequiresPermissions("monitor:data:view")
//    @GetMapping()
//    public String index()
//    {
//        return redirect(prefix + "/index.html");
//    }
}
