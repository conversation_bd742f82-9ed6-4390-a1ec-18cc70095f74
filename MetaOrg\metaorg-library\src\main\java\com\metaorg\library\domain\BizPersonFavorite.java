package com.metaorg.library.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.metaorg.common.annotation.Excel;
import com.metaorg.common.core.domain.BaseEntity;

/**
 * 人员收藏关系对象 biz_person_favorite
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class BizPersonFavorite extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 人员ID */
    @Excel(name = "人员ID")
    private String personId;

    /** 收藏用户ID */
    @Excel(name = "收藏用户ID")
    private Long userId;

    /** 人员信息 */
    private BizPerson person;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setPersonId(String personId)
    {
        this.personId = personId;
    }

    public String getPersonId()
    {
        return personId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public BizPerson getPerson()
    {
        return person;
    }

    public void setPerson(Biz<PERSON>erson person)
    {
        this.person = person;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("personId", getPersonId())
                .append("userId", getUserId())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
} 