/*
Navicat MySQL Data Transfer

Source Server         : org.metavun.com
Source Server Version : 50719
Source Host           : org.metavun.com:15013
Source Database       : metaorg1

Target Server Type    : MYSQL
Target Server Version : 50719
File Encoding         : 65001

Date: 2023-06-19 13:42:48
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for biz_person_reward_punish
-- ----------------------------
DROP TABLE IF EXISTS `biz_person_reward_punish`;
CREATE TABLE `biz_person_reward_punish` (
  `id` varchar(254) NOT NULL COMMENT 'ID',
  `person_id` varchar(254) DEFAULT NULL COMMENT '人员ID',
  `order_num` int(11) DEFAULT '0' COMMENT '序号',
  `reward_punish_category` varchar(254) DEFAULT '' COMMENT '奖惩类别',
  `show_in_appoint` varchar(254) DEFAULT '' COMMENT '任免表中是否显示',
  `reward_punish_name` varchar(255) DEFAULT '' COMMENT '名称',
  `reward_punish_org` varchar(255) DEFAULT '' COMMENT '批准机关',
  `reward_punish_day` varchar(255) DEFAULT '' COMMENT '批准时间',
  `reward_punish_detail` varchar(255) DEFAULT '' COMMENT '详情',
  `reward_punish_reason` varchar(255) DEFAULT '' COMMENT '原因',
  `reward_punish_note` varchar(255) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖惩情况';
SET FOREIGN_KEY_CHECKS=1;
