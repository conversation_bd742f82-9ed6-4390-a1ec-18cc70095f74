package com.metaorg.web.controller.library;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.exception.ServiceException;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.common.utils.uuid.Seq;
import com.metaorg.library.domain.*;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.utils.DataScopeUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import com.metaorg.library.service.IBizStatisticPositionAdjustService;
import com.metaorg.library.service.IBizPersonService;

/**
 * 干部职务调整统计Controller
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/position_adjust")
public class BizPersonPositionAdjustController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(BizPersonPositionAdjustController.class);

    private String prefix = "library/position_adjust";

    @Autowired
    private IBizStatisticPositionAdjustService bizStatisticPositionAdjustService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IBizPersonService bizPersonService;

    @RequiresPermissions("library:position_adjust_statistic:view")
    @GetMapping("position_adjust_statistic")
    public String positionAdjustStatistic(@RequestParam Map<String, Object> params, ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }  
        
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                mmap.put(entry.getKey(), entry.getValue());
            }
        }

        return prefix + "/position_adjust_statistic";
    }

    @RequiresPermissions("library:position_adjust_proposed:view")
    @GetMapping("position_adjust_proposed")
    public String positionAdjustProposed(@RequestParam Map<String, Object> params, ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                mmap.put(entry.getKey(), entry.getValue());
            }
        }
        
        return prefix + "/position_adjust_proposed";
    }

    /**
     * 获取干部调整统计列表查询条件
     */
    private void getPositionAdjustStatisticQueryOptions(BizStatisticPositionAdjust bizStatisticPositionAdjust, Map<String, Object> paramMap) {
        long deptId = -1;

        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizStatisticPositionAdjust.getParams().put("deptIds", Arrays.asList(deptId));
        }
        
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizStatisticPositionAdjust, getSysUser(), "dh", "", "", getUserDeptId());
        }
    }

    /**
     * 获取干部拟调整列表查询条件
     */
    private void getPositionAdjustProposedQueryOptions(BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed, Map<String, Object> paramMap) {
        long deptId = -1;

        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonPositionAdjustProposed.getParams().put("deptIds", Arrays.asList(deptId));
        }

        if (paramMap.get("adjustTypeList") != null && paramMap.get("adjustTypeList") != "") {
            bizPersonPositionAdjustProposed.getParams().put("adjustTypeList", Convert.toStrArray(paramMap.get("adjustTypeList").toString()));
        }
        
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonPositionAdjustProposed, getSysUser(), "dp", "", "", getUserDeptId());
        }
    }

    /**
     * 查询干部职务调整统计列表
     */
    @RequiresPermissions("library:position_adjust_statistic:list")
    @PostMapping("/statistic/list")
    @ResponseBody
    public TableDataInfo positionAdjustStatisticList(BizStatisticPositionAdjust bizStatisticPositionAdjust, @RequestParam Map<String, Object> paramMap) {
        getPositionAdjustStatisticQueryOptions(bizStatisticPositionAdjust, paramMap);
        startPage();
        List<BizStatisticPositionAdjust> list = bizStatisticPositionAdjustService.selectBizStatisticPositionAdjust(bizStatisticPositionAdjust);
        return getDataTable(list);
    }

    /**
     * 查询干部拟调整列表
     */
    @RequiresPermissions("library:position_adjust_proposed:list")
    @PostMapping("/proposed/list")
    @ResponseBody
    public TableDataInfo positionAdjustProposedList(BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed, @RequestParam Map<String, Object> paramMap) {
        getPositionAdjustProposedQueryOptions(bizPersonPositionAdjustProposed, paramMap);
        startPage();
        List<BizPersonPositionAdjustProposed> list = bizStatisticPositionAdjustService.selectBizPersonPositionAdjustProposed(bizPersonPositionAdjustProposed);
        return getDataTable(list);
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

    /**
    * 下载干部职务调整统计模板
    */
    @GetMapping("/statistic/template")
    @ResponseBody
    public AjaxResult downloadPositionAdjustStatisticTemplate()
    {
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String fileName =  "干部调整情况统计导入模板.xls";
        try {
            File file = new File("./config/template/position_adjust_statistic_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            out = new FileOutputStream(getAbsoluteFile(fileName));
            hssfWorkbook.write(out);
            hssfWorkbook.close();
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("导出Excel模板异常{}", e.getMessage());
            throw new UtilException("导出Excel模板失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
    * 下载干部职务拟调整详情模板
    */
    @GetMapping("/proposed/template")
    @ResponseBody
    public AjaxResult downloadPositionAdjustProposedTemplate()
    {
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String fileName = "干部职务拟调整详情导入模板.xls";
        try {
            File file = new File("./config/template/position_adjust_proposed_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            out = new FileOutputStream(getAbsoluteFile(fileName));
            hssfWorkbook.write(out);
            hssfWorkbook.close();
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("导出Excel模板异常{}", e.getMessage());
            throw new UtilException("导出Excel模板失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导入干部职务调整统计数据
     */
    @Log(title = "干部职务调整统计", businessType = BusinessType.IMPORT)
    @RequiresPermissions("library:position_adjust_statistic:add")
    @PostMapping("/statistic/import")
    @ResponseBody
    public AjaxResult importPositionAdjustStatistic(MultipartFile file) throws Exception
    {
        ExcelUtil<BizStatisticPositionAdjustTplVM> util = new ExcelUtil<>(BizStatisticPositionAdjustTplVM.class);
        List<BizStatisticPositionAdjustTplVM> adjustList = util.importExcel(file.getInputStream(), 1);
        String message = importPositionAdjustStatisticData(adjustList);
        return AjaxResult.success(message);
    }

    /**
     * 导入干部职务拟调整详情数据
     */
    @Log(title = "干部职务拟调整详情", businessType = BusinessType.IMPORT)
    @RequiresPermissions("library:position_adjust_proposed:add")
    @PostMapping("/proposed/import")
    @ResponseBody
    public AjaxResult importPositionAdjustProposed(MultipartFile file) throws Exception
    {
        ExcelUtil<BizPersonPositionAdjustProposedTplVM> util = new ExcelUtil<>(BizPersonPositionAdjustProposedTplVM.class);
        List<BizPersonPositionAdjustProposedTplVM> adjustList = util.importExcel(file.getInputStream(), 1);
        String message = importPositionAdjustProposedData(adjustList);
        return AjaxResult.success(message);
    }

    /**
     * 删除干部职务调整统计
     */
    @RequiresPermissions(value = { "library:position_adjust_statistic:remove" }, logical = Logical.OR)
    @Log(title = "干部职务调整统计", businessType = BusinessType.DELETE)
    @PostMapping("statistic/remove")
    @ResponseBody
    public AjaxResult removePositionAdjustStatistic(String ids) {
        String[] deleteIds = Convert.toStrArray(ids);
        if (deleteIds.length == 0) {
            return error("请选择要删除的记录！");
        }
        BizStatisticPositionAdjust bizStatisticPositionAdjust = new BizStatisticPositionAdjust();
        bizStatisticPositionAdjust.getParams().put("ids", deleteIds);
        return toAjax(bizStatisticPositionAdjustService.deleteBizStatisticPositionAdjust(bizStatisticPositionAdjust));
    }

    /**
     * 删除干部职务拟调整详情
     */
    @RequiresPermissions(value = { "library:position_adjust_proposed:remove" }, logical = Logical.OR)
    @Log(title = "干部职务拟调整详情", businessType = BusinessType.DELETE)
    @PostMapping("/proposed/remove")
    @ResponseBody
    public AjaxResult removePositionAdjustProposed(String ids) {
        String[] deleteIds = Convert.toStrArray(ids);
        if (deleteIds.length == 0) {
            return error("请选择要删除的记录！");
        }

        BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed = new BizPersonPositionAdjustProposed();
        bizPersonPositionAdjustProposed.getParams().put("ids", deleteIds);
        return toAjax(bizStatisticPositionAdjustService.deleteBizPersonPositionAdjustProposed(bizPersonPositionAdjustProposed));
    }

    private String importPositionAdjustStatisticData(List<BizStatisticPositionAdjustTplVM> adjustList){
        if (StringUtils.isNull(adjustList) || adjustList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 导入数据检查
        Boolean isValid = checkBizStatisticPositionAdjustImportData(adjustList);

        if (!isValid) { 
            for (BizStatisticPositionAdjustTplVM pvm : adjustList) {
                if (StringUtils.isNotEmpty(pvm.getErrMsg())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
            } 
            if (failureNum > 0)
            {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            }
        }

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        String batchNo = "DR-" + Seq.getId();
        for (BizStatisticPositionAdjustTplVM pvm : adjustList) {
            pvm.setBatchNo(batchNo);
            
            try {
                BizStatisticPositionAdjust deletePositionAdjustStatistic = new BizStatisticPositionAdjust();
                
                deletePositionAdjustStatistic.setDeptId(pvm.getDept().getDeptId());
                deletePositionAdjustStatistic.setStatisticDate(pvm.getStatisticDate());
                bizStatisticPositionAdjustService.deleteBizStatisticPositionAdjust(deletePositionAdjustStatistic);

                BizStatisticPositionAdjust positionAdjust = BizStatisticPositionAdjust.fromBizStatisticPositionAdjustTplVM(pvm);

                positionAdjust.setCreateById(createUserId);
                positionAdjust.setCreateBy(createUserName);
                positionAdjust.setUpdateById(createUserId);
                positionAdjust.setUpdateBy(createUserName);
                positionAdjust.setCreateTime(currentDate);
                positionAdjust.setUpdateTime(currentDate);
                
                int result = bizStatisticPositionAdjustService.batchInsertBizStatisticPositionAdjust(Arrays.asList(positionAdjust));
                if (result > 0) {
                    successNum++;
                } else {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、时间：" + pvm.getStatisticDate()  + "导入失败：";
                    failureMsg.append(msg + "数据入库失败");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、时间：" + pvm.getStatisticDate()  + "导入失败：";
                String exMsg = e.getMessage();
                if(StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException){
                    exMsg = ((InvocationTargetException)e).getTargetException().getMessage();
                }
                if(StringUtils.isEmpty(exMsg)){
                    exMsg = "数据入库失败";
                }
                failureMsg.append(msg + exMsg);
            }
        }


        if (failureNum > 0)
        {
            if (!isValid) {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            } else {
                failureMsg.insert(0, "总共：" + adjustList.size() + "，成功：" + successNum + "，失败：" + failureNum + "，错误如下：");
            }
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条。");
        }


        return successMsg.toString();
    }

    private Boolean checkBizStatisticPositionAdjustImportData(List<BizStatisticPositionAdjustTplVM> checkList) {
        Boolean isOk = true;

        HashMap<String, SysDept> deptMap = new HashMap<>();
        SysDept sysDeptSearch = new SysDept();

        HashSet<String> deptNameSet = new HashSet<>();
        for (BizStatisticPositionAdjustTplVM pvm : checkList) {
            if (StringUtils.isEmpty(pvm.getDeptName())) {
                continue;
            }
            String deptName = pvm.getDeptName().trim();
            deptNameSet.add(deptName);
            pvm.setDeptName(deptName);
        }

        if (deptNameSet.size() > 0) {
           sysDeptSearch.getParams().put("deptNames", deptNameSet.toArray());
            List<SysDept> deptList = deptService.selectDeptList(sysDeptSearch);
            for (SysDept dept : deptList) {
                deptMap.put(dept.getDeptName().trim(), dept);
            }
        }

        for (BizStatisticPositionAdjustTplVM checkTplVM : checkList) {
            String preMsg = "时间：" + checkTplVM.getStatisticDate() + " ";

            String deptName = checkTplVM.getDeptName();

            try {
                // 判断是否为空
                ArrayList<String> emptyMsgList = new ArrayList<>();

                if (StringUtils.isEmpty(deptName))
                    emptyMsgList.add("机构名称");

                if (StringUtils.isEmpty(checkTplVM.getStatisticDate()))
                    emptyMsgList.add("时间");

                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }

                // 检查机构名称
                if (!deptMap.containsKey(deptName)) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "机构名称'" + deptName + "'不存在，请检查机构名称");
                    continue;
                }
                checkTplVM.setDept(deptMap.get(deptName));

                // 时间格式校验
                Date statisticDate = DateUtils.parseDate(checkTplVM.getStatisticDate());
                if (statisticDate == null) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "日期格式不正确，正确示例: 2025-01-01");
                    continue;
                }

                checkTplVM.setStatisticDate(DateUtils.parseDateToStr("yyyy-MM-dd", statisticDate));

                // 判断adjustDetails中的值是否为负数
                Map<String, Integer> adjustDetails = checkTplVM.getAdjustDetails();
                for (Map.Entry<String, Integer> entry : adjustDetails.entrySet()) {
                    String columnName = entry.getKey();
                    Integer value = entry.getValue();
                    if (value < 0) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "机构名称：" + deptName + "，时间：" + checkTplVM.getStatisticDate() + "，[" + columnName + "]列不能为负数");
                    }
                }
            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if (StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException) {
                    exMsg = ((InvocationTargetException) e).getTargetException().getMessage();
                }
                if (StringUtils.isEmpty(exMsg)) {
                    exMsg = "数据校验失败";
                }
                checkTplVM.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }

    private String importPositionAdjustProposedData(List<BizPersonPositionAdjustProposedTplVM> adjustList){
        if (StringUtils.isNull(adjustList) || adjustList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 导入数据检查
        Boolean isValid = checkBizPersonPositionAdjustProposedImportData(adjustList);

        if (!isValid) { 
            for (BizPersonPositionAdjustProposedTplVM pvm : adjustList) {
                if (StringUtils.isNotEmpty(pvm.getErrMsg())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
            } 
            if (failureNum > 0)
            {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            }
        }

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        String batchNo = "DR-" + Seq.getId();
        for (BizPersonPositionAdjustProposedTplVM pvm : adjustList) {
            pvm.setBatchNo(batchNo);
            
            try {
                BizPersonPositionAdjustProposed deletePersonPositionAdjustProposed = new BizPersonPositionAdjustProposed();
                
                deletePersonPositionAdjustProposed.setCitizenId(pvm.getCitizenId());
                deletePersonPositionAdjustProposed.setMeetingTime(pvm.getMeetingTime());
                bizStatisticPositionAdjustService.deleteBizPersonPositionAdjustProposed(deletePersonPositionAdjustProposed);

                BizPersonPositionAdjustProposed positionAdjust = BizPersonPositionAdjustProposed.fromBizPersonPositionAdjustProposedTplVM(pvm);

                positionAdjust.setCreateById(createUserId);
                positionAdjust.setCreateBy(createUserName);
                positionAdjust.setUpdateById(createUserId);
                positionAdjust.setUpdateBy(createUserName);
                positionAdjust.setCreateTime(currentDate);
                positionAdjust.setUpdateTime(currentDate);
                
                int result = bizStatisticPositionAdjustService.batchInsertBizPersonPositionAdjustProposed(Arrays.asList(positionAdjust));
                if (result > 0) {
                    successNum++;
                } else {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、时间：" + pvm.getMeetingTime()  + "导入失败：";
                    failureMsg.append(msg + "数据入库失败");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、时间：" + pvm.getMeetingTime()  + "导入失败：";
                String exMsg = e.getMessage();
                if(StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException){
                    exMsg = ((InvocationTargetException)e).getTargetException().getMessage();
                }
                if(StringUtils.isEmpty(exMsg)){
                    exMsg = "数据入库失败";
                }
                failureMsg.append(msg + exMsg);
            }
        }


        if (failureNum > 0)
        {
            if (!isValid) {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            } else {
                failureMsg.insert(0, "总共：" + adjustList.size() + "，成功：" + successNum + "，失败：" + failureNum + "，错误如下：");
            }
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条。");
        }


        return successMsg.toString();
    }

    private Boolean checkBizPersonPositionAdjustProposedImportData(List<BizPersonPositionAdjustProposedTplVM> checkList) {
        Boolean isOk = true;

        // 根据身份证获取人员信息
        HashMap<String, BizPerson> personMap = new HashMap<>();
        BizPerson bizPersonSearch = new BizPerson();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonSearch, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }

        HashSet<String> citizenIdSet = new HashSet<>();
        HashSet<String> deptNameSet = new HashSet<>();

        for (BizPersonPositionAdjustProposedTplVM pvm : checkList) {
            if (StringUtils.isNotEmpty(pvm.getCitizenId())) {
                String citizenId = pvm.getCitizenId().trim();
                citizenIdSet.add(citizenId);
                pvm.setCitizenId(citizenId);
            }

            if (StringUtils.isEmpty(pvm.getOriginalDeptName()) && StringUtils.isEmpty(pvm.getNewDeptName())) {
                continue;
            }

            String originalDeptName = pvm.getOriginalDeptName().trim();
            String newDeptName = pvm.getNewDeptName().trim();
            deptNameSet.add(originalDeptName);
            deptNameSet.add(newDeptName);
            pvm.setOriginalDeptName(originalDeptName);
            pvm.setNewDeptName(newDeptName);
        }

        if (citizenIdSet.size() > 0) {
            bizPersonSearch.getParams().put("citizenIds", citizenIdSet.toArray());
            List<BizPerson> personList = bizPersonService.selectBizPersonListByCitizenIds(bizPersonSearch);
            for (BizPerson person : personList) {
                personMap.put(person.getCitizenId().trim(), person);
            }
        }

        HashMap<String, SysDept> deptMap = new HashMap<>();
        
        if (deptNameSet.size() > 0) {
           SysDept sysDeptSearch = new SysDept();
           sysDeptSearch.getParams().put("deptNames", deptNameSet.toArray());
            List<SysDept> deptList = deptService.selectDeptList(sysDeptSearch);
            for (SysDept dept : deptList) {
                deptMap.put(dept.getDeptName().trim(), dept);
            }
        }

        HashSet<String> checkBooleanSet = new HashSet<>();
        checkBooleanSet.add("是");
        checkBooleanSet.add("否");

        int rowIndex = 0;
        for (BizPersonPositionAdjustProposedTplVM checkTplVM : checkList) {
            rowIndex++;

            String citizenId = checkTplVM.getCitizenId();
            String preMsg = "第" + rowIndex + "行数据：" + citizenId + " ";

            String originalDeptName = checkTplVM.getOriginalDeptName();
            String newDeptName = checkTplVM.getNewDeptName();

            try {
                // 判断是否为空
                ArrayList<String> emptyMsgList = new ArrayList<>();

                if (StringUtils.isEmpty(citizenId))
                    emptyMsgList.add("身份证号码");

                if (StringUtils.isEmpty(checkTplVM.getMeetingTime()))
                    emptyMsgList.add("党委会时间");

                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }

                // 检查身份证号码
                if (!personMap.containsKey(checkTplVM.getCitizenId())) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "身份证号码'" + checkTplVM.getCitizenId() + "'不存在，请检查身份证号码");
                    continue;
                }
                checkTplVM.setPerson(personMap.get(checkTplVM.getCitizenId()));

                // 检查原机构名称
                if (StringUtils.isNotEmpty(originalDeptName) && !deptMap.containsKey(originalDeptName)) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "调整前机构名称'" + originalDeptName + "'不存在，请检查机构名称");
                    continue;
                }
                checkTplVM.setOriginalDept(deptMap.get(originalDeptName));

                // 检查新机构名称
                if (StringUtils.isNotEmpty(newDeptName) && !deptMap.containsKey(newDeptName)) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "调整后机构名称'" + newDeptName + "'不存在，请检查机构名称");
                    continue;
                }
                checkTplVM.setNewDept(deptMap.get(newDeptName));

                // 时间格式校验
                Date statisticDate = DateUtils.parseDate(checkTplVM.getMeetingTime());
                if (statisticDate == null) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "日期格式不正确，正确示例: 2025-01-01");
                    continue;
                }

                checkTplVM.setMeetingTime(DateUtils.parseDateToStr("yyyy-MM-dd", statisticDate));

                // 调整前是否班子格式校验
                if (StringUtils.isNotEmpty(checkTplVM.getOriginalIsLeadingGroup())) {
                    if (!checkBooleanSet.contains(checkTplVM.getOriginalIsLeadingGroup().trim())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "调整前是否班子格式不正确，应为以下值之一：" + String.join(",", checkBooleanSet));
                        continue;
                    }
                    checkTplVM.setOriginalIsLeadingGroup(checkTplVM.getOriginalIsLeadingGroup().trim());
                }

                // 调整后是否班子格式校验
                if (StringUtils.isNotEmpty(checkTplVM.getNewIsLeadingGroup())) {
                    if (!checkBooleanSet.contains(checkTplVM.getNewIsLeadingGroup().trim())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "调整后是否班子格式不正确，应为以下值之一：" + String.join(",", checkBooleanSet));
                        continue;
                    }
                    checkTplVM.setNewIsLeadingGroup(checkTplVM.getNewIsLeadingGroup().trim());
                }

                // 是否一把手调整格式校验
                if (StringUtils.isNotEmpty(checkTplVM.getIsAdjustTopLeader())) {
                    if (!checkBooleanSet.contains(checkTplVM.getIsAdjustTopLeader().trim())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "是否一把手调整格式不正确，应为以下值之一：" + String.join(",", checkBooleanSet));
                        continue;
                    }
                    checkTplVM.setIsAdjustTopLeader(checkTplVM.getIsAdjustTopLeader().trim());
                }

                // 去掉空格
                Map<String, String> adjustDetails = checkTplVM.getAdjustDetails();
                for (Map.Entry<String, String> entry : adjustDetails.entrySet()) {
                    String value = entry.getValue();
                    if (StringUtils.isNotEmpty(value)) {
                        entry.setValue(value.trim());
                    }
                }
            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if (StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException) {
                    exMsg = ((InvocationTargetException) e).getTargetException().getMessage();
                }
                if (StringUtils.isEmpty(exMsg)) {
                    exMsg = "数据校验失败";
                }
                checkTplVM.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }
}
