package com.metaorg.library.service;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.metaorg.library.domain.BizPersonFavorite;

/**
 * 用户 业务层
 * 
 * <AUTHOR>
 */
public interface IBizPersonFavoriteService
{
    /**
     * 查询用户收藏
     * 
     * @param bizPersonFavorite 用户收藏信息
     * @return 用户收藏
     */
    public List<BizPersonFavorite> selectBizPersonFavoriteList(BizPersonFavorite bizPersonFavorite);

    /**
     * 通过用户ID查询用户收藏
     * 
     * @param userId 用户ID
     * @param personId 人员ID
     * @return 用户收藏列表
     */
    public List<BizPersonFavorite> selectBizPersonFavoriteByUserId(Long userId, String personId);

     /**
     * 通过人员ID查询用户收藏
     * 
     * @param personId 人员ID
     * @param userId 用户ID
     * @return 用户收藏列表
     */
    public List<BizPersonFavorite> selectBizPersonFavoriteByPersonId(String personId, Long userId);

    /**
     * 批量新增用户收藏信息
     * 
     * @param bizPersonFavoriteList 用户收藏列表
     */
    public int insertBizPersonFavorite(Long userId, String[] personIds);

    /**
     * 通过用户ID删除用户收藏
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteBizPersonFavoriteByUserId(Long userId, String[] personIds);

    
    /**
     * 删除用户收藏
     * 
     * @param bizPersonFavorite 用户收藏信息
     * @return 结果
     */
    public int deleteBizPersonFavoriteInfo(BizPersonFavorite bizPersonFavorite);

    /**
     * 通过人员ID和用户ID删除用户收藏
     * 
     * @param personId 人员ID
     * @param userIds 用户ID
     * @return 结果
     */
    public int deleteBizPersonFavoriteInfos(@Param("personId") String personId, @Param("userIds") Long[] userIds);

    /**
     * 通过用户ID和ID列表删除用户收藏
     * 
     * @param userId 用户ID
     * @param ids ID列表
     * @return 结果
     */
    public int deleteBizPersonFavoriteByIds(Long userId, String[] ids);
}
