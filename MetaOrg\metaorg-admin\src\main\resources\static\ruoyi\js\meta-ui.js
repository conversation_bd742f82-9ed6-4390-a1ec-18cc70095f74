(function ($) {
    $.extend({
        metaModal: {
            // 弹出层指定参数选项
            openOptions: function (options) {
                var _url = $.common.isEmpty(options.url) ? "/404.html" : options.url;
                var _title = $.common.isEmpty(options.title) ? "系统窗口" : options.title;
                var _width = $.common.isEmpty(options.width) ? "800" : options.width;
                var _height = $.common.isEmpty(options.height) ? ($(window).height() - 50) : options.height;
                var _btn = ['<i class="fa fa-check"></i> 确认', '<i class="fa fa-close"></i> 关闭'];
                // 如果是移动端，就使用自适应大小弹窗
                if ($.common.isMobile()) {
                    _width = 'auto';
                    _height = 'auto';
                }
                if ($.common.isEmpty(options.yes)) {
                    options.yes = function(index, layero) {
                        var iframeWin = layero.find('iframe')[0];
                        iframeWin.contentWindow.submitHandler(index, layero);
                        options.callBack(index, layero);
                    }
                }
                var btnCallback = {};
                if (options.btn instanceof Array){
                    for (var i = 1, len = options.btn.length; i < len; i++) {
                        var btn = options["btn" + (i + 1)];
                        if (btn) {
                            btnCallback["btn" + (i + 1)] = btn;
                        }
                    }
                }
                var index = top.layer.open($.extend({
                    id: options.id,       // 唯一id
                    anim: options.anim,   // 弹出动画 0-6
                    type: 2,
                    maxmin: $.common.isEmpty(options.maxmin) ? true : options.maxmin,
                    shade: 0.3,
                    title: _title,
                    fix: false,
                    area: [_width + 'px', _height + 'px'],
                    content: _url,
                    shadeClose: $.common.isEmpty(options.shadeClose) ? true : options.shadeClose,
                    skin: options.skin,
                    // options.btn设置为0表示不显示按钮
                    btn: $.common.isEmpty(options.btn) ? _btn : options.btn,
                    yes: options.yes,
                    cancel: function () {
                        return true;
                    },
                    success: function () {
                        $(':focus').blur();
                    }
                }, btnCallback));
                if ($.common.isNotEmpty(options.full) && options.full === true) {
                    top.layer.full(index);
                }
            },
        },
        // 操作封装处理
        metaOperate: {
            // 添加信息
            add: function(id, width, height) {
                table.set();
                $.modal.open("添加" + table.options.modalName, $.operate.addUrl(id), width, height);
            },
            // 修改信息
            edit: function(id, width, height) {
                table.set();
                if ($.common.isEmpty(id) && table.options.type == table_type.bootstrapTreeTable) {
                    var row = $("#" + table.options.id).bootstrapTreeTable('getSelections')[0];
                    if ($.common.isEmpty(row)) {
                        $.modal.alertWarning("请至少选择一条记录");
                        return;
                    }
                    var url = table.options.updateUrl.replace("{id}", row[table.options.uniqueId]);
                    $.modal.open("修改" + table.options.modalName, url, width, height);
                } else {
                    $.modal.open("修改" + table.options.modalName, $.operate.editUrl(id), width, height);
                }
            },
            // 排序信息
            resortItem: function(data, column) {
                table.set();
                var rows = $.map(data, function (row) {
                    return $.common.getItemField(row, column);
                });
                $.modal.confirm("是否保存排序信息吗?", function() {
                    var url = table.options.resortItemUrl;
                    var data = { "ids": rows.join() };
                    $.operate.submit(url, "post", "json", data);
                });
            },

            // 修改信息，以tab页展现
            editTab: function(title, id) {
                table.set();
                $.modal.openTab(title, $.operate.editUrl(id));
            },
            // 详细信息，以tab页展现
            detailTab: function(title, id) {
                table.set();
                $.modal.openTab(title, $.operate.detailUrl(id));
            },
            // 恢复信息
            revert: function(id) {
                table.set();
                $.modal.confirm("确定恢复该条" + table.options.modalName + "信息吗？", function() {
                    var url = $.common.isEmpty(id) ? table.options.revertUrl : table.options.revertUrl.replace("{id}", id);
                    if (table.options.type == table_type.bootstrapTreeTable) {
                        $.operate.get(url);
                    } else {
                        var data = { "ids": id };
                        $.operate.submit(url, "post", "json", data);
                    }
                });
            },
            // 恢复信息
            revert: function(id) {
                table.set();
                $.modal.confirm("确定恢复该条" + table.options.modalName + "信息吗？", function() {
                    var url = $.common.isEmpty(id) ? table.options.revertUrl : table.options.revertUrl.replace("{id}", id);
                    if (table.options.type == table_type.bootstrapTreeTable) {
                        $.operate.get(url);
                    } else {
                        var data = { "ids": id };
                        $.operate.submit(url, "post", "json", data);
                    }
                });
            },
            // 删除信息
            delete: function(id) {
                table.set();
                $.modal.confirm("确定彻底删除该条" + table.options.modalName + "信息吗？", function() {
                    var url = $.common.isEmpty(id) ? table.options.deleteUrl : table.options.deleteUrl.replace("{id}", id);
                    if (table.options.type == table_type.bootstrapTreeTable) {
                        $.operate.get(url);
                    } else {
                        var data = { "ids": id };
                        $.operate.submit(url, "post", "json", data);
                    }
                });
            }
        }
    });
})(jQuery);