<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <th:block th:include="include :: header('干部拟调整列表')" />
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: ztree-css"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .select-table .table td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
        #selectedPeopleModal .ui-layout-content{
            min-height:300px;
        }
        #selectedPeopleModal .fixed-table-container{
            max-height:380px;
        }
        .select-list li p, .select-list li label:not(.radio-box){
            width: 120px !important;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa fa-sitemap"></i> 组织机构
                </div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i
                            class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i
                            class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i
                            class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <input name="treeId" type="hidden" id="treeId" th:value="${deptId}">
                <div id="tree" class="ztree"></div>
            </div>
        </div>
    </div>
    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" id="deptId" name="deptId" th:value="${deptId}">
                        <input type="hidden" id="parentId" name="parentId">
                        <input type="hidden" id="adjustTypeList" name="adjustTypeList" th:value="${adjustTypeList}">
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>关键字：</label>
                                    <input type="text" name="params[keyword]" placeholder="请输入关键字，支持模糊查询" data-toggle="tooltip" data-placement="bottom" title="支持按姓名、身份证号码、现任职务、拟任职务、调整前机构、调整后机构等模糊查询"/>
                                </li>
                                <li>
                                  <label>调整前是否班子： </label>
                                  <select id="originalIsLeadingGroup" name="originalIsLeadingGroup" title="调整前是否班子">
                                    <option value="">所有</option>
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                  </select>
                                </li>
                                <li>
                                  <label>调整后是否班子： </label>
                                  <select id="newIsLeadingGroup" name="newIsLeadingGroup" title="调整后是否班子">
                                    <option value="">所有</option>
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                  </select>
                                </li>
                                <li>
                                  <label>是否一把手调整： </label>
                                  <select id="isAdjustTopLeader" name="isAdjustTopLeader" title="是否一把手调整">
                                    <option value="">所有</option>
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                  </select>
                                </li>
                                <li class="select-time">
                                    <label>党委会日期：</label>
                                    <input type="text" class="time-input" id="meetingBeginTime" data-type="date"
                                           placeholder="开始日期" name="params[meetingBeginTime]" title="开始日期" th:value="${meetingBeginTime}"/>
                                    <span>-</span>
                                    <input type="text" class="time-input" id="meetingEndTime" data-type="date"
                                           placeholder="结束日期" name="params[meetingEndTime]" title="结束日期" th:value="${meetingEndTime}"/>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="searchFormReset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="btn-group-sm" id="check-toolbar" role="group">
                    <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="library:position_adjust_statistic:remove">
                        <i class="fa fa-remove"></i> 删除
                    </a>
                    <a class="btn btn-info" onclick="$.table.importExcel('importTpl',400,250)" shiro:hasPermission="library:position_adjust_statistic:import">
                        <i class="fa fa-upload"></i> 导入
                    </a>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table" data-resizable="true" data-use-row-attr-func="true"></table>
                </div>
            </div>
        </div>
    </div>
    <!-- 导入区域 -->
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file"/>
                <div class="mt10 pt5">
                    <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
                </div>
                <font color="red" class="pull-left mt10">
                    提示：<br/>
                    1. 仅允许导入"xls"或"xlsx"格式文件！<br/>
                    2. 数据导入中，请耐心等待勿关闭本页面！
                </font>
            </div>
        </form>
    </script>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js"/>
    <th:block th:include="include :: ztree-js"/>
    <th:block th:include="include :: bootstrap-table-reorder-rows-js" />
    <th:block th:include="include :: bootstrap-table-resizable-js" />
    <th:block th:include="include :: bootstrap-table-fixed-columns-js" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:src="@{/js/moment.min.js}"></script>
    <script th:inline="javascript">
        /*<![CDATA[*/
        const removeFlag = /*[[${@permission.hasPermi('library:position_adjust_statistic:remove')}]]*/ false;
        const prefix = ctx + "library/position_adjust";
        const sysYesNo = /*[[${@dict.getType('sys_yes_no')}]]*/ [];
        const checkCategory = /*[[${@dict.getType('biz_person_check_category')}]]*/ [];
        const checkResult = /*[[${@dict.getType('biz_person_check_result')}]]*/ [];
       
        const serverMeetingBeginTime = /*[[${meetingBeginTime}]]*/ null;
        const serverMeetingEndTime = /*[[${meetingEndTime}]]*/ null;

        const meetingBeginTime = serverMeetingBeginTime || moment().subtract(1, 'year').format('YYYY-MM-DD');
        const meetingEndTime = serverMeetingEndTime || moment().format('YYYY-MM-DD');
        
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
            $("#meetingBeginTime").val(meetingBeginTime);
            $("#meetingEndTime").val(meetingEndTime);
            let panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 255 });
            // 回到顶部绑定
            if ($.fn.toTop !== undefined) {
                const opt = {
                    win:$('.ui-layout-center'),
                    doc:$('.ui-layout-center')
                };
                $('#scroll-up').toTop(opt);
            }

            $(document).on('keypress', function(e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    $.table.search();
                }
            });
        });

        $(function() {
            const options = {
                toolbar: "check-toolbar",
                url: prefix + "/proposed/list",
                importUrl: prefix + "/proposed/import",
                importTemplateUrl: prefix + "/proposed/template",
                removeUrl: prefix + "/proposed/remove",
                modalName: "干部调整情况统计",
                showToggle: false,
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    {
                        checkbox: true
                    },
                    {
                        field: 'id',
                        title: 'ID',
                        visible: false
                    },
                    {
                        field: 'personId',
                        title: '人员ID',
                        visible: false
                    },
                    {
                        field: 'meetingTime',
                        title: '党委会时间',
                        formatter: function(value, row, index) {
                            return moment(value).format('YYYY-MM-DD');
                        }
                    },
                    {
                        field: 'meetingPersonCount',
                        title: '上会研究人数'
                    },
                    {
                        field: 'personName',
                        title: '姓名'
                    },
                    {
                        field: 'currentPositionName',
                        title: '现任职务'
                    },
                    {
                        field: 'newPositionName',
                        title: '拟任免（聘/解聘）职务'
                    },
                    {
                        field: 'originalDeptName',
                        title: '调整前机构'
                    },
                    {
                        field: 'originalIsLeadingGroup',
                        title: '调整前是否班子',
                        formatter: function(value, row, index) {
                            return value ? '是' : '否';
                        }
                    },
                    {
                        field: 'newDeptName',
                        title: '调整后机构'
                    },
                    {
                        field: 'newIsLeadingGroup',
                        title: '调整后是否班子',
                        formatter: function(value, row, index) {
                            return value ? '是' : '否';
                        }
                    },
                    {
                        field: 'isAdjustTopLeader',
                        title: '是否一把手调整',
                        formatter: function(value, row, index) {
                            return value ? '是' : '否';
                        }
                    },
                    {
                        field: 'adjustTopLeaderDeptName',
                        title: '调整一把手涉及行社'
                    },
                    {
                        field: 'appointmentArchivingStatus',
                        title: '干部任免表归档情况'
                    },
                    {
                        field: 'remark',
                        title: '备注'
                    },
                    {
                        field: 'adjustDetails',
                        title: '调整详情',
                        formatter: function(value, row, index) {
                            if (!value|| value.length === 0) {
                                return '-';
                            }
                            const adjustList = value
                                .filter(item => item.value)
                                .map(item => {
                                    return `${item.name}: ${item.value}`;
                                });
                            return adjustList.join(', ');
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
            queryDeptTree();
        });
        
        // 组织树数据绑定
        function queryDeptTree() {
            const url = ctx + "system/user/deptTreeData";
            const options = {
                url: url,
                expandLevel: 1,
                onClick: zOnClick
            };
            $.tree.init(options);

            function zOnClick(event, treeId, treeNode) {
                $("#deptId").val(treeNode.id);
                $("#parentId").val(treeNode.pId);
                $.table.search();
            }
        }
        
        // 重置筛选条件
        function searchFormReset() {
            $("#formId")[0].reset();
            $("#meetingBeginTime").val(meetingBeginTime);
            $("#meetingEndTime").val(meetingEndTime);
            $(".selectpicker").val("");
            $(".selectpicker").selectpicker('refresh');
            $.table.search();
        }

        $('#btnExpand').click(function() {
            $._tree.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function() {
            $._tree.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        $('#btnRefresh').click(function() {
            queryDeptTree();
        });
        /*]]>*/
    </script>
</body>
</html>