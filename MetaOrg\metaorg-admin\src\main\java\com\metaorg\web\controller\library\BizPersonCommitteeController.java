package com.metaorg.web.controller.library;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonCommittee;
import com.metaorg.library.service.IBizPersonCommitteeService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 两代一委员Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/committee")
public class BizPersonCommitteeController extends BaseController {
    private String prefix = "library/committee";

    @Autowired
    private IBizPersonCommitteeService bizPersonCommitteeService;

    @GetMapping()
    public String committee() {
        return prefix + "/committee";
    }

    /**
     * 查询两代一委员列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonCommittee bizPersonCommittee) {
        startPage();
        List<BizPersonCommittee> list = bizPersonCommitteeService.selectBizPersonCommitteeList(bizPersonCommittee);
        return getDataTable(list);
    }

    /**
     * 导出两代一委员列表
     */
    @Log(title = "两代表一委员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonCommittee bizPersonCommittee) {
        List<BizPersonCommittee> list = bizPersonCommitteeService.selectBizPersonCommitteeList(bizPersonCommittee);
        ExcelUtil<BizPersonCommittee> util = new ExcelUtil<BizPersonCommittee>(BizPersonCommittee.class);
        return util.exportExcel(list, "两代一委员数据");
    }

    /**
     * 新增两代一委员
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap) {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存两代一委员
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "两代表一委员", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "两代表一委员", tableName = PersonTable.BIZ_PERSON_COMMITTEE, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonCommittee bizPersonCommittee) {
        return toAjax(bizPersonCommitteeService.insertBizPersonCommittee(bizPersonCommittee));
    }

    /**
     * 修改两代一委员
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPersonCommittee bizPersonCommittee = bizPersonCommitteeService.selectBizPersonCommitteeById(id);
        mmap.put("bizPersonCommittee", bizPersonCommittee);
        return prefix + "/edit";
    }

    /**
     * 修改保存两代一委员
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "两代表一委员", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "两代表一委员", tableName = PersonTable.BIZ_PERSON_COMMITTEE, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonCommittee bizPersonCommittee) {
        return toAjax(bizPersonCommitteeService.updateBizPersonCommittee(bizPersonCommittee));
    }

    /**
     * 删除两代一委员
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "两代表一委员", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "两代表一委员", tableName = PersonTable.BIZ_PERSON_COMMITTEE, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizPersonCommitteeService.deleteBizPersonCommitteeByIds(ids));
    }

    /**
     * 排序两代一委员
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "两代表一委员", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++) {
            BizPersonCommittee bizPersonCommittee = bizPersonCommitteeService.selectBizPersonCommitteeById(idsArray[i]);
            bizPersonCommittee.setOrderNum(Convert.toLong(i + 1));
            bizPersonCommitteeService.updateBizPersonCommittee(bizPersonCommittee);
        }
        return success();
    }

    private long getMaxOrderNum(String personId) {
        BizPersonCommittee bizPersonCommittee = new BizPersonCommittee();
        bizPersonCommittee.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonCommittee> list = bizPersonCommitteeService.selectBizPersonCommitteeList(bizPersonCommittee);
        if (!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
