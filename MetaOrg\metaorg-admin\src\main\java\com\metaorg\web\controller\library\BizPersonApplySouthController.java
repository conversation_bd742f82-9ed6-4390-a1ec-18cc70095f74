package com.metaorg.web.controller.library;

import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.library.domain.*;
import com.metaorg.library.service.*;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.utils.DataScopeUtils;
import com.metaorg.web.controller.tool.POIUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.shiro.authz.annotation.Logical;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 去南疆申请Controller
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Controller
@RequestMapping("/library/apply_south")
public class BizPersonApplySouthController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(BizPersonApplySouthController.class);
    private String prefix = "library/apply_south";

    @Autowired
    private IBizPersonApplySouth bizPersonApplySouthService;

    @Autowired
    private IBizPersonEducationService educationService;

    @Autowired
    private IBizPersonPoliticsService politicsService;

    @Autowired
    private IBizPersonResumeService resumeService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IBizPersonPositionService positionService;

    @RequiresPermissions("library:apply_south:view")
    @GetMapping()
    public String apply_south(ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        return prefix + "/apply_south";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizPersonApplySouth bizPersonApplySouth, Map<String, Object> paramMap) {
        long deptId = -1;
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonApplySouth.getParams().put("deptId", deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonApplySouth, getSysUser(), "d", "", "", deptId);
        }
    }

    /**
     * 查询去南疆申请列表
     */
    @RequiresPermissions(value = { "library:apply_south:list", "library:person:view" }, logical = Logical.OR)
    @PostMapping({ "/list/{personId}", "/list" })
    @ResponseBody
    public TableDataInfo list(@RequestParam Map<String, Object> paramMap, BizPersonApplySouth bizPersonApplySouth) {
        getQueryOptions(bizPersonApplySouth, paramMap);
        startPage();
        List<BizPersonApplySouth> list = bizPersonApplySouthService.selectBizPersonApplySouthList(bizPersonApplySouth);
        return getDataTable(list);
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

    /**
     * 导出去南疆申请列表
     */
    @RequiresPermissions("library:apply_south:export")
    @Log(title = "去南疆申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestParam Map<String, Object> paramMap, BizPersonApplySouth bizPersonApplySouth,
            HttpServletResponse resp) {
        getQueryOptions(bizPersonApplySouth, paramMap);
        bizPersonApplySouth.getParams().put("orderBy", "apply_date desc,a.id desc");
        List<BizPersonApplySouth> list = bizPersonApplySouthService.selectBizPersonApplySouthList(bizPersonApplySouth);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "去南疆申请_" + System.currentTimeMillis() + ".xls";
        try {
            List<BizPerson> personList = new ArrayList<>();
            for (BizPersonApplySouth pas : list) {
                personList.add(pas.getPerson());
            }
            positionService.fillPositionForPersons(personList);

            File file = new File("./config/template/qnjsq_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            for (int i = 0; i < list.size(); i++) {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                row.getCell(0).setCellValue(i + 1); // 序号
                row.getCell(1).setCellValue(list.get(i).getPerson().getDept().getDeptName()); // 组织名称
                row.getCell(2).setCellValue(list.get(i).getPerson().getJobUnit()); // 职务
                row.getCell(3).setCellValue(list.get(i).getPerson().getName()); // 姓名
                row.getCell(4).setCellValue(list.get(i).getPerson().getSex()); // 性别
                row.getCell(5).setCellValue(list.get(i).getPerson().getNation()); // 民族
                row.getCell(6).setCellValue(list.get(i).getPerson().getBirthday() == null ? ""
                        : list.get(i).getPerson().getBirthday().replace("-", ".").replace("Invalid date", "")); // 出生年月
                row.getCell(7).setCellValue(list.get(i).getPerson().getAge().replace("岁", "")); // 年龄

                // 学历
                BizPersonEducation education = new BizPersonEducation(); // 学历对象
                education.setPersonId(list.get(i).getPersonId());
                List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);
                BizPersonEducation highestEducation = null; // 最高学历
                String ZuiGaoXueLi = ""; // 最高学历
                String ZuiGaoXueWei = ""; // 最高学位
                String ZuiGaoBiYeYuanXiao = ""; // 最高学校及院系
                String ZuiGaoZhuanYe = ""; // 最高专业
                String ZuiGaoBiYeShiJian = ""; // 毕业日期
                // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum)); //正序
                if (educations != null) {
                    for (BizPersonEducation item : educations) {
                        // 根据是否最高学历标识获取最高学历对象
                        if (item.getHighestDegreeFlag().equalsIgnoreCase("Y")) {
                            highestEducation = item;
                        }
                    }
                    // 如果没有标识最高学历获取最后一个
                    if (highestEducation == null && educations.size() > 0) {
                        highestEducation = educations.get(educations.size() - 1);
                    }
                    if (highestEducation != null) {
                        ZuiGaoBiYeYuanXiao = highestEducation.getEduSchool();
                        ZuiGaoBiYeShiJian = highestEducation.getGraduationDay().replace("-", ".")
                                .replace("Invalid date", "");
                        ZuiGaoXueLi = highestEducation.getEduName();
                        ZuiGaoZhuanYe = highestEducation.getMajorName();
                        ZuiGaoXueWei = highestEducation.getDegreeCode();
                    }
                }
                row.getCell(8).setCellValue(ZuiGaoXueLi); // 最高学历

                // 通过政治情况获取入党时间
                BizPersonPolitics politics = new BizPersonPolitics();
                politics.setPersonId(list.get(i).getPersonId());
                List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);
                BizPersonPolitics bizPersonPolitics = null;
                if (politicsList != null) {
                    bizPersonPolitics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
                        @Override
                        public boolean test(BizPersonPolitics bizPersonPolitics) {
                            return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
                        }
                    }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
                        @Override
                        public BizPersonPolitics get() {
                            return new BizPersonPolitics();
                        }
                    });
                }
                // 入党时间
                if (bizPersonPolitics != null && bizPersonPolitics.getJoinPartyDay() != null) {
                    row.getCell(9).setCellValue(
                            bizPersonPolitics.getJoinPartyDay().replace("-", ".").replace("Invalid date", ""));
                } else {
                    row.getCell(9).setCellValue("");
                }
                // 参加工作时间
                row.getCell(10).setCellValue(list.get(i).getPerson().getJobDay() == null ? ""
                        : list.get(i).getPerson().getJobDay().replace("-", ".").replace("Invalid date", ""));
                // 根据最新职职务级职务获取
                if (list.get(i).getPerson() != null && list.get(i).getPerson().getPositions() != null
                        && list.get(i).getPerson().getPositions().size() > 0) {
                    BizPersonPosition personPosition = positionService
                            .mergePositionsIntoSingle(list.get(i).getPerson().getPositions());
                    row.getCell(11).setCellValue(personPosition.getPositionDay().replace("-", "."));
                    row.getCell(12).setCellValue(personPosition.getSameRankDay().replace("-", "."));
                } else {
                    // 从简历中获取最新任职时间
                    BizPersonResume resume = new BizPersonResume();
                    resume.setPersonId(list.get(i).getPersonId());
                    List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume,
                            "start_date desc");
                    BizPersonResume lastResume = null;
                    if (resumes != null) {
                        lastResume = resumes.stream().findFirst().orElseGet(new Supplier<BizPersonResume>() {
                            @Override
                            public BizPersonResume get() {
                                return new BizPersonResume();
                            }
                        });
                    }
                    if (lastResume != null && lastResume.getStartDate() != null) {
                        row.getCell(11)
                                .setCellValue(lastResume.getStartDate().replace("-", ".").replace("Invalid date", ""));
                    } else {
                        row.getCell(11).setCellValue("");
                    }
                    row.getCell(12).setCellValue(""); // 任同职级时间
                }
                row.getCell(13).setCellValue(list.get(i).getPerson().getQualificationJob()); // 专业技术职称
                row.getCell(14).setCellValue(ZuiGaoBiYeShiJian); // 毕业时间
                row.getCell(15).setCellValue(ZuiGaoBiYeYuanXiao); // 毕业院校
                row.getCell(16).setCellValue(ZuiGaoZhuanYe); // 专业
                row.getCell(17).setCellValue(ZuiGaoXueWei); // 学位
                // 申请时间
                row.getCell(18).setCellValue(list.get(i).getApplyDate() == null ? ""
                        : list.get(i).getApplyDate().replace("-", ".").replace("Invalid date", ""));
                row.getCell(19).setCellValue(list.get(i).getApplyDirectionMethod()); // 申请方向及方式
                row.getCell(20).setCellValue(list.get(i).getApprovePaperNo()); // 文号
                row.getCell(21).setCellValue(list.get(i).getApplyExplain()); // 办理情况
            }
            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 新增去南疆申请
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap) {
        mmap.put("personId", personId);
        return prefix + "/add";
    }

    /**
     * 新增保存去南疆申请
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "去南疆申请", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "去南疆申请", tableName = PersonTable.BIZ_PERSON_APPLY_SOUTH, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonApplySouth bizPersonApplySouth) {
        bizPersonApplySouth.setCreateBy(getLoginName());
        return toAjax(bizPersonApplySouthService.insertBizPersonApplySouth(bizPersonApplySouth));
    }

    /**
     * 修改去南疆申请
     */
    @RequiresPermissions(value = { "library:person:edit", "library:apply_south:edit" }, logical = Logical.OR)
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPersonApplySouth bizPersonApplySouth = bizPersonApplySouthService.selectBizPersonApplySouthById(id);
        mmap.put("bizPersonApplySouth", bizPersonApplySouth);
        return prefix + "/edit";
    }

    /**
     * 修改保存去南疆申请
     */
    @RequiresPermissions(value = { "library:person:edit", "library:apply_south:edit" }, logical = Logical.OR)
    @Log(title = "去南疆申请", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "去南疆申请", tableName = PersonTable.BIZ_PERSON_APPLY_SOUTH, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonApplySouth bizPersonApplySouth) {
        bizPersonApplySouth.setUpdateBy(getLoginName());
        return toAjax(bizPersonApplySouthService.updateBizPersonApplySouth(bizPersonApplySouth));
    }

    /**
     * 删除去南疆申请
     */
    @RequiresPermissions(value = { "library:person:edit", "library:apply_south:remove" }, logical = Logical.OR)
    @Log(title = "去南疆申请", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "去南疆申请", tableName = PersonTable.BIZ_PERSON_APPLY_SOUTH, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizPersonApplySouthService.deleteBizPersonApplySouthByIds(ids));
    }
}
