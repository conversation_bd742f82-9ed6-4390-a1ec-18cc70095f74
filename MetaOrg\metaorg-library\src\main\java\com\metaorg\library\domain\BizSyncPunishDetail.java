package com.metaorg.library.domain;

import com.metaorg.common.annotation.Excel;
import com.metaorg.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 同步处罚详情对象 biz_sync_punish_detail
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public class BizSyncPunishDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 处罚公示项ID */
    @Excel(name = "处罚公示项ID")
    private String punishItemId;

    /** 请求ID */
    @Excel(name = "请求ID")
    private String requestId;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer orderNum;

    /** 当事人姓名 */
    @Excel(name = "当事人姓名")
    private String partyName;

    /** 当事人时任职务 */
    @Excel(name = "当事人时任职务")
    private String partyPosition;

    /** 违法违规事实 */
    @Excel(name = "违法违规事实")
    private String violation;

    /** 处罚依据 */
    @Excel(name = "处罚依据")
    private String punishBasis;

    /** 处罚内容 */
    @Excel(name = "行政处罚内容")
    private String punishContent;

    /** 批准机关 */
    @Excel(name = "作出决定机关")
    private String decisionAuthority;

    /** 行政处罚决定书文号 */
    @Excel(name = "行政处罚决定书文号")
    private String punishDocNo;

    /** 处罚决定日期 */
    @Excel(name = "处罚决定日期")
    private String punishDate;

    /** 当事人类型 */
    @Excel(name = "当事人类型")
    private String partyType;

    /** 处罚详情JSON内容 */
    @Excel(name = "处罚详情JSON内容")
    private String punishJsonContent;

    /** 来源ID */
    @Excel(name = "来源ID")
    private String sourceId;

    /** 来源URL */
    @Excel(name = "来源URL")
    private String sourceUrl;

    /** 绑定人员标识 */
    @Excel(name = "绑定人员标识")
    private String bindPersonId;
    
    /** 绑定处罚标识 */
    @Excel(name = "绑定处罚标识")
    private String bindPunishId;
    
    /** 绑定状态 */
    @Excel(name = "绑定状态")
    private String bindStatus;

    /** 关联的处罚公示项 */
    private BizSyncPunishItem punishItem;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setPunishItemId(String punishItemId)
    {
        this.punishItemId = punishItemId;
    }

    public String getPunishItemId()
    {
        return punishItemId;
    }

    public void setRequestId(String requestId)
    {
        this.requestId = requestId;
    }

    public String getRequestId()
    {
        return requestId;
    }

    public void setOrderNum(Integer orderNum)
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum()
    {
        return orderNum;
    }

    public void setPartyName(String partyName)
    {
        this.partyName = partyName;
    }

    public String getPartyName()
    {
        return partyName;
    }

    public void setPartyPosition(String partyPosition)
    {
        this.partyPosition = partyPosition;
    }

    public String getPartyPosition()
    {
        return partyPosition;
    }

    public void setViolation(String violation)
    {
        this.violation = violation;
    }

    public String getViolation()
    {
        return violation;
    }

    public void setPunishBasis(String punishBasis)
    {
        this.punishBasis = punishBasis;
    }

    public String getPunishBasis()
    {
        return punishBasis;
    }

    public void setPunishContent(String punishContent)
    {
        this.punishContent = punishContent;
    }

    public String getPunishContent()
    {
        return punishContent;
    }

    public void setDecisionAuthority(String decisionAuthority)
    {
        this.decisionAuthority = decisionAuthority;
    }

    public String getDecisionAuthority()
    {
        return decisionAuthority;
    }

    public void setPunishDocNo(String punishDocNo)
    {
        this.punishDocNo = punishDocNo;
    }

    public String getPunishDocNo()
    {
        return punishDocNo;
    }

    public void setPunishDate(String punishDate)
    {
        this.punishDate = punishDate;
    }

    public String getPunishDate()
    {
        return punishDate;
    }

    public void setPartyType(String partyType)
    {
        this.partyType = partyType;
    }

    public String getPartyType()
    {
        return partyType;
    }

    public void setPunishJsonContent(String punishJsonContent)
    {
        this.punishJsonContent = punishJsonContent;
    }

    public String getPunishJsonContent()
    {
        return punishJsonContent;
    }

    public void setSourceId(String sourceId)
    {
        this.sourceId = sourceId;
    }

    public String getSourceId()
    {
        return sourceId;
    }

    public void setSourceUrl(String sourceUrl)
    {
        this.sourceUrl = sourceUrl;
    }

    public String getSourceUrl()
    {
        return sourceUrl;
    }

    public void setBindPersonId(String bindPersonId)
    {
        this.bindPersonId = bindPersonId;
    }

    public String getBindPersonId()
    {
        return bindPersonId;
    }

    public void setBindPunishId(String bindPunishId)
    {
        this.bindPunishId = bindPunishId;
    }

    public String getBindPunishId()
    {
        return bindPunishId;
    }

    public void setBindStatus(String bindStatus)
    {
        this.bindStatus = bindStatus;
    }

    public String getBindStatus()
    {
        return bindStatus;
    }

    public BizSyncPunishItem getPunishItem()
    {
        return punishItem;
    }

    public void setPunishItem(BizSyncPunishItem punishItem)
    {
        this.punishItem = punishItem;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("punishItemId", getPunishItemId())
            .append("partyName", getPartyName())
            .append("partyPosition", getPartyPosition())
            .append("violation", getViolation())
            .append("punishBasis", getPunishBasis())
            .append("punishContent", getPunishContent())
            .append("decisionAuthority", getDecisionAuthority())
            .append("punishDocNo", getPunishDocNo())
            .append("punishDate", getPunishDate())
            .append("partyType", getPartyType())
            .append("sourceId", getSourceId())
            .append("sourceUrl", getSourceUrl())
            .append("bindPersonId", getBindPersonId())
            .append("bindPunishId", getBindPunishId())
            .append("bindStatus", getBindStatus())
            .toString();
    }
} 