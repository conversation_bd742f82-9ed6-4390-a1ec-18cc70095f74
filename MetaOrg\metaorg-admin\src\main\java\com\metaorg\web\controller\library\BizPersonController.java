package com.metaorg.web.controller.library;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;

import com.deepoove.poi.XWPFTemplate;
import com.github.pagehelper.PageHelper;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.domain.BaseEntity;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.file.FileUploadUtils;
import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.common.utils.file.MimeTypeUtils;
import com.metaorg.library.domain.*;
import com.metaorg.library.entity.PersonXml;
import com.metaorg.library.service.*;
import com.metaorg.library.utils.AsposeUtil;
import com.metaorg.system.domain.SysRank;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.system.service.ISysDictDataService;
import com.metaorg.system.service.ISysRankService;
import com.metaorg.library.service.IBizPersonFavoriteService;
import com.metaorg.utils.DataScopeUtils;
import com.metaorg.web.controller.tool.POIUtil;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.naming.NoNameCoder;
import com.thoughtworks.xstream.io.xml.Xpp3Driver;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.common.exception.ServiceException;
import java.lang.reflect.InvocationTargetException;
import com.metaorg.common.utils.DictUtils;
import com.metaorg.common.core.domain.entity.SysDictData;

/**
 * 人员信息库Controller
 *
 * <AUTHOR>
 * @date 2023-05-17
 */
@Controller
@RequestMapping("/library/person")
public class BizPersonController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(BizPersonController.class);

    private String prefix = "library/person";

    @Autowired
    private IBizPersonService bizPersonService;

    @Autowired
    private IBizPersonReportService bizPersonReportService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IBizPersonResumeService resumeService;

    @Autowired
    private IBizPersonQualificationService qualificationService;

    @Autowired
    private IBizPersonLevelService levelService;

    @Autowired
    private IBizPersonFamilyService familyService;

    @Autowired
    private IBizPersonEducationService educationService;

    @Autowired
    private IBizPersonPoliticsService politicsService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysRankService rankService;

    @Autowired
    private IBizPersonPositionService positionService;

    @Autowired
    private IBizPersonRewardPunishService bizPersonRewardPunishService;

    @Autowired
    private IBizPersonTagService bizPersonTagService;

    @Autowired
    private IBizPersonOperationLogService bizPersonOperationLogService;

    @Autowired
    private IBizPersonFavoriteService favoriteService;

    @RequiresPermissions("library:person:view")
    @GetMapping()
    public String person(@RequestParam(value = "type") String libraryType, @RequestParam Map<String, Object> params, ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }

        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                mmap.put(entry.getKey(), entry.getValue());
            }
        }

        // 获取职级树列表
        SysRank sysRank = new SysRank();
        sysRank.setParentId("0");
        List<SysRank> ranks = rankService.selectRankTreeList(sysRank);
        mmap.put("ranks", ranks);
        mmap.put("libraryType", libraryType);
        return prefix + "/person";
    }

    @RequiresPermissions("library:person:view")
    @GetMapping("/selectPerson/{deptId}")
    public String selectPerson(@PathVariable(value = "deptId") long deptId, ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(deptId);
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }

        return prefix + "/selectPerson";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizPerson bizPerson, Map<String, Object> paramMap) {
        String libraryType = "";
        if (paramMap.get("libraryType") != null && paramMap.get(("libraryType")) != "") {
            libraryType = paramMap.get("libraryType").toString();
        } else {
            libraryType = "1";
        }
        if (paramMap.get("manageCategoryList") != null && paramMap.get(("manageCategoryList")) != "") {
            bizPerson.getParams().put("manageCategoryList",
                    Convert.toStrArray(paramMap.get("manageCategoryList").toString()));
        }
        if (paramMap.get("postRank") != null && paramMap.get(("postRank")) != "") {
            bizPerson.getParams().put("postRankIds", Convert.toStrArray(paramMap.get("postRank").toString()));
        }
        if (bizPerson.getParams().containsKey("ages")) {
            bizPerson.getParams().put("agesList", Convert.toStrArray(bizPerson.getParams().get("ages").toString()));
        }
        if (paramMap.get("personnelType") != null && paramMap.get(("personnelType")) != "") {
            libraryType = paramMap.get("personnelType").toString();
        }
        if (paramMap.get("retireLeaveFlagSearch") != null && paramMap.get(("retireLeaveFlagSearch")) != "") {
            bizPerson.setRetireLeaveFlag(paramMap.get("retireLeaveFlagSearch").toString());
        }
        if (paramMap.get("eduCategory") != null && paramMap.get(("eduCategory")) != "") {
            bizPerson.getParams().put("eduCategory", paramMap.get("eduCategory").toString());
        }
        if (paramMap.get("eduName") != null && paramMap.get(("eduName")) != "") {
            bizPerson.getParams().put("eduName", paramMap.get("eduName").toString());
        }
        if (paramMap.get("leadingGroupLevel") != null && paramMap.get(("leadingGroupLevel")) != "") {
            bizPerson.getParams().put("leadingGroupLevel", paramMap.get("leadingGroupLevel").toString());
        }
        if (paramMap.get("positionName") != null && paramMap.get(("positionName")) != "") {
            bizPerson.getParams().put("positionName", paramMap.get("positionName").toString());
        }
        if (paramMap.get("positionAdjustTagList") != null && paramMap.get(("positionAdjustTagList")) != "") {
            bizPerson.getParams().put("positionAdjustTagList", Convert.toStrArray(paramMap.get("positionAdjustTagList").toString()));
        }
        if (libraryType.equals("-1") || libraryType.equals("1") || libraryType.equals("2") || libraryType.equals("3")
                || libraryType.equals("4") || libraryType.equals("5") || libraryType.equals("7")) {// 未删除
            bizPerson.setDelFlag("0");
        }
        if (libraryType.equals("1")) {// 现职人员
            bizPerson.setManageStatus("1,5");
        }
        if (libraryType.equals("2")) {// 已退休
            // bizPerson.setRetireLeaveFlag("Y");
            bizPerson.setManageStatus("2");
        }
        if (libraryType.equals("3")) {// 调离
            bizPerson.setManageStatus("3");
        }
        if (libraryType.equals("4")) {// 已去世
            bizPerson.setManageStatus("4");
        }
        if (libraryType.equals("5")) {// 其他
            bizPerson.setManageStatus("6");
        }
        if (libraryType.equals("6")) {// 已删除干部
            bizPerson.setDelFlag("2");
        }
        if (libraryType.equals("7")) {// 班子管理
            bizPerson.setLeaderFlag("Y");
        }
        if (libraryType.equals("8")) {// 免职（相当于辞退）
            bizPerson.setManageStatus("8");
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", bizPerson.getDeptId());
        }
    }

    /**
     * 查询人员信息库列表
     */
    @RequiresPermissions("library:person:list")
    @Log(title = "人员信息库", businessType = BusinessType.OTHER, onlyRecordExceptions = true)
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestParam(value = "type", defaultValue = "1") String libraryType, BizPerson bizPerson,
            @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizPerson, paramMap);
        startPage();
        List<BizPerson> list = bizPersonService.selectBizPersonList(bizPerson);
        return getDataTable(list);
    }

    /**
     * 转序列岗人员
     */
    @GetMapping(("/transfer_it"))
    public String position_transfer(ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        return prefix + "/transfer_it";
    }

    /**
     * 查询需转序列岗人员列表
     */
    @RequiresPermissions("library:transfer_it:list")
    @PostMapping("/transfer_it_list")
    @ResponseBody
    public TableDataInfo transferItList(@RequestParam Map<String, Object> paramMap, BizPerson bizPerson,
            ModelMap mmap) {
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "d", "", "", bizPerson.getDeptId());
        }
        startPage();
        List<BizPerson> list = bizPersonService.selectPersonTransferToItList(bizPerson);
        return getDataTable(list);
    }

    /**
     * 导出人员信息库列表
     */
    @RequiresPermissions("library:person:export")
    @Log(title = "人员信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestParam(value = "type", defaultValue = "1") String libraryType, BizPerson bizPerson,
            @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizPerson, paramMap);
        List<BizPerson> list = bizPersonService.selectBizPersonList(bizPerson);
        list.sort(Comparator.comparing(BizPerson::getOrderNum).thenComparing(BizPerson::getId,
                Comparator.reverseOrder()));
        ExcelUtil<BizPerson> util = new ExcelUtil<BizPerson>(BizPerson.class);
        return util.exportExcel(list, "人员信息库数据");
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

    /**
     * 导出人员花名册
     */
    @RequiresPermissions("library:person:export")
    @Log(title = "人员信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/export/gbmc")
    @ResponseBody
    public AjaxResult exportGbmc(@RequestParam(value = "type", defaultValue = "1") String libraryType,
            @RequestParam Map<String, Object> paramMap, BizPerson bizPerson) {
        getQueryOptions(bizPerson, paramMap);
        bizPerson.getParams().put("orderBy", "p.order_num asc");
        List<BizPerson> list = bizPersonService.selectBizPersonList(bizPerson);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "干部名册_" + System.currentTimeMillis() + ".xls";
        try {
            positionService.fillPositionForPersons(list);
            File file = new File("./config/gbmc_template.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);// .getSheet("Sheet1");
            for (int i = 0; i < list.size(); i++) {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, 4 + i);
                HSSFRow row = hssfSheet.getRow(4 + i);
                row.getCell(0).setCellValue(i + 1); // 序号
                row.getCell(1).setCellValue(list.get(i).getName()); // 姓名
                row.getCell(2).setCellValue(list.get(i).getDept().getDeptName()); // 行社（部门）
                row.getCell(3).setCellValue(list.get(i).getJobUnit()); // 职务
                row.getCell(4).setCellValue(list.get(i).getCitizenId()); // 身份证号码
                row.getCell(5).setCellValue(list.get(i).getSex()); // 性别
                row.getCell(6).setCellValue(list.get(i).getAge().replace("岁", "")); // 年龄
                row.getCell(7).setCellValue(list.get(i).getMaritalStatus()); // 婚姻状况
                row.getCell(8).setCellValue(list.get(i).getHealth()); // 健康状况
                row.getCell(9).setCellValue(list.get(i).getNation()); // 民族
                row.getCell(10).setCellValue(list.get(i).getNativePlace()); // 籍贯
                row.getCell(11).setCellValue(list.get(i).getBirthPlace()); // 出生地

                // 出生年月
                row.getCell(12).setCellValue(list.get(i).getBirthday() == null ? ""
                        : list.get(i).getBirthday().replace("-", ".").replace("Invalid date", ""));
                // 通过政治情况获取入党时间
                BizPersonPolitics politics = new BizPersonPolitics();
                politics.setPersonId(list.get(i).getId());
                List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);
                BizPersonPolitics bizPersonPolitics = null;
                if (politicsList != null) {
                    bizPersonPolitics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
                        @Override
                        public boolean test(BizPersonPolitics bizPersonPolitics) {
                            return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
                        }
                    }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
                        @Override
                        public BizPersonPolitics get() {
                            return new BizPersonPolitics();
                        }
                    });
                }
                // 入党时间
                if (bizPersonPolitics != null && bizPersonPolitics.getJoinPartyDay() != null) {
                    row.getCell(13).setCellValue(
                            bizPersonPolitics.getJoinPartyDay().replace("-", ".").replace("Invalid date", ""));
                } else {
                    row.getCell(13).setCellValue("");
                }
                // 参加工作时间
                row.getCell(14).setCellValue(list.get(i).getJobDay() == null ? ""
                        : list.get(i).getJobDay().replace("-", ".").replace("Invalid date", ""));

                BizPersonEducation education = new BizPersonEducation(); // 学历对象
                education.setPersonId(list.get(i).getId());
                List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);
                BizPersonEducation highestEducation = null; // 最高学历
                String QuanRiZhiJiaoYu_XueLi = ""; // 全日制教育学历
                String QuanRiZhiJiaoYu_XueWei = ""; // 全日制教育学位
                String QuanRiZhiJiaoYu_BiYeYuanXiaoXi = ""; // 全日制教育学校及院系（含专业名称）
                // String QuanRiZhiJiaoYu_ZhuanYe = ""; //全日制教育专业
                String QuanRiZhiJiaoYu_BiYeShiJian = ""; // 全日制教育毕业日期
                String ZaiZhiJiaoYu_XueLi = ""; // 在职教育学历
                String ZaiZhiJiaoYu_XueWei = ""; // 在职教育学位
                String ZaiZhiJiaoYu_BiYeYuanXiaoXi = ""; // 在职教育学校及院系（含专业名称）
                // String ZaiZhiJiaoYu_ZhuanYe = ""; //在职教育专业
                String ZaiZhiJiaoYu_BiYeShiJian = ""; // 在职毕业日期
                String ZuiGaoXueLi = ""; // 最高学历
                // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum)); //正序
                // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum).reversed());
                // //倒序
                if (educations != null) {
                    for (BizPersonEducation item : educations) {
                        // 根据是否最高学历标识获取最高学历对象
                        if (item.getHighestDegreeFlag().equalsIgnoreCase("Y")) {
                            highestEducation = item;
                        }
                        if (item.getEduCategory().equals("全日制教育")) {
                            QuanRiZhiJiaoYu_XueLi = item.getEduName();
                            QuanRiZhiJiaoYu_XueWei = item.getDegreeCode();
                            QuanRiZhiJiaoYu_BiYeYuanXiaoXi = item.getEduSchool();
                            // QuanRiZhiJiaoYu_ZhuanYe = item.getMajorName();
                            QuanRiZhiJiaoYu_BiYeShiJian = item.getGraduationDay().replace("-", ".")
                                    .replace("Invalid date", "");
                        }
                        if (item.getEduCategory().equals("在职教育")) {
                            ZaiZhiJiaoYu_XueLi = item.getEduName();
                            ZaiZhiJiaoYu_XueWei = item.getDegreeCode();
                            ZaiZhiJiaoYu_BiYeYuanXiaoXi = item.getEduSchool();
                            // ZaiZhiJiaoYu_ZhuanYe = item.getMajorName();
                            ZaiZhiJiaoYu_BiYeShiJian = item.getGraduationDay().replace("-", ".").replace("Invalid date",
                                    "");
                        }
                    }
                    // 如果没有标识最高学历获取最后一个
                    if (highestEducation == null && educations.size() > 0) {
                        highestEducation = educations.get(educations.size() - 1);
                    }
                    if (highestEducation != null) {
                        ZuiGaoXueLi = highestEducation.getEduName();
                    }
                }
                String QuanRiZhiJiaoYu_XueLi_XueWei = QuanRiZhiJiaoYu_XueLi;
                String ZaiZhiJiaoYu_XueLi_XueWei = ZaiZhiJiaoYu_XueLi;
                if (StringUtils.isNotEmpty(QuanRiZhiJiaoYu_XueLi_XueWei)) {
                    if (StringUtils.isNotEmpty(QuanRiZhiJiaoYu_XueWei))
                        QuanRiZhiJiaoYu_XueLi_XueWei += "\n" + QuanRiZhiJiaoYu_XueWei;
                } else {
                    QuanRiZhiJiaoYu_XueLi_XueWei = QuanRiZhiJiaoYu_XueWei;
                }
                if (StringUtils.isNotEmpty(ZaiZhiJiaoYu_XueLi_XueWei)) {
                    if (StringUtils.isNotEmpty(ZaiZhiJiaoYu_XueWei))
                        ZaiZhiJiaoYu_XueLi_XueWei += "\n" + ZaiZhiJiaoYu_XueWei;
                } else {
                    ZaiZhiJiaoYu_XueLi_XueWei = ZaiZhiJiaoYu_XueWei;
                }
                row.getCell(15).setCellValue(ZuiGaoXueLi); // 最高学历
                row.getCell(16).setCellValue(QuanRiZhiJiaoYu_XueLi_XueWei); // 全日制教育学历/学位
                row.getCell(17).setCellValue(QuanRiZhiJiaoYu_BiYeYuanXiaoXi); // 全日制教育毕业院校及专业
                row.getCell(18).setCellValue(QuanRiZhiJiaoYu_BiYeShiJian);
                row.getCell(19).setCellValue(ZaiZhiJiaoYu_XueLi_XueWei); // 全日制教育学历/学位
                row.getCell(20).setCellValue(ZaiZhiJiaoYu_BiYeYuanXiaoXi);
                row.getCell(21).setCellValue(ZaiZhiJiaoYu_BiYeShiJian);
                row.getCell(22).setCellValue(list.get(i).getQualificationJob()); // 专业技术职务
                if (list.get(i).getPositions().size() > 0) {
                    BizPersonPosition personPosition = positionService
                            .mergePositionsIntoSingle(list.get(i).getPositions());
                    row.getCell(23).setCellValue(personPosition.getPartyPositionName().replace(",", "、")); // 党内职务
                    row.getCell(24).setCellValue(personPosition.getPositionName().replace(",", "、")); // 行政职务
                    row.getCell(25).setCellValue(personPosition.getPositionDay().replace("-", "."));
                    row.getCell(26).setCellValue(personPosition.getSameRankDay().replace("-", "."));
                    row.getCell(27).setCellValue(personPosition.getSamePositionDay().replace("-", "."));
                } else {// 旧逻辑
                    row.getCell(23).setCellValue(""); // 党内职务
                    row.getCell(24).setCellValue(""); // 行政职务
                    // 从简历中获取最新任职时间
                    BizPersonResume resume = new BizPersonResume();
                    resume.setPersonId(list.get(i).getId());
                    List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume,
                            "start_date desc");
                    BizPersonResume lastResume = null;
                    if (resumes != null) {
                        lastResume = resumes.stream().findFirst().orElseGet(new Supplier<BizPersonResume>() {
                            @Override
                            public BizPersonResume get() {
                                return new BizPersonResume();
                            }
                        });
                    }
                    if (lastResume != null && lastResume.getStartDate() != null) {
                        row.getCell(25)
                                .setCellValue(lastResume.getStartDate().replace("-", ".").replace("Invalid date", ""));
                    } else {
                        row.getCell(25).setCellValue("");
                    }
                    // 从专业技术任职资格获取任同职级时间
                    BizPersonQualification qualification = new BizPersonQualification();
                    qualification.setPersonId(list.get(i).getId());
                    List<BizPersonQualification> qualifications = null;
                    qualifications = qualificationService.selectBizPersonQualificationList(qualification);
                    BizPersonQualification lastQualification = null;
                    if (qualifications != null) {
                        // 按任职资格日期排序
                        qualifications.sort(Comparator.comparing(BizPersonQualification::getQualificationDay));
                        if (qualifications.size() > 0) {
                            lastQualification = qualifications.get(qualifications.size() - 1);
                        }
                    }
                    if (lastQualification != null && lastQualification.getQualificationDay() != null) {
                        row.getCell(26).setCellValue(
                                lastQualification.getQualificationDay().replace("-", ".").replace("Invalid date", ""));
                    } else {
                        row.getCell(26).setCellValue("");
                    }
                    // 职务层次情况获取岗位任同岗位时间
                    BizPersonLevel level = new BizPersonLevel();
                    level.setPersonId(list.get(i).getId());
                    List<BizPersonLevel> levels = null;
                    levels = levelService.selectBizPersonLevelList(level);
                    BizPersonLevel lastLevel = null;
                    if (levels != null) {
                        // 按职务层次批准日期排序
                        levels.sort(Comparator.comparing(BizPersonLevel::getApproveDay));
                        if (levels.size() > 0) {
                            lastLevel = levels.get(levels.size() - 1);
                        }
                    }
                    if (lastLevel != null && lastLevel.getApproveDay() != null) {
                        row.getCell(27)
                                .setCellValue(lastLevel.getApproveDay().replace("-", ".").replace("Invalid date", ""));
                    } else {
                        row.getCell(27).setCellValue("");
                    }
                }
                row.getCell(28).setCellValue(list.get(i).getIdentityFlag()); // 个人身份特殊标识
                String supportFrontierMember = list.get(i).getSupportFrontierMember();
                row.getCell(29).setCellValue(
                        dictDataService.selectDictLabel("sys_yes_no", supportFrontierMember, supportFrontierMember)); // 是否援疆干部
                row.getCell(30).setCellValue(list.get(i).getPersonIdentity()); // 个人身份
                String retireLeaveFlag = list.get(i).getRetireLeaveFlag();
                row.getCell(31)
                        .setCellValue(dictDataService.selectDictLabel("sys_yes_no", retireLeaveFlag, retireLeaveFlag)); // 离岗退养
                row.getCell(32).setCellValue(list.get(i).getSpeciality()); // 专长
                row.getCell(33).setCellValue(list.get(i).getRewardPunishContent()); // 奖惩情况
                row.getCell(34).setCellValue(list.get(i).getCheckContent()); // 考核内容
                row.getCell(35).setCellValue(list.get(i).getQualificationSpeciality()); // 专业特长
                String rankName = "";
                if (list.get(i).getPositions().size() > 0) {
                    rankName = list.get(i).getPositions().get(0).getPostRankName();
                }
                row.getCell(36).setCellValue(rankName);

                // 退休（内退）时间
                String retireLeaveDay = list.get(i).getRetireLeaveDay();
                row.getCell(37).setCellValue(StringUtils.isEmpty(retireLeaveDay) ? ""
                        : retireLeaveDay.replace("-", ".").replace("Invalid date", ""));
                // 免（离）职时间
                String dismissDay = list.get(i).getDismissDay();
                row.getCell(38).setCellValue(StringUtils.isEmpty(dismissDay) ? ""
                        : dismissDay.replace("-", ".").replace("Invalid date", ""));
                // 去世时间
                String deathDay = list.get(i).getDeathDay();
                row.getCell(39).setCellValue(
                        StringUtils.isEmpty(deathDay) ? "" : deathDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(40).setCellValue(list.get(i).getPersonNote()); // 个人基本情况备注
            }
            POIUtil.removeRow(hssfSheet, 3);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 新增人员信息库
     */
    @GetMapping("/add")
    public String add(@RequestParam(value = "deptId", defaultValue = "0") Long deptId, ModelMap mmap) {
        deptService.checkDeptDataScope(deptId);
        SysDept dept = deptService.selectDeptById(deptId);
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", deptId);
            mmap.put("deptName", dept.getDeptName());
            mmap.put("maxOrderNum", getMaxOrderNum(deptId));
        }
        return prefix + "/add";
    }

    /**
     * 批量编辑人员信息
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/batchEdit")
    public String batchEdit(@RequestParam(value = "id", defaultValue = "0") Long deptId, ModelMap mmap) {
        deptService.checkDeptDataScope(deptId);
        SysDept dept = deptService.selectDeptById(deptId);
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", deptId);
            mmap.put("deptName", dept.getDeptName());
            mmap.put("maxOrderNum", getMaxOrderNum(deptId));
        }
        return prefix + "/batchEdit";
    }

    /**
     * 新增保存人员信息库
     */
    @RequiresPermissions("library:person:add")
    @Log(title = "人员信息库", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPerson bizPerson, HttpServletRequest request, MultipartFile file2) {
        if (StringUtils.isNotEmpty(bizPerson.getCitizenId())) {
            boolean exist = bizPersonService.existCitizenId(bizPerson.getCitizenId());
            if (exist) {
                return error("公民身份号码【" + bizPerson.getCitizenId() + "】已存在，请勿重复添加！");
            }
        }

        // 检查orderNumber是否为空
        if (StringUtils.isNull(bizPerson.getOrderNum())) {
            BizPerson filterBizPerson = new BizPerson();
            filterBizPerson.setDelFlag("0");
            bizPerson.setOrderNum(getMaxOrderNum(filterBizPerson));
        }
        int row = bizPersonService.insertBizPerson(bizPerson);
        if (row > 0) {
            return success(bizPerson);
        }
        return error();
    }

    /**
     * 修改人员信息库
     */
    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.VIEW)
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPerson bizPerson = bizPersonService.selectBizPersonById(id);

        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(id);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);

        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(id);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);

        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(id);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);

        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(id);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum).reversed());

        BizPersonEducation quanRiZhiEntity = new BizPersonEducation();
        BizPersonEducation zaiZhiEntity = new BizPersonEducation();
        quanRiZhiEntity = educations.stream().filter(item -> item.getEduCategory().equals("全日制教育")).findFirst()
                .orElseGet(new Supplier<BizPersonEducation>() {
                    @Override
                    public BizPersonEducation get() {
                        return new BizPersonEducation();
                    }
                });
        zaiZhiEntity = educations.stream().filter(item -> item.getEduCategory().equals("在职教育")).findFirst()
                .orElseGet(new Supplier<BizPersonEducation>() {
                    @Override
                    public BizPersonEducation get() {
                        return new BizPersonEducation();
                    }
                });

        BizPersonFavorite favorite = new BizPersonFavorite();
        favorite.setPersonId(id);
        List<BizPersonFavorite> favorites = favoriteService.selectBizPersonFavoriteByPersonId(id, getUserId());
        mmap.put("isFavorite", favorites.isEmpty() ? false : true);

        // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum));
        // BizPersonEducation quanRiZhiEntity = new BizPersonEducation();
        // BizPersonEducation zaiZhiEntity = new BizPersonEducation();
        // quanRiZhiEntity.setHighestDegreeFlag("N");
        // quanRiZhiEntity.setHighestMajorFlag("N");
        // zaiZhiEntity.setHighestDegreeFlag("N");
        // zaiZhiEntity.setHighestMajorFlag("N");
        // for (BizPersonEducation item : educations) {
        // if (item.getEduCategory().equals("全日制教育")) {
        // //全日制最高学位
        // if(quanRiZhiEntity.getHighestMajorFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getDegreeCode())){
        // quanRiZhiEntity.setDegreeCode(item.getDegreeCode());
        // quanRiZhiEntity.setHighestMajorFlag(item.getHighestMajorFlag());
        // }
        // }else{
        // if(item.getHighestMajorFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getDegreeCode())){
        // quanRiZhiEntity.setDegreeCode(item.getDegreeCode());
        // quanRiZhiEntity.setHighestMajorFlag(item.getHighestMajorFlag());
        // }
        // }
        // }
        // //全日制最高学历
        // if(!quanRiZhiEntity.getHighestDegreeFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getEduName())){
        // quanRiZhiEntity.setEduName(item.getEduName());
        // quanRiZhiEntity.setHighestDegreeFlag(item.getHighestDegreeFlag());
        // }
        // }else{
        // if(item.getHighestDegreeFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getEduName())){
        // quanRiZhiEntity.setEduName(item.getEduName());
        // quanRiZhiEntity.setHighestDegreeFlag(item.getHighestDegreeFlag());
        // }
        // }
        // }
        // // 毕业院校系及专业
        // quanRiZhiEntity.setEduSchool(item.getEduSchool());
        // quanRiZhiEntity.setMajorName(item.getMajorName());
        // }
        // if (item.getEduCategory().equals("在职教育")) {
        // //在职教育最高学位
        // if(!zaiZhiEntity.getHighestMajorFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getDegreeCode())){
        // zaiZhiEntity.setDegreeCode(item.getDegreeCode());
        // zaiZhiEntity.setHighestMajorFlag(item.getHighestMajorFlag());
        // }
        // }else{
        // if(item.getHighestMajorFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getDegreeCode())){
        // zaiZhiEntity.setDegreeCode(item.getDegreeCode());
        // zaiZhiEntity.setHighestMajorFlag(item.getHighestMajorFlag());
        // }
        // }
        // }
        // //在职教育最高学历
        // if(!zaiZhiEntity.getHighestDegreeFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getEduName())){
        // zaiZhiEntity.setEduName(item.getEduName());
        // zaiZhiEntity.setHighestDegreeFlag(item.getHighestDegreeFlag());
        // }
        // }else{
        // if(item.getHighestDegreeFlag().equalsIgnoreCase("Y")){
        // if(StringUtils.isNotEmpty(item.getEduName())){
        // zaiZhiEntity.setEduName(item.getEduName());
        // zaiZhiEntity.setHighestDegreeFlag(item.getHighestDegreeFlag());
        // }
        // }
        // }
        // // 毕业院校、系及专业
        // zaiZhiEntity.setEduSchool(item.getEduSchool());
        // zaiZhiEntity.setMajorName(item.getMajorName());
        // }
        // }

        List<BizPerson> list = new ArrayList<>();
        list.add(bizPerson);
        bizPersonService.fillPersonDeptForPersons(list);

        mmap.put("bizPerson", bizPerson);
        mmap.put("resumes", resumes);
        mmap.put("families", families);
        mmap.put("educations", educations);
        mmap.put("personDepts", bizPerson.getPersonDepts());
        mmap.put("quanRiZhi_eduCategory", quanRiZhiEntity.getEduCategory());
        mmap.put("quanRiZhi_eduName", quanRiZhiEntity.getEduName());
        mmap.put("quanRiZhi_degreeCode", quanRiZhiEntity.getDegreeCode());
        mmap.put("quanRiZhi_eduSchool", quanRiZhiEntity.getEduSchool());
        mmap.put("quanRiZhi_majorName", quanRiZhiEntity.getMajorName());
        mmap.put("zaiZhi_eduCategory", zaiZhiEntity.getEduCategory());
        mmap.put("zaiZhi_eduName", zaiZhiEntity.getEduName());
        mmap.put("zaiZhi_degreeCode", zaiZhiEntity.getDegreeCode());
        mmap.put("zaiZhi_eduSchool", zaiZhiEntity.getEduSchool());
        mmap.put("zaiZhi_majorName", zaiZhiEntity.getMajorName());
        mmap.put("politics", politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        }));
        return prefix + "/edit";
    }

    /**
     * 修改人员信息库
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/photo/{id}")
    public String photo(@PathVariable("id") String id, ModelMap mmap) {
        BizPerson bizPerson = bizPersonService.selectBizPersonById(id);
        mmap.put("bizPerson", bizPerson);
        return prefix + "/photo";
    }

    /**
     * 查看个人360画像
     */
    @PersonOperationLog(module = "360画像", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.VIEW)
    @RequiresPermissions("library:person:view")
    @GetMapping("/profile/{id}")
    public String profile(@PathVariable("id") String id, ModelMap mmap) {
        BizPerson bizPerson = bizPersonService.selectBizPersonById(id);

        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(id);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);

        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(id);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);

        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(id);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);

        if (resumes != null && resumes.size() > 0) {
            resumes.forEach(item -> {
                if (StringUtils.isEmpty(item.getStartDate()) || StringUtils.isEmpty(item.getEndDate())) {
                    return;
                }

                try {
                    Date beginTime = DateUtils.parseDate(item.getStartDate());
                    Date endTime = DateUtils.parseDate(item.getEndDate());
                    item.getParams().put("duration", DateUtils.getYearMonthDiff(beginTime, endTime));
                } catch (Exception e) {
                    log.error("计算教育经历时长异常{}", e.getMessage());
                }
            });
        }

        BizPersonEducation quanRiZhiEntity = new BizPersonEducation();
        BizPersonEducation zaiZhiEntity = new BizPersonEducation();
        quanRiZhiEntity = educations.stream().filter(item -> item.getEduCategory().equals("全日制教育")).findFirst()
                .orElseGet(new Supplier<BizPersonEducation>() {
                    @Override
                    public BizPersonEducation get() {
                        return new BizPersonEducation();
                    }
                });
        zaiZhiEntity = educations.stream().filter(item -> item.getEduCategory().equals("在职教育")).findFirst()
                .orElseGet(new Supplier<BizPersonEducation>() {
                    @Override
                    public BizPersonEducation get() {
                        return new BizPersonEducation();
                    }
                });

        BizPersonPosition position = new BizPersonPosition();
        position.setPersonId(id);
        position.setCurrentPost("Y");
        List<BizPersonPosition> positions = positionService.selectBizPersonPositionList(position);

        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(id);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        BizPersonRewardPunish bizPersonRewardPunish = new BizPersonRewardPunish();
        bizPersonRewardPunish.setPersonId(id);
        bizPersonRewardPunish.getParams().put("orderby", "a.reward_punish_day desc,a.id desc");
        List<BizPersonRewardPunish> rewardPunishList = bizPersonRewardPunishService
                .selectBizPersonRewardPunishList(bizPersonRewardPunish);

        BizPersonFavorite favorite = new BizPersonFavorite();
        favorite.setPersonId(id);
        List<BizPersonFavorite> favorites = favoriteService.selectBizPersonFavoriteByPersonId(id, getUserId());
        mmap.put("isFavorite", favorites.isEmpty() ? false : true);

        mmap.put("person", bizPerson);
        mmap.put("resumes", resumes);
        mmap.put("families", families);
        mmap.put("educations", educations);
        mmap.put("quanRiZhi_eduCategory", quanRiZhiEntity.getEduCategory());
        mmap.put("quanRiZhi_eduName", quanRiZhiEntity.getEduName());
        mmap.put("quanRiZhi_degreeCode", quanRiZhiEntity.getDegreeCode());
        mmap.put("quanRiZhi_eduSchool", quanRiZhiEntity.getEduSchool());
        mmap.put("quanRiZhi_majorName", quanRiZhiEntity.getMajorName());
        mmap.put("zaiZhi_eduCategory", zaiZhiEntity.getEduCategory());
        mmap.put("zaiZhi_eduName", zaiZhiEntity.getEduName());
        mmap.put("zaiZhi_degreeCode", zaiZhiEntity.getDegreeCode());
        mmap.put("zaiZhi_eduSchool", zaiZhiEntity.getEduSchool());
        mmap.put("zaiZhi_majorName", zaiZhiEntity.getMajorName());
        mmap.put("politics", politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        }));

        mmap.put("positions", positions.stream().findFirst().orElseGet(new Supplier<BizPersonPosition>() {
            @Override
            public BizPersonPosition get() {
                return new BizPersonPosition();
            }
        }));

        mmap.put("rewards", rewardPunishList.stream().filter(new Predicate<BizPersonRewardPunish>() {
            @Override
            public boolean test(BizPersonRewardPunish bizPersonRewardPunish) {
                return bizPersonRewardPunish.getRewardPunishCategory().equals("奖励");
            }
        }).collect(Collectors.toList()));

        mmap.put("punishments", rewardPunishList.stream().filter(new Predicate<BizPersonRewardPunish>() {
            @Override
            public boolean test(BizPersonRewardPunish bizPersonRewardPunish) {
                return bizPersonRewardPunish.getRewardPunishCategory().equals("惩处");
            }
        }).collect(Collectors.toList()));

        // 查询人员标签
        BizPersonTag bizPersonTag = new BizPersonTag();
        bizPersonTag.setPersonId(id);
        List<BizPersonTag> personTags = bizPersonTagService.selectBizPersonTagList(bizPersonTag);
        mmap.put("personTags", personTags);

        return prefix + "/profile";
    }

    /**
     * 查询人员操作记录
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/operationLogs/{personId}")
    @ResponseBody
    public TableDataInfo operationLogs(@PathVariable("personId") String personId) {
        startPage();
        List<BizPersonOperationLog> list = bizPersonOperationLogService.selectOperationLogByPersonId(personId);
        return getDataTable(list);
    }

    /**
     * 保存头像
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updatePhoto/{id}")
    @ResponseBody
    public AjaxResult updatePhoto(@PathVariable("id") String id, @RequestParam("avatarfile") MultipartFile file) {
        BizPerson bizPerson = bizPersonService.selectBizPersonById(id);
        try {
            if (!file.isEmpty()) {
                String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file,
                        MimeTypeUtils.IMAGE_EXTENSION);
                bizPerson.setPhoto(avatar);
                if (bizPersonService.updateBizPerson(bizPerson) > 0) {
                    return success(avatar);
                }
            }
            return error();
        } catch (Exception e) {
            log.error("修改头像失败！", e);
            return error(e.getMessage());
        }
    }

    /**
     * 修改保存人员信息库
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "人员信息库", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPerson bizPerson) {
        if (StringUtils.isNotEmpty(bizPerson.getCitizenId())) {
            boolean exist = bizPersonService.existCitizenId(bizPerson.getCitizenId(),
                    new String[] {bizPerson.getId() });
            if (exist) {
                return error("公民身份号码 【" + bizPerson.getCitizenId() + "】已存在，请勿重复添加！");
            }
        }
        return toAjax(bizPersonService.updateBizPerson(bizPerson));
    }

    /**
     * 批量修改保存人员信息库
     */
    @RequiresPermissions("library:person:edit")
    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.UPDATE)
    @Log(title = "批量修改人员信息库", businessType = BusinessType.UPDATE)
    @PostMapping("/batch_edit")
    @ResponseBody
    public AjaxResult batchEditSave(BizPerson bizPerson) {
        return toAjax(bizPersonService.updateBizPersonByIds(bizPerson));
    }

    /**
     * 删除人员信息库
     */
    @RequiresPermissions("library:person:remove")
    @Log(title = "人员信息库", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizPersonService.deleteBizPersonByIds(ids));
    }

    /**
     * 删除人员信息库
     */
    @RequiresPermissions("library:person:remove")
    @Log(title = "人员信息库", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.DELETE)
    @PostMapping("/delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(bizPersonService.realDeleteBizPersonByIds(ids));
    }

    /**
     * 恢复人员信息库
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "人员信息库", businessType = BusinessType.UPDATE)
    @PostMapping("/revert")
    @ResponseBody
    public AjaxResult revert(String ids) {
        // return success();
        return toAjax(bizPersonService.revertBizPersonByIds(ids));
    }

    /**
     * 导入人员信息库
     */
    @GetMapping("/import")
    public String imports(@RequestParam(value = "deptId", defaultValue = "0") Long deptId, ModelMap mmap) {
        deptService.checkDeptDataScope(deptId);
        SysDept dept = deptService.selectDeptById(deptId);
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", deptId);
            mmap.put("deptName", dept.getDeptName());
            mmap.put("maxOrderNum", getMaxOrderNum(deptId));
        }
        return prefix + "/import";
    }

    /**
     * 分析图表
     */
    @GetMapping("/analytics1")
    public String analytics1(@RequestParam(value = "ids", defaultValue = "") String ids, ModelMap mmap) {
        mmap.put("ids", ids);
        return prefix + "/analytics1";
    }

    /**
     * 导出人员信息库LRMX
     */
    @GetMapping("/export/lrmx/{personId}")
    @RequiresPermissions("library:person:export")
    public void exportsLrmx(@PathVariable(value = "personId") String personId, HttpServletResponse resp)
            throws IOException {
        BizPerson person = bizPersonService.selectBizPersonById(personId);
        PersonXml xml = PersonXml.fromBizPerson(person);

        // 人员简历
        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(personId);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);

        // 家庭成员
        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(personId);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);

        // 教育经历
        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(personId);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);

        // 政治面貌
        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(personId);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        politics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        });

        xml.setResumes(resumes);
        xml.setFamilies(families);
        xml.setEducations(educations);
        xml.setPolitics(politics);
        xml.setChuShengNianYue(xml.getChuShengNianYue().replace("-", "").replace(".", ""));
        xml.setRuDangShiJian(xml.getRuDangShiJian().replace("-", "").replace(".", ""));
        xml.setCanJiaGongZuoShiJian(xml.getCanJiaGongZuoShiJian().replace("-", "").replace(".", ""));
        XStream xstream = new XStream(new Xpp3Driver(new NoNameCoder()));
        xstream.autodetectAnnotations(true);
        xstream.setMode(XStream.NO_REFERENCES);

        OutputStream out = null;
        String safeName = StringEscapeUtils.escapeHtml4(person.getName()); // 防止文件名被浏览器解析
        safeName = StringEscapeUtils.unescapeHtml4(safeName);
        String filename = safeName + "_" + System.currentTimeMillis() + ".lrmx";
        try {
            filename = FileUtils.fixFilenameByRegex(filename);
            resp.setHeader("Content-Disposition",
                    "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, "UTF-8"));
            out = resp.getOutputStream();
            out.write("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n".getBytes("UTF-8"));
            xstream.toXML(xml, out);
        } catch (Exception e) {
            log.error("导出Lrmx异常{}", e.getMessage());
            throw new UtilException("导出Lrmx失败，请联系网站管理员！");
        } finally {
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * 导出人员任免表WORD
     */
    @GetMapping("/export/word/{personId}")
    @RequiresPermissions("library:person:export")
    @Cacheable(value = "users", key = "#id")
    public void exportsWord(@PathVariable(value = "personId") String personId, HttpServletResponse resp) {
        BizPerson person = bizPersonService.selectBizPersonById(personId);
        if (person == null) {
            return;
        }

        PersonXml xml = PersonXml.fromBizPerson(person);

        // 人员简历
        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(personId);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);

        // 家庭成员
        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(personId);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);

        // 教育经历
        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(personId);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);

        // 政治面貌
        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(personId);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        politics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        });

        xml.setResumes(resumes);
        xml.setFamilies(families);
        xml.setEducations(educations);
        xml.setPolitics(politics);

        OutputStream out = null;
        String safeName = StringEscapeUtils.escapeHtml4(person.getName()); // 防止文件名被浏览器解析
        safeName = StringEscapeUtils.unescapeHtml4(safeName);
        String filename = safeName + "_" + System.currentTimeMillis() + ".docx";
        // String filename = person.getName() +".docx";
        try {
            File resource = new File("./config/rmb_template.docx").getAbsoluteFile();

            // new ClassPathResource("rmb_template.docx").getFile().getAbsoluteFile();
            // File resource = new
            // ClassPathResource("rmb_template.docx").getFile().getAbsoluteFile();
            XWPFTemplate template = XWPFTemplate.compile(resource).render(xml.toWordTemplateData());
            filename = FileUtils.fixFilenameByRegex(filename);
            resp.setHeader("Content-Disposition",
                    "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, "UTF-8"));
            resp.setHeader("Pragma", "no-cache");
            resp.addHeader("Cache-Control", "must-revalidate");
            resp.addHeader("Cache-Control", "no-cache");
            resp.addHeader("Cache-Control", "no-store");
            resp.setDateHeader("Expires", 0);

            out = resp.getOutputStream();
            template.writeAndClose(out);
        } catch (Exception e) {
            log.error("导出Word异常{}", e.getMessage());
            throw new UtilException("导出Word失败，请联系网站管理员！");
        } finally {
            IOUtils.closeQuietly(out);
        }
    }

    @GetMapping("/pdf/{personId}")
    @RequiresPermissions("library:person:export")
    public void exportsPdf(@PathVariable(value = "personId") String personId, HttpServletResponse resp) {
        BizPerson person = bizPersonService.selectBizPersonById(personId);
        if (person == null) {
            return;
        }

        PersonXml xml = PersonXml.fromBizPerson(person);

        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(personId);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);

        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(personId);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);

        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(personId);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);

        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(personId);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        politics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        });

        xml.setResumes(resumes);
        xml.setFamilies(families);
        xml.setEducations(educations);
        xml.setPolitics(politics);

        OutputStream out = null;
        String safeName = StringEscapeUtils.escapeHtml4(person.getName()); // 防止文件名被浏览器解析
        safeName = StringEscapeUtils.unescapeHtml4(safeName);
        String filename = safeName + "_" + System.currentTimeMillis() + ".pdf";

        try {
            File resource = new File("./config/rmb_template.docx").getAbsoluteFile();

            XWPFTemplate template = XWPFTemplate.compile(resource).render(xml.toWordTemplateData());

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            template.writeAndClose(byteArrayOutputStream);

            out = resp.getOutputStream();

            AsposeUtil bean = new AsposeUtil();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            bean.word2pdf(out, byteArrayInputStream);

            byteArrayOutputStream.close();

            filename = FileUtils.fixFilenameByRegex(filename);
            resp.setHeader("Content-Disposition",
                    "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, "UTF-8"));
            resp.setHeader("Pragma", "no-cache");
            resp.addHeader("Cache-Control", "must-revalidate");
            resp.addHeader("Cache-Control", "no-cache");
            resp.addHeader("Cache-Control", "no-store");
            resp.setDateHeader("Expires", 0);
        } catch (Exception e) {
            log.error("导出Pdf异常{}", e.getMessage());
            throw new UtilException("导出Pdf失败，请联系网站管理员！");
        } finally {
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * 排序地址与通信
     */
    // @RequiresPermissions("library:address:edit")
    @Log(title = "人员信息库", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        // Map<Long, List<BizPerson>> deptPersons = new HashMap<>();
        // for (int i = 0; i < idsArray.length; i++)
        // {
        // BizPerson bizPerson = bizPersonService.selectBizPersonById(idsArray[i]);
        // if(!deptPersons.containsKey(bizPerson.getDeptId())) {
        // deptPersons.put(bizPerson.getDeptId(), new ArrayList<>());
        // }
        // List<BizPerson> list = deptPersons.get(bizPerson.getDeptId());
        // list.add(bizPerson);
        // }
        // for (Map.Entry<Long, List<BizPerson>> entry : deptPersons.entrySet()) {
        // List<BizPerson> list = entry.getValue();
        // long minOrderNum =
        // list.stream().mapToLong(BizPerson::getOrderNum).min().getAsLong();
        // if(minOrderNum == 0) minOrderNum = 1;
        // for(BizPerson bizPerson: list) {
        // bizPerson.setOrderNum(minOrderNum);
        // minOrderNum++;
        // bizPersonService.updateBizPerson(bizPerson);
        // }
        // }
        List<BizPerson> persons = new ArrayList<>();
        for (int i = 0; i < idsArray.length; i++) {
            BizPerson bizPerson = bizPersonService.selectBizPersonById(idsArray[i]);
            if (bizPerson != null) {
                persons.add(bizPerson);
            }
        }
        long minOrderNum = persons.stream().mapToLong(BizPerson::getOrderNum).min().getAsLong();
        if (minOrderNum == 0)
            minOrderNum = 1;
        for (BizPerson bizPerson : persons) {
            bizPerson.setOrderNum(minOrderNum);
            minOrderNum++;
            bizPersonService.updateBizPerson(bizPerson);
        }
        return success();
    }

    /**
     * 导出需转专业岗人员列表
     */
    @RequiresPermissions("library:transfer_it:export")
    @Log(title = "人员信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/export/transfer_it")
    @ResponseBody
    public AjaxResult exportTransferIt(BizPerson bizPerson, @RequestParam Map<String, Object> paramMap) {
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "d", "", "", bizPerson.getDeptId());
        }
        bizPerson.getParams().put("orderBy", "p.order_num asc");
        List<BizPerson> list = bizPersonService.selectPersonTransferToItList(bizPerson);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "需转专业岗_" + System.currentTimeMillis() + ".xls";

        try {
            File file = new File("./config/template/xuzhuanzhuanyegang_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            for (int i = 0; i < list.size(); i++) {
                BizPerson person = list.get(i);

                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                row.getCell(0).setCellValue(i + 1); // 序号
                row.getCell(1).setCellValue(person.getDept().getDeptName()); // 行社名称
                row.getCell(2).setCellValue(person.getName()); // 姓名
                row.getCell(3).setCellValue(person.getJobUnit()); // 职务
                row.getCell(4).setCellValue(person.getSex()); // 性别
                // 出生年月
                String birthday = person.getBirthday();
                row.getCell(5).setCellValue(
                        StringUtils.isEmpty(birthday) ? "" : birthday.replace("-", ".").replace("Invalid date", ""));
                row.getCell(6).setCellValue(person.getAge().replace("岁", "")); // 年龄
                row.getCell(7).setCellValue(""); // 籍贯
            }

            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导出行社高管职务统计
     */
    @RequiresPermissions("library:person:export")
    @Log(title = "人员信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/export/leaderposition")
    @ResponseBody
    public AjaxResult exportLeaderPosition(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        long deptId = -1;
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            baseEntity.getParams().put("deptId", deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(baseEntity, getSysUser(), "vpd", "", "", deptId);
        }

        List<Map<String, Object>> list = bizPersonReportService.selectLeaderPostGroup(baseEntity);
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "行社高管数据统计表_" + System.currentTimeMillis() + ".xls";
        try {
            File file = new File("./config/template/leaderposition_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);// .getSheet("Sheet1");

            int mergedStart = 4;
            int mergedEnd = 4;
            int listSize = list.size();
            String curPdeptName = "";
            if (listSize > 0)
                curPdeptName = list.get(0).get("pdept_name").toString();
            for (int i = 0; i < listSize; i++) {
                String pdeptName = list.get(i).get("pdept_name").toString();
                if (!curPdeptName.equals(pdeptName) || i == listSize - 1) {
                    if (i == listSize - 1 && listSize > 1 && curPdeptName.equals(pdeptName))
                        mergedEnd++;
                    if (mergedStart != mergedEnd) {
                        CellRangeAddress region = new CellRangeAddress(mergedStart, mergedEnd, 0, 0);
                        hssfSheet.addMergedRegion(region);
                    }
                    curPdeptName = pdeptName;
                    mergedStart = 4 + i;
                }
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, 4 + i);
                HSSFRow row = hssfSheet.getRow(4 + i);
                row.getCell(0).setCellValue(pdeptName); // 地区
                row.getCell(1).setCellValue(i + 1); // 序号
                row.getCell(2).setCellValue(list.get(i).get("dept_name").toString()); // 行社（部门）
                row.getCell(3).setCellFormula("E" + (5 + i) + "+S" + (5 + i) + "+T" + (5 + i)); // 合计
                row.getCell(4).setCellFormula("sum(F" + (5 + i) + ":R" + (5 + i) + ")"); // 班子职数
                row.getCell(5).setCellValue(Integer.valueOf(list.get(i).get("sjdlsz_num").toString())); // 书记、董（理）事长
                row.getCell(6).setCellValue(Integer.valueOf(list.get(i).get("fsjdlsz_num").toString())); // 副书记、董（理）事长
                row.getCell(7).setCellValue(Integer.valueOf(list.get(i).get("fsj_num").toString())); // 副书记
                row.getCell(8).setCellValue(Integer.valueOf(list.get(i).get("fsjjwsj_num").toString())); // 副书记、纪委书记
                row.getCell(9).setCellValue(Integer.valueOf(list.get(i).get("fsjfdlsz_num").toString())); // 副书记、副董（理）事长
                row.getCell(10).setCellValue(Integer.valueOf(list.get(i).get("fdlsz_num").toString())); // 副董（理）事长
                row.getCell(11).setCellValue(Integer.valueOf(list.get(i).get("hzzr_num").toString())); // 行长（主任）
                row.getCell(12).setCellValue(0); // 主持经营工作
                row.getCell(13).setCellValue(Integer.valueOf(list.get(i).get("jwsjjsz_num").toString())); // 纪委书记、监事长
                row.getCell(14).setCellValue(Integer.valueOf(list.get(i).get("jsz_num").toString())); // 监事长
                row.getCell(15).setCellValue(Integer.valueOf(list.get(i).get("fhzfzr_num").toString())); // 副行长（副主任）
                row.getCell(16).setCellValue(Integer.valueOf(list.get(i).get("dwwy_num").toString())); // 党委委员
                row.getCell(17).setCellValue(Integer.valueOf(list.get(i).get("wyzl_num").toString())); // 委员助理
                row.getCell(18).setCellValue(Integer.valueOf(list.get(i).get("zl_num").toString())); // 助理
                row.getCell(19).setCellFormula("sum(U" + (5 + i) + ":W" + (5 + i) + ")"); // 非领导职务
                row.getCell(20).setCellValue(Integer.valueOf(list.get(i).get("xcy_num").toString())); // 巡察员
                row.getCell(21).setCellValue(Integer.valueOf(list.get(i).get("ghzx_num").toString())); // 工会主席
                row.getCell(22).setCellValue(Integer.valueOf(list.get(i).get("zyjs_num").toString())); // 专业技术
                mergedEnd = 4 + i;
            }
            if (listSize > 0) {
                // 合并单元格式
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, 4 + listSize);
                HSSFRow row = hssfSheet.getRow(4 + listSize);
                row.getCell(0).setCellValue(""); // 地区
                row.getCell(1).setCellValue(""); // 序号
                row.getCell(2).setCellValue("合计");
                final String[] colNames = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O",
                        "P", "Q", "R", "S", "T", "U", "V", "W" };
                for (int i = 3; i < colNames.length; i++) {
                    row.getCell(i).setCellFormula("sum(" + colNames[i] + "5:" + colNames[i] + (listSize + 4) + ")");
                }
            }
            POIUtil.removeRow(hssfSheet, 3);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导出行社班子缺位统计
     */
    @RequiresPermissions("library:person:export")
    @Log(title = "人员信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/export/positionomission")
    @ResponseBody
    public AjaxResult exportPositiOnomission(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        long deptId = -1;
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            baseEntity.getParams().put("deptId", deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(baseEntity, getSysUser(), "vpd", "", "", deptId);
        }

        List<Map<String, Object>> list = bizPersonReportService.selectPostOmissionGroup(baseEntity);
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "行社班子缺位统计表_" + System.currentTimeMillis() + ".xls";
        try {
            File file = new File("./config/template/positionomission_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);

            int listSize = list.size();
            int hzzr_index = 4 + listSize + 1;
            int jsz_index = 4 + 2 * listSize + 2;
            int fhzfzr_index = 4 + 3 * listSize + 3;
            POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, hzzr_index - 1);
            POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, jsz_index - 1);
            POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, fhzfzr_index - 1);
            hssfSheet.getRow(hzzr_index - 1).getCell(0).setCellValue("行长（主任）");
            hssfSheet.getRow(jsz_index - 1).getCell(0).setCellValue("监事长");
            hssfSheet.getRow(fhzfzr_index - 1).getCell(0).setCellValue("副行长（主任）");

            for (int i = 0; i < list.size(); i++) {
                // 董（理）事长
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, 4 + i);
                int dlsz_num = Integer.valueOf(list.get(i).get("dlsz_num").toString());
                int hzzr_num = Integer.valueOf(list.get(i).get("hzzr_num").toString());
                int jsz_num = Integer.valueOf(list.get(i).get("jsz_num").toString());
                int fhzfzr_num = Integer.valueOf(list.get(i).get("fhzfzr_num").toString());
                int qt_num = Integer.valueOf(list.get(i).get("qt_num").toString());
                int leaderCount = dlsz_num + hzzr_num + jsz_num + fhzfzr_num + qt_num;
                int assigned_positions = Integer.valueOf(list.get(i).get("assigned_positions").toString());

                HSSFRow row = hssfSheet.getRow(4 + i);
                row.getCell(0).setCellValue(i + 1); // 序号
                row.getCell(1).setCellValue(list.get(i).get("dept_name").toString()); // 行社
                row.getCell(2).setCellValue(list.get(i).get("assigned_positions").toString()); // 应配班子数
                row.getCell(3).setCellValue(dlsz_num == 0 ? "1人" : ""); // 缺位数量
                row.getCell(4).setCellValue(leaderCount); // 班子现有职数
                row.getCell(5).setCellValue(""); // 现情况
                row.getCell(6).setCellValue(""); // 备注
                // 行长（主任）
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, hzzr_index + i);
                HSSFRow hzzr_row = hssfSheet.getRow(hzzr_index + i);
                hzzr_row.getCell(0).setCellValue(i + 1); // 序号
                hzzr_row.getCell(1).setCellValue(list.get(i).get("dept_name").toString()); // 行社
                hzzr_row.getCell(2).setCellValue(list.get(i).get("assigned_positions").toString()); // 应配班子数
                hzzr_row.getCell(3).setCellValue(hzzr_num == 0 ? "1人" : ""); // 缺位数量
                hzzr_row.getCell(4).setCellValue(leaderCount); // 班子现有职数
                hzzr_row.getCell(5).setCellValue(""); // 现情况
                hzzr_row.getCell(6).setCellValue(""); // 备注
                // 监事长
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, jsz_index + i);
                HSSFRow jsz_row = hssfSheet.getRow(jsz_index + i);
                jsz_row.getCell(0).setCellValue(i + 1); // 序号
                jsz_row.getCell(1).setCellValue(list.get(i).get("dept_name").toString()); // 行社
                jsz_row.getCell(2).setCellValue(list.get(i).get("assigned_positions").toString()); // 应配班子数
                jsz_row.getCell(3).setCellValue(jsz_num == 0 ? "1人" : ""); // 缺位数量
                jsz_row.getCell(4).setCellValue(leaderCount); // 班子现有职数
                jsz_row.getCell(5).setCellValue(""); // 现情况
                jsz_row.getCell(6).setCellValue(""); // 备注
                // 副行长（主任）
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, fhzfzr_index + i);
                HSSFRow fhzfzr_row = hssfSheet.getRow(fhzfzr_index + i);
                fhzfzr_row.getCell(0).setCellValue(i + 1); // 序号
                fhzfzr_row.getCell(1).setCellValue(list.get(i).get("dept_name").toString()); // 行社
                fhzfzr_row.getCell(2).setCellValue(list.get(i).get("assigned_positions").toString()); // 应配班子数
                // 副行长（主任）缺位数量 = 应配职数 - 3 - 其他领导数，其中3代表董（理）事长、行长（主任）、监事长
                int fhzfzr_onomission = assigned_positions - 3 - qt_num;
                fhzfzr_row.getCell(3).setCellValue(fhzfzr_onomission > 0 ? fhzfzr_onomission + "人" : ""); // 缺位数量
                fhzfzr_row.getCell(4).setCellValue(leaderCount); // 班子现有职数
                fhzfzr_row.getCell(5).setCellValue(""); // 现情况
                fhzfzr_row.getCell(6).setCellValue(""); // 备注
            }
            POIUtil.removeRow(hssfSheet, 3);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导出人员职务结构统计
     */
    @RequiresPermissions("library:person:export")
    @Log(title = "人员信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/export/rank_structure")
    @ResponseBody
    public AjaxResult exportRankStructure(@RequestParam Map<String, Object> paramMap, BizPerson bizPerson) {
        getQueryOptions(bizPerson, paramMap);
        List<Map<String, Object>> list = bizPersonService.selectRankStructureGroup(bizPerson);

        // 获取职级信息
        SysRank rankWhere = new SysRank();
        rankWhere.setParentId("0");
        List<SysRank> rankTree = rankService.selectRankTreeList(rankWhere);

        Map<String, Integer> deptMap = new HashMap<>();
        Map<String, Integer> rankMap = new HashMap<>();
        LinkedHashSet<String> deptSet = new LinkedHashSet<>();

        for (int i = 0; i < list.size(); i++) {
            deptSet.add(list.get(i).get("dept_name").toString());
        }

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "人员职务结构_" + System.currentTimeMillis() + ".xls";
        try {
            File file = new File("./config/template/zhiwujigou_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            // 生成头部（职级）
            HSSFRow row1 = hssfSheet.getRow(1);
            HSSFRow row2 = hssfSheet.getRow(2);
            HSSFRow row3 = hssfSheet.getRow(3);
            int cloumnIndex = 1;
            int cloumnMergedStart = 1;
            for (int i = 0; i < rankTree.size(); i++) {
                int childSize = rankTree.get(i).getChilds().size();
                if (i == 0) {
                    row1.getCell(cloumnIndex).setCellValue(rankTree.get(i).getRankName());
                } else {
                    cloumnIndex++;
                    row1.createCell(cloumnIndex);
                    row1.getCell(cloumnIndex).setCellStyle(row1.getCell(1).getCellStyle());
                    row1.getCell(cloumnIndex).setCellValue(rankTree.get(i).getRankName());
                }
                for (int j = 0; j < childSize; j++) {
                    if (j > 0) {
                        cloumnIndex++;
                        row1.createCell(cloumnIndex);
                        row1.getCell(cloumnIndex).setCellStyle(row1.getCell(1).getCellStyle());
                    }
                    row2.createCell(cloumnIndex);
                    row2.getCell(cloumnIndex).setCellStyle(row1.getCell(1).getCellStyle());
                    int colWidth = rankTree.get(i).getChilds().get(j).getRankName().getBytes().length * 256;
                    // 记录职级的位置
                    rankMap.put(rankTree.get(i).getChilds().get(j).getRankName(), cloumnIndex);

                    // 设置列宽
                    hssfSheet.setColumnWidth(cloumnIndex, colWidth);
                    row2.getCell(cloumnIndex).setCellValue(rankTree.get(i).getChilds().get(j).getRankName());
                    if (cloumnIndex > 1) {
                        row3.createCell(cloumnIndex);
                        row3.getCell(cloumnIndex).setCellStyle(row3.getCell(1).getCellStyle());
                        row3.getCell(cloumnIndex).setCellValue(row3.getCell(1).getNumericCellValue());
                    }
                }
                if (childSize > 1) {
                    CellRangeAddress region = new CellRangeAddress(1, 1, cloumnMergedStart,
                            cloumnMergedStart + childSize - 1);
                    hssfSheet.addMergedRegion(region);
                    cloumnMergedStart += childSize;
                } else {
                    cloumnMergedStart++;
                }
            }

            String totalColLabel = "A"; // 人数合计列最后一列列名（前一列）
            // 增加部门/行社列
            if (rankTree.size() > 0) {
                cloumnIndex++;
                row1.createCell(cloumnIndex);
                row2.createCell(cloumnIndex);
                row3.createCell(cloumnIndex);
                row1.getCell(cloumnIndex).setCellStyle(row1.getCell(1).getCellStyle());
                row2.getCell(cloumnIndex).setCellStyle(row1.getCell(1).getCellStyle());
                row3.getCell(cloumnIndex).setCellStyle(row3.getCell(1).getCellStyle());
                row1.getCell(cloumnIndex).setCellValue("人数合计");
                CellRangeAddress region = new CellRangeAddress(1, 2, cloumnIndex, cloumnIndex);
                hssfSheet.addMergedRegion(region);

                int colWidth3 = "人数合计".getBytes().length * 256;
                // 设置列宽
                hssfSheet.setColumnWidth(cloumnIndex, colWidth3);
                row3.getCell(cloumnIndex).setCellValue(row3.getCell(1).getNumericCellValue());
                totalColLabel = POIUtil.convertToColLabel(cloumnIndex);
                row3.getCell(cloumnIndex).setCellFormula("sum(B4:" + totalColLabel + "4)");
                // 合并第一行标题
                CellRangeAddress regionRow0 = new CellRangeAddress(0, 0, 0, cloumnIndex);
                hssfSheet.addMergedRegion(regionRow0);
            }
            // 初始化列首（左侧部门/行社）
            int m = 0;
            for (String deptName : deptSet) {
                if (m == 0) {
                    row3.getCell(0).setCellValue(deptName);
                } else {
                    POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, 3 + m);
                    HSSFRow row = hssfSheet.getRow(3 + m);
                    row.getCell(0).setCellValue(deptName);
                    row.getCell(cloumnIndex).setCellFormula("sum(B" + (4 + m) + ":" + totalColLabel + (4 + m) + ")");
                }
                deptMap.put(deptName, 3 + m);
                m++;
            }

            for (int i = 0; i < list.size(); i++) {
                int cellIndex = rankMap.get(list.get(i).get("rank_name").toString());
                int rowIndex = deptMap.get(list.get(i).get("dept_name").toString());
                HSSFRow row = hssfSheet.getRow(rowIndex);
                row.getCell(cellIndex).setCellValue(Integer.valueOf(list.get(i).get("count").toString()));
            }
            if (m == 0) {
                POIUtil.removeRow(hssfSheet, 3);
            } else {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, 3 + m);
                HSSFRow row = hssfSheet.getRow(3 + m);
                row.getCell(0).setCellValue("合计");
                // 按职级合计
                for (int i = 1; i <= cloumnIndex; i++) {
                    String colLabel = POIUtil.convertToColLabel(i + 1);
                    row.getCell(i).setCellFormula("sum(" + colLabel + "4:" + colLabel + (3 + m) + ")");
                }
                m++;
            }

            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    private long getMaxOrderNum(Long deptId) {
        BizPerson bizPerson = new BizPerson();
        bizPerson.setDeptId(deptId);
        bizPerson.setDelFlag("0");
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPerson> list = bizPersonService.selectBizPersonList(bizPerson);
        if (!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }

    private long getMaxOrderNum(BizPerson bizPerson) {
        bizPerson.setDelFlag("0");
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPerson> list = bizPersonService.selectBizPersonList(bizPerson);
        if (!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }

    /**
     * 查询身份证是否存在
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/citizenId/exist/{citizenId}")
    @Log(title = "查询身份证是否存在", businessType = BusinessType.OTHER)
    @ResponseBody
    public AjaxResult imports(@PathVariable(value = "citizenId") String citizenId) {
        boolean exist = bizPersonService.existCitizenId(citizenId);
        HashMap<String, Boolean> result = new HashMap<String, Boolean>();
        result.put("exist", exist);
        return success(result);
    }

    /**
     * 添加人员标签
     */
    @RequiresPermissions("library:person:edit")
    @PostMapping("/tags/{personId}")
    @Log(title = "添加人员标签", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult addTag(@PathVariable(value = "personId") String personId,
            @RequestParam Map<String, Object> paramMap) {
        String createUserId = getUserId().toString();
        String createUserName = getUserName();

        String tagCategoryId = Convert.toStr(paramMap.get("tag_category_id"), "");
        String tagCategory = Convert.toStr(paramMap.get("tag_category"), "");
        String tagNames = Convert.toStr(paramMap.get("tag_name"), "");
        String tagMatch = Convert.toStr(paramMap.get("tag_match"), "");
        String tagNameIds = Convert.toStr(paramMap.get("tag_name_id"), "");
        String tagValueIds = Convert.toStr(paramMap.get("tag_value_id"), "");
        String tagValueNames = Convert.toStr(paramMap.get("tag_value_name"), "");
        String tagValues = Convert.toStr(paramMap.get("tag_value"), "");

        if (StringUtils.isEmpty(tagNames)) {
            return error("标签名称不能为空");
        }

        // 分割标签名称，支持批量添加
        String[] tagNameArray = tagNames.split(",");
        String[] tagNameIdArray = StringUtils.isNotEmpty(tagNameIds) ? tagNameIds.split(",") : new String[0];
        String[] tagValueIdArray = StringUtils.isNotEmpty(tagValueIds) ? tagValueIds.split(",") : new String[0];
        String[] tagValueNameArray = StringUtils.isNotEmpty(tagValueNames) ? tagValueNames.split(",") : new String[0];
        String[] tagValueArray = StringUtils.isNotEmpty(tagValues) ? tagValues.split(",") : new String[0];

        List<Map<String, Object>> addedTags = new ArrayList<>();

        Date currentDate = DateUtils.getNowDate();

        for (int i = 0; i < tagNameArray.length; i++) {
            String tagName = tagNameArray[i].trim();
            if (StringUtils.isEmpty(tagName)) {
                continue;
            }

            BizPersonTag bizPersonTag = new BizPersonTag();
            bizPersonTag.setCreateBy(createUserName);
            bizPersonTag.setCreateById(createUserId);
            bizPersonTag.setUpdateBy(createUserName);
            bizPersonTag.setUpdateById(createUserId);

            bizPersonTag.setCreateTime(currentDate);
            bizPersonTag.setUpdateTime(currentDate);

            bizPersonTag.setPersonId(personId);
            bizPersonTag.setTagCategory(tagCategory);
            bizPersonTag.setTagName(tagName);
            bizPersonTag.setTagMatch(tagMatch);
            bizPersonTag.setSyncVisual("是");
            bizPersonTag.setTagSource("manual");

            bizPersonTag.setTagCategoryId(tagCategoryId);

            if (i < tagNameIdArray.length) {
                bizPersonTag.setTagNameId(tagNameIdArray[i].trim());
            }
            if (i < tagValueIdArray.length) {
                bizPersonTag.setTagValueId(tagValueIdArray[i].trim());
            }
            if (i < tagValueNameArray.length) {
                bizPersonTag.setTagValueName(tagValueNameArray[i].trim());
            }
            if (i < tagValueArray.length) {
                bizPersonTag.setTagValue(tagValueArray[i].trim());
            }

            // 删除已存在的相同标签
            BizPersonTag deleteBizPersonTag = new BizPersonTag();
            deleteBizPersonTag.setPersonId(personId);
            deleteBizPersonTag.setTagCategoryId(bizPersonTag.getTagCategoryId());
            deleteBizPersonTag.setTagName(bizPersonTag.getTagName());
            deleteBizPersonTag.setTagValue(bizPersonTag.getTagValue());
            bizPersonTagService.deleteBizPersonTag(deleteBizPersonTag);

            // 插入新标签
            bizPersonTagService.insertBizPersonTag(bizPersonTag);

            // 收集添加的标签信息
            Map<String, Object> tagInfo = new HashMap<>();
            tagInfo.put("id", bizPersonTag.getId());
            tagInfo.put("tag_category_id", bizPersonTag.getTagCategoryId());
            tagInfo.put("tag_name_id", bizPersonTag.getTagNameId());
            tagInfo.put("tag_name", bizPersonTag.getTagName());
            tagInfo.put("tag_value_id", bizPersonTag.getTagValueId());
            tagInfo.put("tag_value_name", bizPersonTag.getTagValueName());
            tagInfo.put("tag_value", bizPersonTag.getTagValue());
            addedTags.add(tagInfo);
        }

        // 返回批量添加的标签信息
        Map<String, Object> data = new HashMap<>();
        data.put("tags", addedTags);
        data.put("count", addedTags.size());
        return AjaxResult.success("成功添加 " + addedTags.size() + " 个标签", data);
    }

    /**
     * 删除人员标签
     */
    @RequiresPermissions("library:person:edit")
    @PostMapping("/tags/{personId}/{tagId}")
    @Log(title = "删除人员标签", businessType = BusinessType.DELETE)
    @ResponseBody
    public AjaxResult removeTag(@PathVariable(value = "personId") String personId,
            @PathVariable(value = "tagId") String tagId) {
        if (StringUtils.isEmpty(tagId)) {
            return error("标签标识不能为空");
        }
        if (StringUtils.isEmpty(personId)) {
            return error("人员标识不能为空");
        }
        BizPersonTag bizPersonTag = new BizPersonTag();
        bizPersonTag.setId(tagId);
        bizPersonTag.setPersonId(personId);
        bizPersonTagService.deleteBizPersonTag(bizPersonTag);
        return success();
    }

    /**
    * 下载人员信息导入模板
    */
    @GetMapping("/template")
    @ResponseBody
    public AjaxResult downloadTemplate()
    {
        List<BizPersonTplVM> list = new ArrayList<>();
        BizPersonTplVM demo1 = new BizPersonTplVM();
        BizPersonTplVM demo2 = new BizPersonTplVM();

        String identityFlag = "";
        List<SysDictData> identityFlagDicts = DictUtils.getDictCache("biz_person_identity_flag");
        if (identityFlagDicts != null && identityFlagDicts.size() > 0) {
            boolean found = false;
            for (SysDictData dict : identityFlagDicts) {
                if ("人大代表".equals(dict.getDictLabel())) {
                    identityFlag = dict.getDictValue();
                    found = true;
                    break;
                }
            }
            if (!found) {
                identityFlag = identityFlagDicts.get(0).getDictValue();
            }
        }

        String manageCategory = "";
        List<SysDictData> manageCategoryDicts = DictUtils.getDictCache("biz_person_manage_category");
        if (manageCategoryDicts != null && manageCategoryDicts.size() > 0) {
            boolean found = false;
            for (SysDictData dict : manageCategoryDicts) {
                if ("区联社机关干部".equals(dict.getDictLabel())) {
                    manageCategory = dict.getDictValue();
                    found = true;
                    break;
                }
            }
            if (!found) {
                manageCategory = manageCategoryDicts.get(0).getDictValue();
            }
        }

        String personIdentity = "";
        List<SysDictData> personIdentityDicts = DictUtils.getDictCache("biz_person_identity");
        if (personIdentityDicts != null && personIdentityDicts.size() > 0) {
            boolean found = false;
            for (SysDictData dict : personIdentityDicts) {
                if ("行社高管".equals(dict.getDictLabel())) {
                    identityFlag = dict.getDictValue();
                    found = true;
                    break;
                }
            }
            if (!found) {
                identityFlag = identityFlagDicts.get(0).getDictValue();
            }
        }

        demo1.setDeptName("乌鲁木齐农商银行");
        demo1.setPersonName("陈天帆");
        demo1.setCitizenId("450921198767880618");
        demo1.setSex("0");
        demo1.setNation("01");
        demo1.setBirthday("1976-01");
        demo1.setNativePlace("广西玉林");
        demo1.setBirthPlace("广西玉林");
        demo1.setRegisterPlace("广西玉林");
        demo1.setGrowupPlace("广西玉林");
        demo1.setMaritalStatus("20");
        demo1.setHealth("1");
        demo1.setRegisterExtend("");
        demo1.setUsedName("");
        demo1.setIdentityFlag(identityFlag);
        demo1.setLeaderFlag("N");
        demo1.setExcellentMemberOfCpc("N");
        demo1.setExcellentMemberOfCps("N");
        demo1.setSupportFrontierMember("N");
        demo1.setJobDay("2005-01");
        demo1.setCivilServantRegisterDay("2005-01");
        demo1.setJoinSystemDay("2005-01");
        demo1.setExperienceInBase("Y");
        demo1.setManageCategory(manageCategory);
        demo1.setManageStatus("1");
        demo1.setDeathDay("");
        demo1.setDismissDay("");
        demo1.setJobUnit("乌鲁木齐农商银行副行长");
        demo1.setPersonIdentity(personIdentity);
        demo1.setSpeciality("");
        demo1.setPersonNote("");
        demo1.setRetireLeaveFlag("N");
        demo1.setRetireLeaveDay("");

        demo2.setDeptName("乌鲁木齐农商银行");
        demo2.setPersonName("王军");
        demo2.setCitizenId("450921198767880611");
        demo2.setSex("0");
        demo2.setNation("01");
        demo2.setBirthday("1976-01");
        demo2.setNativePlace("广西玉林");
        demo2.setBirthPlace("广西玉林");
        demo2.setRegisterPlace("广西玉林");
        demo2.setGrowupPlace("广西玉林");
        demo2.setMaritalStatus("20");
        demo2.setHealth("1");
        demo2.setRegisterExtend("");
        demo2.setUsedName("");
        demo2.setIdentityFlag(identityFlag);
        demo2.setLeaderFlag("N");
        demo2.setExcellentMemberOfCpc("N");
        demo2.setExcellentMemberOfCps("N");
        demo2.setSupportFrontierMember("N");
        demo2.setJobDay("2005-01");
        demo2.setCivilServantRegisterDay("2005-01");
        demo2.setJoinSystemDay("2005-01");
        demo2.setExperienceInBase("N");
        demo2.setManageCategory(manageCategory);
        demo2.setManageStatus("2");
        demo2.setDeathDay("");
        demo2.setDismissDay("");
        demo2.setJobUnit("乌鲁木齐农商银行副行长");
        demo2.setPersonIdentity(personIdentity);
        demo2.setSpeciality("");
        demo2.setPersonNote("");
        demo2.setRetireLeaveFlag("Y");
        demo2.setRetireLeaveDay("2005-01");
       
        list.add(demo1);
        list.add(demo2);
        ExcelUtil<BizPersonTplVM> util = new ExcelUtil<BizPersonTplVM>(BizPersonTplVM.class);
        return util.exportExcel(list, "人员信息导入模板");
    }

    /**
     * 导入人员信息库批量修改数据
     */
    @Log(title = "导入人员批量修改信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions("library:person:edit")
    @PostMapping("/import")
    @ResponseBody
    public AjaxResult importPerson(MultipartFile file) throws Exception
    {
        ExcelUtil<BizPersonTplVM> util = new ExcelUtil<>(BizPersonTplVM.class);
        List<BizPersonTplVM> adjustList = util.importExcel(file.getInputStream());
        String message = importPersonData(adjustList);
        return AjaxResult.success(message);
    }

    private String importPersonData(List<BizPersonTplVM> adjustList){
        if (StringUtils.isNull(adjustList) || adjustList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 导入数据检查
        Boolean isValid = checkBizPersonImportData(adjustList);

        if (!isValid) {
            for (BizPersonTplVM pvm : adjustList) {
                if (StringUtils.isNotEmpty(pvm.getErrMsg())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
            } 
            if (failureNum > 0)
            {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            }
        }

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        for (BizPersonTplVM pvm : adjustList) {
            try {
                BizPerson person = BizPerson.fromBizPersonTplVM(pvm);

                person.setUpdateById(createUserId);
                person.setUpdateBy(createUserName);
                person.setUpdateTime(currentDate);
                
                int result = bizPersonService.updateBizPerson(person);
                if (result > 0) {
                    successNum++;
                } else {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、姓名：" + pvm.getPersonName()  + "导入失败：";
                    failureMsg.append(msg + "数据入库失败");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、姓名：" + pvm.getPersonName()  + "导入失败：";
                String exMsg = e.getMessage();
                if(StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException){
                    exMsg = ((InvocationTargetException)e).getTargetException().getMessage();
                }
                if(StringUtils.isEmpty(exMsg)){
                    exMsg = "数据入库失败";
                }
                failureMsg.append(msg + exMsg);
            }
        }

        if (failureNum > 0)
        {
            if (!isValid) {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            } else {
                failureMsg.insert(0, "总共：" + adjustList.size() + "，成功：" + successNum + "，失败：" + failureNum + "，错误如下：");
            }
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条。");
        }


        return successMsg.toString();
    }

    private Boolean checkBizPersonImportData(List<BizPersonTplVM> checkList) {
        Boolean isOk = true;

        // 根据身份证获取人员信息
        HashMap<String, BizPerson> personMap = new HashMap<>();
        BizPerson bizPersonSearch = new BizPerson();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonSearch, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }

        HashSet<String> citizenIdSet = new HashSet<>();
        HashSet<String> deptNameSet = new HashSet<>();

        for (BizPersonTplVM pvm : checkList) {
            if (StringUtils.isNotEmpty(pvm.getCitizenId())) {
                String citizenId = pvm.getCitizenId().trim();
                citizenIdSet.add(citizenId);
                pvm.setCitizenId(citizenId);
            }

            if (StringUtils.isEmpty(pvm.getDeptName())) {
                continue;
            }

            String deptName = pvm.getDeptName().trim();
            deptNameSet.add(deptName);
            pvm.setDeptName(deptName);
        }

        if (citizenIdSet.size() > 0) {
            bizPersonSearch.getParams().put("citizenIds", citizenIdSet.toArray());
            List<BizPerson> personList = bizPersonService.selectBizPersonListByCitizenIds(bizPersonSearch);
            for (BizPerson person : personList) {
                personMap.put(person.getCitizenId().trim(), person);
            }
        }

        HashMap<String, SysDept> deptMap = new HashMap<>();
        
        if (deptNameSet.size() > 0) {
           SysDept sysDeptSearch = new SysDept();
           sysDeptSearch.getParams().put("deptNames", deptNameSet.toArray());
            List<SysDept> deptList = deptService.selectDeptList(sysDeptSearch);
            for (SysDept dept : deptList) {
                deptMap.put(dept.getDeptName().trim(), dept);
            }
        }

        int rowIndex = 0;
        for (BizPersonTplVM checkTplVM : checkList) {
            rowIndex++;

            String citizenId = checkTplVM.getCitizenId();
            String preMsg = "第" + rowIndex + "行数据：姓名：" + checkTplVM.getPersonName() + "，身份证：" + citizenId + " ";

            String deptName = checkTplVM.getDeptName();

            try {
                // 判断是否为空
                ArrayList<String> emptyMsgList = new ArrayList<>();

                if (StringUtils.isEmpty(checkTplVM.getPersonName()))
                    emptyMsgList.add("姓名");

                if (StringUtils.isEmpty(citizenId))
                    emptyMsgList.add("身份证");

                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }
                
                // 检查身份证号码
                if (!personMap.containsKey(checkTplVM.getCitizenId())) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "身份证'" + checkTplVM.getCitizenId() + "'不存在，请检查身份证号码");
                    continue;
                }
                checkTplVM.setPerson(personMap.get(checkTplVM.getCitizenId()));

                // 检查机构名称
                if (StringUtils.isNotEmpty(deptName) && !deptMap.containsKey(deptName)) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "机构名称'" + deptName + "'不存在，请检查机构名称");
                    continue;
                }
                checkTplVM.setDept(deptMap.get(deptName));
            
                // 检查性别
                if (StringUtils.isNotEmpty(checkTplVM.getSex())) {
                    checkTplVM.setSex(DictUtils.getDictLabel("sys_user_sex", checkTplVM.getSex()));
                    if (StringUtils.isEmpty(checkTplVM.getSex())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "性别格式不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 检查民族
                if (StringUtils.isNotEmpty(checkTplVM.getNation())) {  
                    checkTplVM.setNation(DictUtils.getDictLabel("biz_person_nation", checkTplVM.getNation()));
                    if (StringUtils.isEmpty(checkTplVM.getNation())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "民族格式不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 出生年月
                if (StringUtils.isNotEmpty(checkTplVM.getBirthday())) {
                    Date birthday = DateUtils.parseDate(checkTplVM.getBirthday());
                    if (birthday == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "出生年月不正确，正确示例: 1998-01");
                        continue;
                    }
                }

                // 婚姻状况
                if (StringUtils.isNotEmpty(checkTplVM.getMaritalStatus())) {  
                    checkTplVM.setMaritalStatus(DictUtils.getDictLabel("biz_person_marital", checkTplVM.getMaritalStatus()));
                    if (StringUtils.isEmpty(checkTplVM.getMaritalStatus())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "婚姻状况不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 健康状况
                if (StringUtils.isNotEmpty(checkTplVM.getHealth())) { 
                    checkTplVM.setHealth(DictUtils.getDictLabel("biz_person_health", checkTplVM.getHealth()));
                    if (StringUtils.isEmpty(checkTplVM.getHealth())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "健康状况不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 个人身份特殊标识
                if (StringUtils.isNotEmpty(checkTplVM.getIdentityFlag())) {
                    checkTplVM.setIdentityFlag(DictUtils.getDictLabel("biz_person_identity_flag", checkTplVM.getIdentityFlag()));
                    if (StringUtils.isEmpty(checkTplVM.getIdentityFlag())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "个人身份特殊标识不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 领导班子成员标识
                if (StringUtils.isNotEmpty(checkTplVM.getLeaderFlag())) {
                    if (StringUtils.isEmpty(DictUtils.getDictLabel("sys_yes_no", checkTplVM.getLeaderFlag()))) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "领导班子成员标识不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 全国优秀共产党员
                if (StringUtils.isNotEmpty(checkTplVM.getExcellentMemberOfCpc())) {
                    if (StringUtils.isEmpty(DictUtils.getDictLabel("sys_yes_no", checkTplVM.getExcellentMemberOfCpc()))) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "全国优秀共产党员不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 全国优秀县委书记
                if (StringUtils.isNotEmpty(checkTplVM.getExcellentMemberOfCps())) {
                    if (StringUtils.isEmpty(DictUtils.getDictLabel("sys_yes_no", checkTplVM.getExcellentMemberOfCps()))) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "全国优秀县委书记不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 是否援疆干部
                if (StringUtils.isNotEmpty(checkTplVM.getSupportFrontierMember())) {
                    if (StringUtils.isEmpty(DictUtils.getDictLabel("sys_yes_no", checkTplVM.getSupportFrontierMember()))) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "是否援疆干部不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 参加工作时间
                if (StringUtils.isNotEmpty(checkTplVM.getJobDay())) {
                    Date birthday = DateUtils.parseDate(checkTplVM.getJobDay());
                    if (birthday == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "参加工作时间不正确，正确示例: 1998-01");
                        continue;
                    }
                }

                // 进入农信社工作日期
                if (StringUtils.isNotEmpty(checkTplVM.getCivilServantRegisterDay())) {
                    Date birthday = DateUtils.parseDate(checkTplVM.getCivilServantRegisterDay());
                    if (birthday == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "进入农信社工作日期不正确，正确示例: 1998-01");
                        continue;
                    }
                }

                // 进入本系统工作日期
                if (StringUtils.isNotEmpty(checkTplVM.getJoinSystemDay())) {
                    Date birthday = DateUtils.parseDate(checkTplVM.getJoinSystemDay());
                    if (birthday == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "进入本系统工作日期不正确，正确示例: 1998-01");
                        continue;
                    }
                }

                // 是否有基层工作经历
                if (StringUtils.isNotEmpty(checkTplVM.getExperienceInBase())) {
                    if (StringUtils.isEmpty(DictUtils.getDictLabel("sys_yes_no", checkTplVM.getExperienceInBase()))) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "是否有基层工作经历不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 人员管理类别
                if (StringUtils.isNotEmpty(checkTplVM.getManageCategory())) {
                    checkTplVM.setManageCategory(DictUtils.getDictLabel("biz_person_manage_category", checkTplVM.getManageCategory()));
                    if (StringUtils.isEmpty(checkTplVM.getManageCategory())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "人员管理类别不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 人员管理状态
                if (StringUtils.isNotEmpty(checkTplVM.getManageStatus())) {
                    if (StringUtils.isEmpty(DictUtils.getDictLabel("biz_person_manage_status", checkTplVM.getManageStatus()))) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "人员管理状态不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 去世时间
                if (StringUtils.isNotEmpty(checkTplVM.getDeathDay())) {
                    Date birthday = DateUtils.parseDate(checkTplVM.getDeathDay());
                    if (birthday == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "去世时间不正确，正确示例: 1998-01");
                        continue;
                    }
                }

                // 免职时间
                if (StringUtils.isNotEmpty(checkTplVM.getDismissDay())) {
                    Date birthday = DateUtils.parseDate(checkTplVM.getDismissDay());
                    if (birthday == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "免职时间不正确，正确示例: 1998-01");
                        continue;
                    }
                }

                // 离岗退养标识
                if (StringUtils.isNotEmpty(checkTplVM.getRetireLeaveFlag())) {
                    if (StringUtils.isEmpty(DictUtils.getDictLabel("sys_yes_no", checkTplVM.getRetireLeaveFlag()))) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "离岗退养标识不正确，请从下拉列表中选中相应值");
                        continue;
                    }
                }

                // 离岗退养时间
                if (StringUtils.isNotEmpty(checkTplVM.getRetireLeaveDay())) {
                    Date birthday = DateUtils.parseDate(checkTplVM.getRetireLeaveDay());
                    if (birthday == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "离岗退养时间不正确，正确示例: 1998-01");
                        continue;
                    }
                }
            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if (StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException) {
                    exMsg = ((InvocationTargetException) e).getTargetException().getMessage();
                }
                if (StringUtils.isEmpty(exMsg)) {
                    exMsg = "数据校验失败";
                }
                checkTplVM.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }
}
