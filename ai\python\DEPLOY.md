# 新疆金融监管局行政处罚信息自动化抓取和处理系统

## 项目简介

本项目是一个金融监管处罚信息自动化抓取和处理系统

## 安装和配置

### 1. 安装依赖

```bash
# 1. 安装uv

# Windows
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"

# MacOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 使用uv安装依赖
uv sync; uv sync --extra dev;

# 安装playwright浏览器
uv run playwright install chromium
```

### 2. 环境配置

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑`.env`文件，配置数据库、RabbitMQ和OpenAI API等信息。

## 使用方法

### 快速启动

#### 方式1: 使用启动脚本

**Linux/macOS:**

```bash
# 在线同步
chmod +x scripts/start.sh
./scripts/start.sh

# 手工同步，使用启动脚本导出Excel
./scripts/start.sh --export data.xlsx        # Linux/macOS
scripts\start.bat --export data.xlsx         # Windows

```

**Windows:**

```cmd
scripts\start.bat
```

#### 方式2: 手动启动

```bash

# 在线同步
uv run python -m nfra_crawler.main

# 手工同步(导出需同步的处罚记录到当前目录的punish_data.xlsx文件)
uv run python -m nfra_crawler.main --export punish_data.xlsx
```
