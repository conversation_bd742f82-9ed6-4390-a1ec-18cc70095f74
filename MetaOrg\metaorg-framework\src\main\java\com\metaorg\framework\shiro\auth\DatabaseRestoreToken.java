package com.metaorg.framework.shiro.auth;

import com.metaorg.common.core.domain.entity.SysUser;
import org.apache.shiro.authc.UsernamePasswordToken;

/**
 * 数据库Token
 * 继承UsernamePasswordToken以确保与现有Realm兼容
 * 
 * <AUTHOR>
 */
public class DatabaseRestoreToken extends UsernamePasswordToken {
    
    private static final long serialVersionUID = 1L;
    
    private SysUser user;
    
    public DatabaseRestoreToken(String loginName, SysUser user) {
        super(loginName, "", false);
        this.user = user;
    }
    
    public SysUser getUser() {
        return user;
    }
    
    public String getLoginName() {
        return getUsername();
    }
    
    @Override
    public String toString() {
        return "DatabaseRestoreToken{" +
                "loginName='" + getUsername() + '\'' +
                ", userId=" + (user != null ? user.getUserId() : null) +
                '}';
    }
}
