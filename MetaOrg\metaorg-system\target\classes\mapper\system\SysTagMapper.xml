<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.system.mapper.SysTagMapper">

    <resultMap type="SysTag" id="SysTagResult">
        <id     property="id"         column="id"        />
        <result property="parentId"   column="parent_id"  />
		<result property="parentName" column="parent_name" />
        <result property="ancestors"  column="ancestors"  />
        <result property="name"       column="name"      />
        <result property="code"       column="code"      />
        <result property="value"      column="value"     />
        <result property="status"     column="status"    />
        <result property="dataType"   column="data_type" />
        <result property="renderType" column="render_type" />
        <result property="score"      column="score"     />
        <result property="cssClass"   column="css_class" />
        <result property="orderNum"   column="order_num" />
        <result property="description" column="description" />
        <result property="createBy"   column="create_by" />
        <result property="createById"   column="create_by_id" />
        <result property="createTime" column="create_time" />
        <result property="updateBy"   column="update_by" />
        <result property="updateById"   column="update_by_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

	<resultMap type="SysTag" id="SysTagResultChild">
		<id     property="id"     column="child_id"     />
		<result property="parentId"   column="child_parent_id"   />
		<result property="ancestors"  column="child_ancestors"   />
		<result property="name"   column="child_name"   />
		<result property="code"   column="child_code"   />
		<result property="value"   column="child_value"   />
		<result property="status"   column="child_status"   />
		<result property="dataType"   column="child_data_type"   />
		<result property="renderType"   column="child_render_type"   />
		<result property="score"   column="child_score"   />
		<result property="cssClass"   column="child_css_class"   />
		<result property="orderNum"   column="child_order_num"   />
		<result property="description"     column="child_description"      />
		<result property="parentName" column="child_parent_name" />
		<result property="parentCode" column="child_parent_code" />
		<result property="createBy"   column="child_create_by"   />
		<result property="createById"   column="child_create_by_id"   />
		<result property="createTime" column="child_create_time" />
		<result property="updateBy"   column="child_update_by"   />
		<result property="updateById"   column="child_update_by_id"   />
		<result property="updateTime" column="child_update_time" />
	</resultMap>

    <sql id="tagHierarchyCTE">
    with recursive tag_hierarchy as (
        select
            id as tag_id,
            id as root_tag_id,
            name as root_tag_name,
            order_num as root_tag_order_num
        from
            sys_tag
        where
            status = 1
            <if test="params.parentCodeList != null and params.parentCodeList != ''"> and code in 
                <foreach item="item" index="index" collection="params.parentCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        union all

        select
            t.id as tag_id,
            th.root_tag_id,
            th.root_tag_name,
            th.root_tag_order_num
        from
            sys_tag t
        inner join
            tag_hierarchy th on t.parent_id = th.tag_id
        where
            t.status = 1
    )
    </sql>

	<resultMap type="SysTag" id="SysTagTreeResult" extends="SysTagResult">
		<collection  property="childs"  column="id" javaType="java.util.List"  foreignColumn="parent_id" resultMap="SysTagResultChild" />
	</resultMap>

	<sql id="selectTagFields">
		select t.id, t.parent_id, t.ancestors, t.name, t.code, t.value, t.score, t.css_class, t.order_num, t.description, t.create_by, t.create_time, t.update_by, t.update_time,
			t.status, t.data_type, t.render_type
	</sql>

	<sql id="selectChildTagFields">
		 ,cd.id as child_id, cd.parent_id as child_parent_id, cd.ancestors as child_ancestors, cd.name as child_name, cd.code as child_code,
			   cd.value as child_value, cd.score as child_score, cd.css_class as child_css_class, cd.order_num as child_order_num, cd.description as child_description,
			   cd.create_by as child_create_by, cd.create_by_id as child_create_by_id, cd.create_time as child_create_time, cd.update_by as child_update_by,
			   cd.update_by_id as child_update_by_id, cd.update_time as child_update_time,
			   cd.status as child_status, cd.data_type as child_data_type, cd.render_type as child_render_type
	</sql>
    
    <sql id="selectTagVo">
		<include refid="selectTagFields"/>
        from sys_tag t
    </sql>

	<sql id="selectTagTreeVo">
		<include refid="selectTagFields"/>
		<include refid="selectChildTagFields"/>
		from sys_tag t
		left join sys_tag cd on cd.parent_id = t.id
	</sql>
    
    <select id="selectTagList" parameterType="SysTag" resultMap="SysTagResult">
        <if test="params.parentCodeList != null and params.parentCodeList.length > 0">
            <include refid="tagHierarchyCTE"/>
        </if>
        <include refid="selectTagVo"/>
        <where>
            <if test="params.keyword != null and params.keyword != ''"> AND 
            (
                t.name like concat('%', #{params.keyword}, '%') or
                t.code like concat('%', #{params.keyword}, '%') or
                t.value like concat('%', #{params.keyword}, '%') or
                t.description like concat('%', #{params.keyword}, '%')
            )
            </if>
            <if test="parentId != null and parentId != ''">
                AND t.parent_id = #{parentId}
            </if>
            <if test="name != null and name != ''">
                AND t.name like concat('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND t.code = #{code}
            </if>
            <if test="value != null and value != ''">
                AND t.value like concat('%', #{value}, '%')
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="parentCode != null and parentCode != ''">
                AND EXISTS (SELECT 1 FROM sys_tag b WHERE b.code = #{parentCode} AND b.id = t.parent_id)
            </if>
            <if test="params.parentCodeList != null and params.parentCodeList.length > 0">
                AND exists (select 1 from tag_hierarchy th where th.tag_id = t.id)
            </if>
        </where>
        order by t.parent_id, t.order_num
    </select>
    
    <select id="selectTagTreeList" parameterType="SysTag" resultMap="SysTagTreeResult">
        <if test="params.parentCodeList != null and params.parentCodeList.length > 0">
            <include refid="tagHierarchyCTE"/>
        </if>
        <include refid="selectTagTreeVo"/>
        <where>
            <if test="params.keyword != null and params.keyword != ''"> AND 
            (
                t.name like concat('%', #{params.keyword}, '%') or
                t.code like concat('%', #{params.keyword}, '%') or
                t.value like concat('%', #{params.keyword}, '%') or
                t.description like concat('%', #{params.keyword}, '%')
            )
            </if>
            <if test="parentId != null and parentId != ''">
                AND t.parent_id = #{parentId}
            </if>
            <if test="name != null and name != ''">
                AND t.name like concat('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND t.code like concat('%', #{code}, '%')
            </if>
            <if test="value != null and value != ''">
                AND t.value like concat('%', #{value}, '%')
            </if>
            <if test="status != null">
                AND t.status = #{status} 
                AND cd.status = #{status}
            </if>
            <if test="parentCode != null and parentCode != ''">
                AND EXISTS (SELECT 1 FROM sys_tag b WHERE b.code = #{parentCode} AND b.id = t.parent_id)
            </if>
            <if test="params.parentCodeList != null and params.parentCodeList.length > 0">
                AND exists (select 1 from tag_hierarchy th where th.tag_id = t.id)
            </if>
        </where>
        order by t.parent_id, t.order_num, cd.order_num
    </select>
    
    <select id="selectTagById" parameterType="String" resultMap="SysTagResult">
        select 
            id, parent_id, ancestors, name, code, value, score, css_class, order_num, description, create_by, create_time, update_by, update_time, 
            data_type, render_type, status,
            (select name from sys_tag where id = t.parent_id) parent_name
        from 
            sys_tag t
        where id = #{id}
    </select>
    
    <select id="selectTagByIds" parameterType="String" resultMap="SysTagResult">
        <include refid="selectTagVo"/>
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    
    <select id="selectTagCount" parameterType="SysTag" resultType="int">
        select count(1) from sys_tag
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="parentId != null and parentId != ''">
                AND parent_id = #{parentId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>
    
    <select id="checkTagCodeUnique" resultMap="SysTagResult">
        select id from sys_tag where code = #{code} limit 1
    </select>
    
    <select id="selectChildrenTagById" parameterType="String" resultMap="SysTagResult">
        <include refid="selectTagVo"/>
        where find_in_set(#{id}, ancestors)
    </select>
    
    <insert id="insertTag" parameterType="SysTag">
 		insert into sys_tag
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null and id != ''">id,</if>
 			<if test="parentId != null and parentId != ''">parent_id,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="name != null and name != ''">name,</if>
			<if test="code != null and code != ''">code,</if>
            <if test="value != null and value != ''">value,</if>
            <if test="status != null">status,</if>
            <if test="dataType != null and dataType != ''">data_type,</if>
            <if test="renderType != null and renderType != ''">render_type,</if>
            <if test="score != null">score,</if>
            <if test="cssClass != null and cssClass != ''">css_class,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="description != null and description != ''">description,</if>
			<if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createById != null and createById != ''">create_by_id,</if>
			<if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateById != null and updateById != ''">update_by_id,</if>
            <if test="updateTime != null">update_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
 			<if test="id != null and id != ''">#{id},</if>
 			<if test="parentId != null and parentId != ''">#{parentId},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="name != null and name != ''">#{name},</if>
			<if test="code != null and code != ''">#{code},</if>
            <if test="value != null and value != ''">#{value},</if>
            <if test="status != null">#{status},</if>
            <if test="dataType != null and dataType != ''">#{dataType},</if>
            <if test="renderType != null and renderType != ''">#{renderType},</if>
            <if test="score != null">#{score},</if>
            <if test="cssClass != null and cssClass != ''">#{cssClass},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="description != null and description != ''">#{description},</if>
			<if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createById != null and createById != ''">#{createById},</if>
			<if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateById != null and updateById != ''">#{updateById},</if>
            <if test="updateTime != null">#{updateTime},</if>
		</trim>
    </insert>
    
    <update id="updateTag" parameterType="SysTag">
        update sys_tag
        <set>
            <if test="parentId != null and parentId != ''">parent_id = #{parentId},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="value != null">value = #{value},</if>
            <if test="status != null">status = #{status},</if>
            <if test="dataType != null and dataType != ''">data_type = #{dataType},</if>
            <if test="renderType != null and renderType != ''">render_type = #{renderType},</if>
            <if test="score != null">score = #{score},</if>
            <if test="cssClass != null">css_class = #{cssClass},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateById != null and updateById != ''">update_by_id = #{updateById},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        where id = #{id}
    </update>
    
    <update id="updateTagChildren" parameterType="java.util.List">
        update sys_tag set ancestors =
        <foreach collection="tags" item="item" index="index"
                 separator=" " open="case id" close="end">
            when #{item.id} then #{item.ancestors}
        </foreach>
        where id in
        <foreach collection="tags" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    
    <delete id="deleteTagById" parameterType="String">
        delete from sys_tag where id = #{id}
    </delete>
	
 	<delete id="deleteTagByIds">
 		delete from sys_tag where id in
 		<foreach item="id" collection="array" open="(" separator="," close=")">
 			#{id}
        </foreach> 
 	</delete>
	
	<update id="updateTagStatusNormal" parameterType="String">
 	    update sys_tag set status = 1 where id in
 	    <foreach collection="array" item="tagId" open="(" separator="," close=")">
        	#{tagId}
        </foreach>
	</update>

</mapper> 