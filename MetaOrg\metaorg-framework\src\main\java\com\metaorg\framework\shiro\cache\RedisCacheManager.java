package com.metaorg.framework.shiro.cache;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import org.apache.shiro.cache.CacheManager;
import org.springframework.beans.factory.annotation.Value;
import com.metaorg.framework.redis.NamespacedRedisTemplate;

import com.metaorg.common.utils.spring.SpringUtils;

/**
 * 缓存管理器 使用redis实现
 * 
 * <AUTHOR>
 */
@SuppressWarnings(value = { "unchecked", "rawtypes" })
public class RedisCacheManager implements CacheManager
{
    /**
     * 用于shiro中用到的cache
     */
    private ConcurrentMap<String, Cache> caches = new ConcurrentHashMap<>();

    /**
     * redis cache 工具类
     */
    private NamespacedRedisTemplate<String, Object> namespacedRedisTemplate = SpringUtils.getBean("namespacedRedisTemplate");

    /**
     * Session超时时间，单位为分钟（-1表示24小时，未设置默认30分钟）
     */
    @Value("${shiro.session.expireTime:30}")
    private int expireTime;

    /**
     * 缓存过期时间配置
     */
    private static final long PERMANENT_EXPIRE = 0;           // 永不过期
    private static final long LOGIN_RECORD_EXPIRE = 600;      // 登录记录10分钟
    private static final long DEFAULT_EXPIRE = 3600;          // 默认1小时

    @Override
    public <K, V> Cache<K, V> getCache(String name) throws CacheException
    {
        Cache cache = caches.get(name);
        if (cache == null)
        {
            synchronized (this)
            {
                // 双重检查锁定模式
                cache = caches.get(name);
                if (cache == null)
                {
                    // 根据缓存名称设置不同的过期时间
                    long expireTime = getExpireTime(name);
                    cache = new RedisCache<String, Object>(name, expireTime, namespacedRedisTemplate);
                    caches.put(name, cache);
                }
            }
        }
        return (Cache<K, V>) cache;
    }

    /**
     * 根据缓存名称获取过期时间
     * @param cacheName 缓存名称
     * @return 过期时间（秒）
     */
    private long getExpireTime(String cacheName) {
        switch (cacheName) {
            case "sys-authCache":
            case "sys-config":
            case "sys-dict":
                return PERMANENT_EXPIRE;  // 永不过期
            case "loginRecordCache":
                return LOGIN_RECORD_EXPIRE;  // 10分钟
            case "shiro-activeSessionCache":
                // 根据session配置设置过期时间：-1表示24小时，其他值按分钟计算
                if (expireTime == -1) {
                    return 24 * 60 * 60; // 24小时
                } else {
                    return expireTime * 60; // 按分钟转换为秒
                }
            default:
                return DEFAULT_EXPIRE;  // 1小时
        }
    }

    /**
     * 获取所有缓存名称
     * @return 缓存名称数组
     */
    public String[] getCacheNames()
    {
        // 预定义的缓存名称列表，与EhCache配置保持一致
        return new String[] {
            "sys-config",
            "sys-dict", 
            "sys-cache",
            "sys-authCache",
            "sys-userCache",
            "loginRecordCache",
            "appValidateCodeCache",
            "shiro-activeSessionCache"
        };
    }

}
