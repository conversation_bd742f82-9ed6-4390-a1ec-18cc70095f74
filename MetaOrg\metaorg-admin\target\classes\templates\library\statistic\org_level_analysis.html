<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <th:block th:include="include :: header('干部统计分析')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <style>
      .col-left {
        border-right: 1px solid #ddd;
      }
      .gray-bg {
        padding-right: 0 !important;
      }
      .chart-container {
        height: 300px;
      }
      .knowledge-chart-container {
        height: 400px;
      }
    </style>
  </head>
  <body class="gray-bg">
    <div class="ui-layout-west">
      <div class="box box-main">
        <div class="box-header">
          <div class="box-title"><i class="fa fa-sitemap"></i> 组织机构</div>
          <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display: none"><i class="fa fa-chevron-up"></i></button>
            <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
            <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
          </div>
        </div>
        <div class="ui-layout-content">
          <input name="treeId" type="hidden" id="treeId" th:value="${deptId}" />
          <div id="tree" class="ztree"></div>
        </div>
      </div>
    </div>
    <div class="ui-layout-center">
      <input type="hidden" id="deptId" name="deptId" th:value="${deptId}" />
      <div class="wrapper wrapper-content">
        <!--总体情况-->
        <div class="row">
          <div class="col-sm-12">
            <div class="ibox float-e-margins">
              <div class="ibox-title">
                <h5>统计图表：总体情况</h5>
              </div>
              <div class="ibox-content">
                <div class="row">
                  <div class="col-sm-6 col-left">
                    <div id="overall_chart" class="chart-container"></div>
                  </div>
                  <div class="col-sm-6 col-left">
                    <div id="leader_team_composition_chart" class="chart-container"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--班子配备-->
        <div class="row">
          <div class="col-sm-12">
            <div class="ibox float-e-margins">
              <div class="ibox-title">
                <h5>统计图表：班子配备</h5>
              </div>
              <div class="ibox-content">
                <div class="row">
                  <div class="col-sm-4 col-left">
                    <div id="leader_team_gender_chart" class="chart-container"></div>
                  </div>
                  <div class="col-sm-4">
                    <div id="middle_team_gender_chart" class="chart-container"></div>
                  </div>
                  <div class="col-sm-4">
                    <div id="other_team_gender_chart" class="chart-container"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--专业知识-->
        <div class="row">
          <div class="col-sm-12">
            <div class="ibox float-e-margins">
              <div class="ibox-title">
                <h5>统计图表：专业知识分布</h5>
              </div>
              <div class="ibox-content">
                <div class="row">
                  <div class="col-sm-12">
                    <div id="professional_chart" class="knowledge-chart-container"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <th:block th:include="include :: echarts-js" />
    <script th:inline="javascript">
      /*<![CDATA[*/
      var ctx = /*[[@{/}]]*/ "";
      /*]]>*/

      function queryDeptTree() {
        var url = ctx + 'system/user/deptTreeData';
        var options = {
          url: url,
          expandLevel: 1,
          onClick: zOnClick,
        };
        $.tree.init(options);

        function zOnClick(event, treeId, treeNode) {
          $('#deptId').val(treeNode.id);
          $('#parentId').val(treeNode.pId);
          handlerConfirmRemove();
          selectedList = [];
          $.table.search();
          $('#selected-num').html(0);
        }
      }

      $(function () {
        var panehHidden = false;
        if ($(this).width() < 769) {
          panehHidden = true;
        }
        var libraryType = $('#libraryType').val();
        $('#personnelType').val(libraryType);

        $('body').layout({ initClosed: panehHidden, west__size: 255 });
        // 回到顶部绑定
        if ($.fn.toTop !== undefined) {
          var opt = {
            win: $('.ui-layout-center'),
            doc: $('.ui-layout-center'),
          };
          $('#scroll-up').toTop(opt);
        }
        queryDeptTree();
        $('#btnExpand').click(function () {
          $._tree.expandAll(true);
          $(this).hide();
          $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function () {
          $._tree.expandAll(false);
          $(this).hide();
          $('#btnExpand').show();
        });

        $('#btnRefresh').click(function () {
          queryDeptTree();
        });
        var overallChart = echarts.init(document.getElementById('overall_chart'));
        var leaderTeamCompositionChart = echarts.init(document.getElementById('leader_team_composition_chart'));
        var leaderTeamGenderChart = echarts.init(document.getElementById('leader_team_gender_chart'));
        var middleTeamGenderChart = echarts.init(document.getElementById('middle_team_gender_chart'));
        var otherTeamGenderChart = echarts.init(document.getElementById('other_team_gender_chart'));
        var professionalChart = echarts.init(document.getElementById('professional_chart'));

        var deptId = $('#deptId').val();
        var url = ctx + 'library/statistic/orgs/' + deptId + '/levels';
        $.get(url, function (result) {
          // 总体情况 - 显示层级分布（班子、中层、其他）
          var data = {};
          if (result.data) {
            data = result.data;
          }

          var totalCount = data.total_count || 0;
          var assignedPositionCount = data.assigned_position_count || 0;
          var totalMaleCount = data.total_male_count || 0;
          var totalFemaleCount = data.total_female_count || 0;
          var leaderTeamCount = data.leader_count || 0;
          var middleTeamCount = data.middle_count || 0;
          var otherTeamCount = totalCount - leaderTeamCount - middleTeamCount;

          var pieData = [
            {
              name: '班子',
              value: leaderTeamCount,
              percentage: ((leaderTeamCount / totalCount) * 100).toFixed(2),
            },
            {
              name: '中层',
              value: middleTeamCount,
              percentage: ((middleTeamCount / totalCount) * 100).toFixed(2),
            },
            {
              name: '其他',
              value: otherTeamCount,
              percentage: ((otherTeamCount / totalCount) * 100).toFixed(2),
            },
          ];

          var overallOption = {
            title: {
              text: '总体情况',
              left: 'left',
            },
            tooltip: {
              trigger: 'item',
            },
            grid: {
              x: 30,
              x2: 40,
              y2: 24,
            },
            color: ['#525CBF', '#A0CB7E', '#E68C59'],
            legend: {
              left: 'right',
            },
            series: [
              {
                name: '总体情况',
                type: 'pie',
                data: pieData,
                label: {
                  formatter: function (data) {
                    return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          };

          overallChart.setOption(overallOption);

          // 班子配备
          var leaderTeamOmissionCount = assignedPositionCount - leaderTeamCount;
          if (leaderTeamOmissionCount < 0) {
            leaderTeamOmissionCount = 0;
          }

          var leaderTeamPieData = [
            {
              name: '班子',
              value: leaderTeamCount,
              percentage: ((leaderTeamCount / assignedPositionCount) * 100).toFixed(2),
            },
            {
              name: '缺位',
              value: leaderTeamOmissionCount,
              percentage: ((leaderTeamOmissionCount / assignedPositionCount) * 100).toFixed(2),
            },
          ];

          var leaderTeamCompositionOption = {
            title: {
              text: '班子配备情况',
              left: 'left',
            },
            tooltip: {
              trigger: 'item',
            },
            grid: {
              x: 30,
              x2: 40,
              y2: 24,
            },
            color: ['#525CBF', '#A0CB7E', '#E68C59'],
            legend: {
              left: 'right',
            },
            series: [
              {
                name: '班子配备情况',
                type: 'pie',
                data: leaderTeamPieData,
                label: {
                  formatter: function (data) {
                    return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          };

          leaderTeamCompositionChart.setOption(leaderTeamCompositionOption);

          // 班子性别
          var leaderTeamMaleCount = data.leader_male_count || 0;
          var leaderTeamFemaleCount = data.leader_female_count || 0;

          var leaderTeamPieData = [
            {
              name: '男',
              value: leaderTeamMaleCount,
              percentage: leaderTeamCount > 0 ? ((leaderTeamMaleCount / leaderTeamCount) * 100).toFixed(2) : '0.00',
            },
            {
              name: '女',
              value: leaderTeamFemaleCount,
              percentage: leaderTeamCount > 0 ? ((leaderTeamFemaleCount / leaderTeamCount) * 100).toFixed(2) : '0.00',
            },
          ];

          var leaderTeamOption = {
            title: {
              text: '班子性别分布',
              left: 'left',
            },
            tooltip: {
              trigger: 'item',
            },
            grid: {
              x: 30,
              x2: 40,
              y2: 24,
            },
            color: ['#525CBF', '#A0CB7E'],
            legend: {
              left: 'right',
            },
            series: [
              {
                name: '班子性别分布',
                type: 'pie',
                data: leaderTeamPieData,
                label: {
                  formatter: function (data) {
                    return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          };
          leaderTeamGenderChart.setOption(leaderTeamOption);

          // 中层配备
          var middleLevelMaleCount = data.middle_male_count || 0;
          var middleLevelFemaleCount = data.middle_female_count || 0;

          var middleLevelPieData = [
            {
              name: '男',
              value: middleLevelMaleCount,
              percentage: middleTeamCount > 0 ? ((middleLevelMaleCount / middleTeamCount) * 100).toFixed(2) : '0.00',
            },
            {
              name: '女',
              value: middleLevelFemaleCount,
              percentage: middleTeamCount > 0 ? ((middleLevelFemaleCount / middleTeamCount) * 100).toFixed(2) : '0.00',
            },
          ];

          var middleLevelOption = {
            title: {
              text: '中层性别分布',
              left: 'left',
            },
            tooltip: {
              trigger: 'item',
            },
            grid: {
              x: 30,
              x2: 40,
              y2: 24,
            },
            color: ['#525CBF', '#A0CB7E'],
            legend: {
              left: 'right',
            },
            series: [
              {
                name: '中层性别分布',
                type: 'pie',
                data: middleLevelPieData,
                label: {
                  formatter: function (data) {
                    return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          };
          middleTeamGenderChart.setOption(middleLevelOption);

          // 其他配备
          var otherLevelMaleCount = totalMaleCount - leaderTeamMaleCount - middleLevelMaleCount;
          var otherLevelFemaleCount = totalFemaleCount - leaderTeamFemaleCount - middleLevelFemaleCount;

          var otherLevelPieData = [
            {
              name: '男',
              value: otherLevelMaleCount,
              percentage: otherTeamCount > 0 ? ((otherLevelMaleCount / otherTeamCount) * 100).toFixed(2) : '0.00',
            },
            {
              name: '女',
              value: otherLevelFemaleCount,
              percentage: otherTeamCount > 0 ? ((otherLevelFemaleCount / otherTeamCount) * 100).toFixed(2) : '0.00',
            },
          ];

          var otherLevelOption = {
            title: {
              text: '其他性别分布',
              left: 'left',
            },
            tooltip: {
              trigger: 'item',
            },
            grid: {
              x: 30,
              x2: 40,
              y2: 24,
            },
            color: ['#525CBF', '#A0CB7E'],
            legend: {
              left: 'right',
            },
            series: [
              {
                name: '其他性别分布',
                type: 'pie',
                data: otherLevelPieData,
                label: {
                  formatter: function (data) {
                    return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          };
          otherTeamGenderChart.setOption(otherLevelOption);

          // 专业知识分布
          var professionalData = data.professional;
          if (professionalData && Array.isArray(professionalData) && professionalData.length > 0) {
            var keys = professionalData.map(function (item) {
              return item.name || '未知';
            });
            var values = professionalData.map(function (item) {
              return item.total_count || 0;
            });

            var dataMax = Math.max(...values);
            var yAxisMax = dataMax <= 1 ? 2 : Math.ceil(dataMax * 1.3);

            var professionalOption = {
              title: {
                text: '专业知识分布',
                left: 'left',
              },
              tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c}人',
              },
              legend: {
                left: 'right',
                data: ['数量'],
              },
              color: ['#E67E9A'],
              grid: {
                x: 30,
                x2: 40,
                y2: 24,
                containLabel: true,
              },
              calculable: true,
              xAxis: [
                {
                  type: 'category',
                  data: keys,
                  axisLabel: {
                    interval: 0,
                    rotate: keys.length > 6 ? 45 : 0,
                  },
                },
              ],
              yAxis: [
                {
                  type: 'value',
                  minInterval: 1,
                  max: yAxisMax,
                  axisLine: {
                    show: true,
                  },
                },
              ],
              series: [
                {
                  name: '数量',
                  barWidth: 30,
                  type: 'bar',
                  data: values,
                  itemStyle: {
                    borderRadius: [4, 4, 0, 0],
                    shadowColor: 'rgba(0, 0, 0, 0.2)',
                    shadowBlur: 4,
                    shadowOffsetY: 2,
                  },
                  label: {
                    show: true,
                    position: 'top',
                  },
                },
              ],
            };

            professionalChart.setOption(professionalOption);
          }
          // 响应式调整
          window.onresize = function () {
            overallChart.resize();
            leaderTeamGenderChart.resize();
            middleTeamGenderChart.resize();
            professionalChart.resize();
          };
        });
      });
    </script>
  </body>
</html>
