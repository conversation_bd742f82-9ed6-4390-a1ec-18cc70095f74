<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <th:block th:include="include :: header('数据同步-处罚公示列表')" />
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .select-table .table td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
        div.select-list li label:not(.radio-box) { width: 150px; }
        div.select-list li input, div.select-list li select { width: 260px; }
        div.select-list .select-time input { width: 122px; }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>关键字：</label>
                                <input type="text" name="params[keyword]" placeholder="请输入关键字，支持模糊查询" data-toggle="tooltip" data-placement="bottom" title="支持按当事人名称、当事人时任职务、作出决定机关、处罚决定书文号、处罚内容等模糊查询"/>
                            </li>
                            <li>
                                <label for="partyName">当事人名称：</label>
                                <input type="text" id="partyName" name="partyName" placeholder="请输入当事人名称，支持模糊查询"/>
                            </li>
                            <li>
                              <label>当事人类型： </label>
                              <select id="partyType" name="partyType" title="按当事人类型查询">
                                <option value="" selected>所有</option>
                                <option value="个人">个人</option>
                                <option value="机构">机构</option>
                              </select>
                            </li>
                            <li>
                                <label for="partyPosition">当事人时任职务：</label>
                                <input type="text" id="partyPosition" name="partyPosition" class="input-long" placeholder="请输入当事人时任职务，支持模糊查询" width="150"/>
                            </li>
                            <li>
                                <label for="punishDocNo">行政处罚决定书文号：</label>
                                <input type="text" id="punishDocNo" name="punishDocNo" placeholder="请输入行政处罚决定书文号，支持模糊查询"/>
                            </li>
                            <li>
                                <label for="decisionAuthority">作出决定机关：</label>
                                <input type="text" id="decisionAuthority" name="decisionAuthority" placeholder="请输入作出决定机关，支持模糊查询"/>
                            </li>
                            <li class="select-time">
                                <label>作出处罚决定的日期：</label>
                                <input type="text" class="time-input" id="punishDateBegin" data-type="date"
                                       placeholder="开始日期" name="params[punishDateBegin]" title="开始日期"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="punishDateEnd" data-type="date"
                                       placeholder="结束日期" name="params[punishDateEnd]" title="结束日期"/>
                            </li>
                            <li class="select-time">
                                <label>发布日期：</label>
                                <input type="text" class="time-input" id="publishedDateBegin" data-type="date"
                                       placeholder="开始日期" name="params[publishedDateBegin]" title="开始日期"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="publishedDateEnd" data-type="date"
                                       placeholder="结束日期" name="params[publishedDateEnd]" title="结束日期"/>
                            </li>
                            <li class="select-selectpicker">
                                <label>人员匹配状态：</label>
                                <select name="bindStatusList" class="selectpicker" title="按人员匹配状态查询" th:with="type=${@dict.getType('biz_sync_punish_bind_status')}"
                                        data-actions-box="true" data-select-all-text="选择所有"
                                        data-deselect-all-text="取消选择" data-live-search="true"
                                        data-none-selected-text="请选择" multiple>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="searchFormReset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="nfra-punish-toolbar" role="group">
                <a class="btn btn-success" onclick="manualSync()" shiro:hasPermission="library:nfra_punish:sync">
                    <i class="fa fa-sync"></i> 同步处罚公示
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-resizable="true" data-use-row-attr-func="true"></table>
            </div>
        </div>
    </div>
    
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js"/>
    <th:block th:include="include :: bootstrap-select-js" />
    <th:block th:include="include :: bootstrap-table-reorder-rows-js" />
    <th:block th:include="include :: bootstrap-table-resizable-js" />
    <th:block th:include="include :: bootstrap-table-fixed-columns-js" />
    <script th:src="@{/js/moment.min.js}"></script>
    <script th:inline="javascript">
        /*<![CDATA[*/
        const editFlag = /*[[${@permission.hasPermi('library:nfra_punish:edit')}]]*/ false;
        const bindStatusDict = /*[[${@dict.getType('biz_sync_punish_bind_status')}]]*/ [];
        const prefix = ctx + "library/reward_punish/nfra_punish";
        
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();

            $(document).on('keypress', function(e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    $.table.search();
                }
            });
        });

        $(function() {
            const options = {
                toolbar: "nfra-punish-toolbar",
                url: prefix + "/list",
                modalName: "处罚公示",
                showToggle: false, 
                fixedColumns: true,
                fixedNumber: 1,
                fixedRightNumber: 1,
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                columns: [
                    {
                        field: 'id',
                        title: 'ID',
                        visible: false
                    },
                    {
                        field: 'partyName',
                        title: '当事人名称',
                        width: 10,
                        widthUnit: '%',
                        formatter: function(value, row, index) {
                            if (row.bindPersonId && row.bindStatus == 'confirmed') {
                                return `<a href="javascript:void(0)" title="查看资料" onclick="showPersonDetail('${row.partyName}的资料','${row.bindPersonId}')">${value}</a>`;
                            }
                            return value;
                        }
                    },
                    {
                        field: 'partyType',
                        title: '当事人类型',
                        width: 10,
                        widthUnit: '%',
                    },
                    {
                        field: 'partyPosition',
                        title: '当事人时任职务',
                        width: 10,
                        widthUnit: '%',
                        formatter: function(value, row, index) {
                            return value ? value : '-';
                        }
                    },
                    {
                        field: 'punishContent',
                        title: '处罚内容',
                    },
                    {
                        field: 'punishDocNo',
                        title: '行政处罚决定书文号',
                        width: 10,
                        widthUnit: '%',
                        formatter: function(value, row, index) {
                            return value ? value : '-';
                        }
                    },
                    {
                        field: 'punishDate',
                        title: '行政处罚决定日期',
                        width: 10,
                        widthUnit: '%',
                        formatter: function(value, row, index) {
                            if (!value) {
                                return '-';
                            }
                            return moment(value).format('YYYY-MM-DD');
                        }
                    },
                    {
                        field: 'punishItem.rewardPunishOrg',
                        title: '作出决定机关',
                        width: 10,
                        widthUnit: '%',
                        formatter: function(value, row, index) {
                            return value ? value : '-';
                        }
                    },
                    {
                        field: 'punishItem.publishedDate',
                        title: '发布日期',
                        width: 10,
                        widthUnit: '%',
                        formatter: function(value, row, index) {
                            if (!value) {
                                return '-';
                            }
                            return moment(value).format('YYYY-MM-DD');
                        }
                    },
                    {
                        field: 'bindStatus',
                        title: '人员匹配状态',
                        width: 10,
                        widthUnit: '%',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(bindStatusDict, value);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            if (row.partyType !== '个人') {
                                return '';
                            }
                            var actions = [];
                            switch (row.bindStatus) {
                                case 'confirmed':
                                    actions.push(`<a class="btn btn-danger btn-xs ${editFlag}" href="javascript:void(0)" title="取消与系统内人员的关联" onclick="cancelBindPerson('${row.id}')"><i class="fa fa-remove"></i>取消关联</a>`);
                                    break;
                                case 'pending':
                                    actions.push(`<a class="btn btn-success btn-xs ${editFlag}" href="javascript:void(0)" title="查看待确认的人员" onclick="selectPerson('${row.id}')"><i class="fa fa-edit"></i>查看人员</a>`);
                                    break;
                            }
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });

        // 重置搜索表单
        function searchFormReset() {
            $("#formId")[0].reset();
            $(".selectpicker").val("");
            $(".selectpicker").selectpicker('refresh');
            $.table.search();
        }

        // 同步处罚公示
        function manualSync() {
            $.modal.confirm("确认要执行同步处罚公示吗？", function() {
                $.modal.loading("正在提交同步请求，请稍候...");
                $.ajax({
                    url: prefix + "/manualSync",
                    type: "post",
                    dataType: "json",
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess(result.msg);
                            $.table.refresh();
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.msgError("同步请求提交失败，请重试！");
                    }
                });
            });
        }
		
		function selectPerson(id) {
			var url = prefix + '/selectPerson/' + id;
		    $.modal.open("选择人员", url);
		}

		function cancelBindPerson(id) {
			$.modal.confirm("取消与系统内人员的关联后系统将自动删除任务表中的此项处罚记录，您确认要取消关联吗？", function() {
				$.operate.save(prefix + "/cancelBindPerson", { "id": id }, function(result) {
					if (result.code === 0) {
						$.modal.msgSuccess(result.msg);
						$.table.refresh();
					} else {
						$.modal.msgError(result.msg);
					}
				});
			});
		}

        function showPersonDetail(title, id) {
            $.modal.openTab(title, ctx + 'library/person/edit/' + id);
        }
        /*]]>*/
    </script>
</body>
</html> 