package com.metaorg.framework.redis;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.Metric;
import org.springframework.data.geo.Point;
import org.springframework.data.geo.format.PointFormatter;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import com.metaorg.framework.redis.RedisNamespaceUtil;


/**
 * @ClassName: RedisServiceImpl
 * @Description: TODO()
 * <AUTHOR>
 * @date 2018年8月15日 上午10:06:21
 * @version V1.0
 */
@Service
public class RedisServiceImpl implements RedisService {

	@Autowired
	private StringRedisTemplate template;
	
	@Autowired
	private RedisNamespaceUtil redisNamespaceUtil;

	public RedisServiceImpl() {
	}

	@Override
	public void remove(String... keys) {
		String[] var2 = keys;
		int var3 = keys.length;

		for (int var4 = 0; var4 < var3; ++var4) {
			String key = var2[var4];
			this.remove(key);
		}
	}

	@Override
	public void removePattern(String pattern) {
		String namespacedPattern = redisNamespaceUtil.addNamespace(pattern);
		Set<String> keys = this.template.keys(namespacedPattern);
		if (keys.size() > 0) {
			this.template.delete(keys);
		}
	}

	@Override
	public void remove(String key) {
		if (this.exists(key)) {
			String namespacedKey = redisNamespaceUtil.addNamespace(key);
			this.template.delete(namespacedKey);
		}
	}

	@Override
	public boolean exists(String key) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		return this.template.hasKey(namespacedKey);
	}

	@Override
	public String get(String key) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ValueOperations<String, String> operations = this.template.opsForValue();
		return (String) operations.get(namespacedKey);
	}

	@Override
	public boolean set(String key, String value) {
		boolean result = false;
		try {
			String namespacedKey = redisNamespaceUtil.addNamespace(key);
			ValueOperations<String, String> operations = this.template.opsForValue();
			operations.set(namespacedKey, value);
			result = true;
		} catch (Exception var5) {
			var5.printStackTrace();
		}
		return result;
	}

	@Override
	public boolean set(String key, String value, Long expireTime, TimeUnit timeUnit) {
		boolean result = false;
		try {
			String namespacedKey = redisNamespaceUtil.addNamespace(key);
			ValueOperations<String, String> operations = this.template.opsForValue();
			operations.set(namespacedKey, value);
			this.template.expire(namespacedKey, expireTime, timeUnit);
			result = true;
		} catch (Exception var7) {
			var7.printStackTrace();
		}
		return result;
	}
	
	@Override
	public List<String> range(String key, Long start, Long end) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.range(namespacedKey, start, end);
	}

	@Override
	public void trim(String key, Long start, Long end) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		operations.trim(namespacedKey, start, end);
	}

	@Override
	public Long size(String key) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.size(namespacedKey);
	}

	@Override
	public Long leftPush(String key, String value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.leftPush(namespacedKey, value);
	}

	@Override
	public Long leftPushAll(String key, String... values) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.leftPushAll(namespacedKey, values);
	}

	@Override
	public Long leftPushIfPresent(String key, String value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.leftPushIfPresent(namespacedKey, value);
	}

	@Override
	public Long leftPush(String key, String pivot, String value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.leftPush(namespacedKey, pivot, value);
	}

	@Override
	public Long rightPush(String key, String value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPush(namespacedKey, value);
	}

	@Override
	public Long rightPushAll(String key, String... values) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPushAll(namespacedKey, values);
	}

	@Override
	public Long rightPushIfPresent(String key, String value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPushIfPresent(namespacedKey, value);
	}

	@Override
	public Long rightPush(String key, String pivot, String value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPush(namespacedKey, pivot, value);
	}

	@Override
	public void set(String key, Long index, String value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		operations.set(namespacedKey, index, value);
	}

	@Override
	public Long remove(String key, Long count, Object value) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		if (this.exists(key)) {
			return operations.remove(namespacedKey, count, value);
		}
		return null;
	}

	@Override
	public String index(String key, Long index) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.index(namespacedKey, index);
	}

	@Override
	public String leftPop(String key) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		if (this.exists(key)) {
			return operations.leftPop(namespacedKey);
		}
		return null;
	}

	@Override
	public String leftPop(String key, Long timeout, TimeUnit unit) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.leftPop(namespacedKey, timeout, unit);
	}

	@Override
	public String rightPop(String key) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPop(namespacedKey);
	}

	@Override
	public String rightPop(String key, Long timeout, TimeUnit unit) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPop(namespacedKey, timeout, unit);
	}

	@Override
	public String rightPopAndLeftPush(String sourceKey, String destinationKey) {
		String namespacedSourceKey = redisNamespaceUtil.addNamespace(sourceKey);
		String namespacedDestinationKey = redisNamespaceUtil.addNamespace(destinationKey);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPopAndLeftPush(namespacedSourceKey, namespacedDestinationKey);
	}

	@Override
	public String rightPopAndLeftPush(String sourceKey, String destinationKey, Long timeout, TimeUnit unit) {
		String namespacedSourceKey = redisNamespaceUtil.addNamespace(sourceKey);
		String namespacedDestinationKey = redisNamespaceUtil.addNamespace(destinationKey);
		ListOperations<String, String> operations =  this.template.opsForList();
		return operations.rightPopAndLeftPush(namespacedSourceKey, namespacedDestinationKey, timeout, unit);
	}
	
	@Override
	public Long geoAdd(String key, String point, String member) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		return this.template.opsForGeo().add(namespacedKey, PointFormatter.INSTANCE.convert(point), member);
	}

	@Override
	public Long geoRemove(String key, String... members) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		return this.template.opsForGeo().remove(namespacedKey, members);
	}

	@Override
	public List<Point> geoPos(String key, String... members) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		return this.template.opsForGeo().position(namespacedKey, members);
	}

	@Override
	public List<String> geoHash(String key, String... members) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		return this.template.opsForGeo().hash(namespacedKey, members);
	}

	@Override
	public Double geoDist(String key, String member1, String member2, Metric metric) {
		String namespacedKey = redisNamespaceUtil.addNamespace(key);
		return this.template.opsForGeo().distance(namespacedKey, member1, member2, metric).getValue();
	}


}
