package com.metaorg.framework.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Redis命名空间工具类
 * 用于统一管理Redis key的命名空间，避免与其他系统的缓存冲突
 * 
 * <AUTHOR>
 */
@Component
public class RedisNamespaceUtil {
    
    @Value("${spring.redis.namespace:metaorg}")
    private String namespace;
    
    /**
     * 为Redis key添加命名空间前缀
     * 
     * @param key 原始key
     * @return 带命名空间的key
     */
    public String addNamespace(String key) {
        if (key == null || key.trim().isEmpty()) {
            return key;
        }
        
        // 如果key已经包含命名空间前缀，直接返回
        if (key.startsWith(namespace + ":")) {
            return key;
        }
        
        return namespace + ":" + key;
    }
    
    /**
     * 为Redis key添加命名空间前缀（批量处理）
     * 
     * @param keys 原始key数组
     * @return 带命名空间的key数组
     */
    public String[] addNamespace(String... keys) {
        if (keys == null || keys.length == 0) {
            return keys;
        }
        
        String[] result = new String[keys.length];
        for (int i = 0; i < keys.length; i++) {
            result[i] = addNamespace(keys[i]);
        }
        return result;
    }
    
    /**
     * 移除Redis key的命名空间前缀
     * 
     * @param key 带命名空间的key
     * @return 原始key
     */
    public String removeNamespace(String key) {
        if (key == null || key.trim().isEmpty()) {
            return key;
        }
        
        String prefix = namespace + ":";
        if (key.startsWith(prefix)) {
            return key.substring(prefix.length());
        }
        
        return key;
    }
    
    /**
     * 获取命名空间前缀
     * 
     * @return 命名空间前缀
     */
    public String getNamespace() {
        return namespace;
    }
    
    /**
     * 获取带冒号的命名空间前缀
     * 
     * @return 带冒号的命名空间前缀
     */
    public String getNamespacePrefix() {
        return namespace + ":";
    }
    
    /**
     * 检查key是否包含命名空间前缀
     * 
     * @param key Redis key
     * @return 是否包含命名空间前缀
     */
    public boolean hasNamespace(String key) {
        return key != null && key.startsWith(namespace + ":");
    }
}
