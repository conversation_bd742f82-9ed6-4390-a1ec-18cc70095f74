<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.system.mapper.SysRankMapper">

	<resultMap type="SysRank" id="SysRankResult">
		<id     property="rankId"     column="rank_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="rankName"   column="rank_name"   />
		<result property="rankCode"   column="rank_code"   />
		<result property="orderNum"   column="order_num"   />
		<result property="status"     column="status"      />
		<result property="maleWarningAhead"    column="male_warning_ahead"    />
		<result property="femaleWarningAhead"    column="female_warning_ahead"    />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="parentCode" column="parent_code" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>

	<resultMap type="SysRank" id="SysRankResultChild">
		<id     property="rankId"     column="child_rank_id"     />
		<result property="parentId"   column="child_parent_id"   />
		<result property="ancestors"  column="child_ancestors"   />
		<result property="rankName"   column="child_rank_name"   />
		<result property="rankCode"   column="child_rank_code"   />
		<result property="orderNum"   column="child_order_num"   />
		<result property="status"     column="child_status"      />
		<result property="maleWarningAhead"    column="child_male_warning_ahead"    />
		<result property="femaleWarningAhead"    column="child_female_warning_ahead"    />
		<result property="delFlag"    column="child_del_flag"    />
		<result property="parentName" column="child_parent_name" />
		<result property="parentCode" column="child_parent_code" />
		<result property="createBy"   column="child_create_by"   />
		<result property="createTime" column="child_create_time" />
		<result property="updateBy"   column="child_update_by"   />
		<result property="updateTime" column="child_update_time" />
	</resultMap>

	<resultMap type="SysRank" id="SysRankTreeResult" extends="SysRankResult">
		<collection  property="childs"  column="rank_id" javaType="java.util.List"  foreignColumn="parent_id" resultMap="SysRankResultChild" />
	</resultMap>

	<sql id="selectRankFields">
		select d.rank_id, d.parent_id, d.ancestors, d.rank_name, d.rank_code, d.order_num, d.status, d.male_warning_ahead,
			   d.female_warning_ahead, d.del_flag, d.create_by, d.create_time
	</sql>
	
	<sql id="selectChildRankFields">
		 ,cd.rank_id as child_rank_id, cd.parent_id as child_parent_id, cd.ancestors as child_ancestors, cd.rank_name as child_rank_name, cd.rank_code as child_rank_code,
			   cd.order_num as child_order_num, cd.status as child_status, cd.male_warning_ahead as child_male_warning_ahead,cd.female_warning_ahead as child_female_warning_ahead,
			   cd.del_flag as child_del_flag, cd.create_by as child_create_by, cd.create_time as child_create_time
	</sql>
	
	<sql id="selectRankVo">
		<include refid="selectRankFields"/>
        from sys_rank d
    </sql>

	<sql id="selectRankTreeVo">
		<include refid="selectRankFields"/>
		<include refid="selectChildRankFields"/>
		from sys_rank d
		left join sys_rank cd on cd.parent_id = d.rank_id and cd.del_flag = '0'
	</sql>
	
	<select id="selectRankList" parameterType="SysRank" resultMap="SysRankResult">
        <include refid="selectRankVo"/>
        where d.del_flag = '0'
		<if test="rankId != null and rankId != ''">
			AND d.rank_id = #{rankId}
		</if>
        <if test="parentId != null and parentId != ''">
			AND d.parent_id = #{parentId}
		</if>
		<if test="rankName != null and rankName != ''">
			AND d.rank_name like concat('%', #{rankName}, '%')
		</if>
		<if test="rankCode != null and rankCode != ''">
			AND d.rank_code = #{rankCode}
		</if>
		<if test="status != null and status != ''">
			AND d.status = #{status}
		</if>
		order by d.parent_id, d.order_num
    </select>

	<select id="selectRankTreeList" parameterType="SysRank" resultMap="SysRankTreeResult">
		<include refid="selectRankTreeVo"/>
		where d.del_flag = '0'
		<if test="rankId != null and rankId != ''">
			AND d.rank_id = #{rankId}
		</if>
		<if test="parentId != null and parentId != ''">
			AND d.parent_id = #{parentId}
		</if>
		<if test="rankName != null and rankName != ''">
			AND d.rank_name like concat('%', #{rankName}, '%')
		</if>
		<if test="rankCode != null and rankCode != ''">
			AND d.rank_code = #{rankCode}
		</if>
		<if test="status != null and status != ''">
			AND d.status = #{status}
		</if>
		order by d.order_num,cd.order_num
	</select>

	<select id="selectRankCount" parameterType="SysRank" resultType="int">
		select count(1) from sys_rank
		where del_flag = '0'
		<if test="rankId != null and rankId != ''"> and rank_id = #{rankId} </if>
		<if test="parentId != null and parentId != ''"> and parent_id = #{parentId} </if>
	</select>

	<select id="checkRankNameUnique" resultMap="SysRankResult">
	    <include refid="selectRankVo"/>
		where rank_name=#{rankName} and parent_id = #{parentId} and del_flag = '0' limit 1
	</select>

	<select id="selectRankById" parameterType="String" resultMap="SysRankResult">
		select d.rank_id, d.parent_id, d.ancestors, d.rank_name, d.rank_code, d.order_num, d.status,
			(select rank_name from sys_rank where rank_id = d.parent_id) parent_name,
			(select rank_code from sys_rank where rank_id = d.parent_id) parent_code, d.male_warning_ahead,
			d.female_warning_ahead, d.del_flag,d.create_by, d.create_time
		from sys_rank d
		where d.rank_id = #{rankId}
	</select>

	<select id="selectChildrenRankById" parameterType="String" resultMap="SysRankResult">
		select * from sys_rank where find_in_set(#{rankId}, ancestors)
	</select>

	<select id="selectNormalChildrenRankById" parameterType="String" resultType="int">
		select count(*) from sys_rank where status = 0 and del_flag = '0' and find_in_set(#{rankId}, ancestors)
	</select>
	
	<insert id="insertRank" parameterType="SysRank">
 		insert into sys_rank
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="rankId != null and rankId != ''">rank_id,</if>
 			<if test="parentId != null and parentId != ''">parent_id,</if>
 			<if test="rankName != null and rankName != ''">rank_name,</if>
			<if test="rankCode != null and rankCode != ''">rank_code,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="orderNum != null">order_num,</if>
 			<if test="status != null">status,</if>
			<if test="maleWarningAhead != null">male_warning_ahead,</if>
			<if test="femaleWarningAhead != null">female_warning_ahead,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
 			<if test="rankId != null and rankId != ''">#{rankId},</if>
 			<if test="parentId != null and parentId != ''">#{parentId},</if>
 			<if test="rankName != null and rankName != ''">#{rankName},</if>
			<if test="rankCode != null and rankCode != ''">#{rankCode},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
			<if test="orderNum != null">#{orderNum},</if>
 			<if test="status != null">#{status},</if>
			<if test="maleWarningAhead != null">#{maleWarningAhead},</if>
			<if test="femaleWarningAhead != null">#{femaleWarningAhead},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>
	
	<update id="updateRank" parameterType="SysRank">
 		update sys_rank
 		<set>
 			<if test="parentId != null and parentId != '0'.toString()">parent_id = #{parentId},</if>
 			<if test="rankName != null and rankName != ''">rank_name = #{rankName},</if>
			<if test="rankCode != null and rankCode != ''">rank_code = #{rankCode},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="orderNum != null">order_num = #{orderNum},</if>
			<if test="status != null and status != ''">status = #{status},</if>
			<if test="maleWarningAhead != null">male_warning_ahead = #{maleWarningAhead},</if>
			<if test="femaleWarningAhead != null">female_warning_ahead = #{femaleWarningAhead},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
 		</set>
 		where rank_id = #{rankId}
	</update>
	
	<update id="updateRankChildren" parameterType="java.util.List">
	    update sys_rank set ancestors =
	    <foreach collection="ranks" item="item" index="index"
	        separator=" " open="case rank_id" close="end">
	        when #{item.rankId} then #{item.ancestors}
	    </foreach>
	    where rank_id in
	    <foreach collection="ranks" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.rankId}
	    </foreach>
	 </update>

	<delete id="deleteRankById" parameterType="String">
		update sys_rank set del_flag = '2' where rank_id = #{rankId}
	</delete>
	
	<update id="updateRankStatusNormal" parameterType="String">
 	    update sys_rank set status = '0' where rank_id in
 	    <foreach collection="array" item="rankId" open="(" separator="," close=")">
        	#{rankId}
        </foreach>
	</update>

</mapper> 