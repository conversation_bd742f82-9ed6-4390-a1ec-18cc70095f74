package com.metaorg.framework.shiro.realm;

import java.util.HashSet;
import java.util.Set;

import com.metaorg.common.constant.Constants;
import com.metaorg.common.utils.MessageUtils;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.enums.UserStatus;
import com.metaorg.common.exception.user.CaptchaException;
import com.metaorg.common.exception.user.RoleBlockedException;
import com.metaorg.common.exception.user.UserBlockedException;
import com.metaorg.common.exception.user.UserDeleteException;
import com.metaorg.common.exception.user.UserNotExistsException;
import com.metaorg.common.exception.user.UserPasswordNotMatchException;
import com.metaorg.common.exception.user.UserPasswordRetryLimitExceedException;
import com.metaorg.common.utils.ShiroUtils;
import com.metaorg.framework.jwt.auth.JwtToken;
import com.metaorg.framework.jwt.utils.JwtUtils;
import com.metaorg.framework.oauth2.auth.OAuth2Token;
import com.metaorg.framework.shiro.auth.DatabaseRestoreToken;
import com.metaorg.framework.shiro.service.SysLoginService;
import com.metaorg.framework.shiro.service.SysPasswordService;
import com.metaorg.system.service.ISysMenuService;
import com.metaorg.system.service.ISysRoleService;
import com.metaorg.system.service.ISysUserService;
import com.metaorg.framework.manager.AsyncManager;
import com.metaorg.framework.manager.factory.AsyncFactory;

/**
 * 自定义Realm 处理登录 权限
 * 
 * <AUTHOR>
 */
public class UserRealm extends AuthorizingRealm
{
    private static final Logger log = LoggerFactory.getLogger(UserRealm.class);

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    /**
     * 授权
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection arg0)
    {
        SysUser user = ShiroUtils.getSysUser();
        // 角色列表
        Set<String> roles = new HashSet<String>();
        // 功能列表
        Set<String> menus = new HashSet<String>();
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        // 管理员拥有所有权限
        if (user.isAdmin())
        {
            info.addRole("admin");
            info.addStringPermission("*:*:*");
        }
        else
        {
            roles = roleService.selectRoleKeys(user.getUserId());
            menus = menuService.selectPermsByUserId(user.getUserId());
            // 角色加入AuthorizationInfo认证对象
            info.setRoles(roles);
            // 权限加入AuthorizationInfo认证对象
            info.setStringPermissions(menus);
        }
        return info;
    }

    /**
     * 登录认证
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken)
            throws AuthenticationException
    {
        if (authenticationToken instanceof DatabaseRestoreToken)
        {
            // 处理数据库恢复认证Token
            DatabaseRestoreToken restoreToken = (DatabaseRestoreToken) authenticationToken;
            SysUser user = restoreToken.getUser();
            String loginName = restoreToken.getLoginName();
            
            log.info("处理数据库恢复认证Token，用户: {}", loginName);
            
            if (user == null)
            {
                throw new AuthenticationException("数据库恢复认证失败：用户信息为空");
            }
            
            // 验证用户状态
            if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
            {
                throw new UserDeleteException();
            }
            
            if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
            {
                throw new UserBlockedException();
            }
            
            log.info("用户[{}]数据库恢复认证成功", loginName);
            return new SimpleAuthenticationInfo(user, null, getName());
        }
        else if (authenticationToken instanceof JwtToken)
        {
            JwtToken jwtToken = (JwtToken) authenticationToken;
            String token = jwtToken.getToken();
            String username = JwtUtils.getUserName(token);
            if (username == null)
            {
                throw new AccountException("token 验证失败");
            }
            SysUser user = userService.selectUserByLoginName(username);
            if (user == null)
            {
                throw new AuthenticationException("用户数据不存在");
            }

            try
            {
                JwtUtils.verify(username, user.getPassword(), jwtToken.getToken());

                if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
                {
                    throw new UserDeleteException();
                }

                if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
                {
                    throw new UserBlockedException();
                }
            }
            catch (Exception e)
            {
                log.info("对用户[" + username + "]进行jwt登录验证..验证未通过{}", e.getMessage());
                throw new AuthenticationException(e.getMessage(), e);
            }

            return new SimpleAuthenticationInfo(user, null, getName());
        }
        else if (authenticationToken instanceof OAuth2Token)
        {
            OAuth2Token oauth2Token = (OAuth2Token) authenticationToken;
            String username = oauth2Token.getUsername();
            
            if (username == null || username.trim().isEmpty())
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor("", Constants.LOGIN_FAIL, "用户名不能为空"));
                throw new AccountException("OAuth2用户名不能为空");
            }
            
            SysUser user = userService.selectUserByLoginName(username);
            if (user == null)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, "暂时无权限访问"));
                throw new AuthenticationException("用户数据不存在");
            }

            try
            {
                // OAuth2认证验证
                boolean credentialIsValid = true;
                if (oauth2Token.isOAuth2Auth())
                {
                    String credential = oauth2Token.getCredential();
                    if (credential == null || credential.trim().isEmpty())
                    {
                        credentialIsValid = false;
                    }
                }
                else
                {
                    credentialIsValid = false;
                }

                if(!credentialIsValid){
                    AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, "OAuth2凭证不能为空"));
                    throw new AuthenticationException("OAuth2凭证不能为空");
                }

                // 验证用户状态
                if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
                {
                    AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.delete")));
                    throw new UserDeleteException();
                }

                if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
                {
                    AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.blocked", user.getRemark())));
                    throw new UserBlockedException();
                }
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
                loginService.setRolePermission(user);
                loginService.recordLoginInfo(Long.decode(user.getUserId().toString()));
            }
            catch (Exception e)
            {
                log.info("对用户[{}]进行OAuth2登录验证..验证未通过: {}", username, e.getMessage());
                throw new AuthenticationException(e.getMessage(), e);
            }

            return new SimpleAuthenticationInfo(user, null, getName());
        }
        else
        {
            UsernamePasswordToken upToken = (UsernamePasswordToken) authenticationToken;
            String username = upToken.getUsername();
            StringBuffer upwd = new StringBuffer();
            if (upToken.getPassword() != null)
            {
                //password = new String(upToken.getPassword());
                char[] upTokenPwd = upToken.getPassword();
                for(int i=0;i<upTokenPwd.length;i++){
                    upwd.append(upTokenPwd[i]);
                }
            }

            SysUser user = null;
            try
            {
//                user = loginService.login(username, password);
                user = loginService.login(username, upwd.toString());
            }
            catch (CaptchaException e)
            {
                throw new AuthenticationException(e.getMessage(), e);
            }
            catch (UserNotExistsException e)
            {
                throw new UnknownAccountException(e.getMessage(), e);
            }
            catch (UserPasswordNotMatchException e)
            {
                throw new IncorrectCredentialsException(e.getMessage(), e);
            }
            catch (UserPasswordRetryLimitExceedException e)
            {
                throw new ExcessiveAttemptsException(e.getMessage(), e);
            }
            catch (UserBlockedException e)
            {
                throw new LockedAccountException(e.getMessage(), e);
            }
            catch (RoleBlockedException e)
            {
                throw new LockedAccountException(e.getMessage(), e);
            }
            catch (Exception e)
            {
                log.info("对用户[" + username + "]进行登录验证..验证未通过{}", e.getMessage());
                throw new AuthenticationException(e.getMessage(), e);
            }
            SimpleAuthenticationInfo info = new SimpleAuthenticationInfo(user, null, getName());
            return info;
        }
    }

    /**
     * 清理指定用户授权信息缓存
     */
    public void clearCachedAuthorizationInfo(Object principal)
    {
        SimplePrincipalCollection principals = new SimplePrincipalCollection(principal, getName());
        this.clearCachedAuthorizationInfo(principals);
    }

    /**
     * 清理所有用户授权信息缓存
     */
    public void clearAllCachedAuthorizationInfo()
    {
        Cache<Object, AuthorizationInfo> cache = getAuthorizationCache();
        if (cache != null)
        {
            for (Object key : cache.keys())
            {
                cache.remove(key);
            }
        }
    }
}
