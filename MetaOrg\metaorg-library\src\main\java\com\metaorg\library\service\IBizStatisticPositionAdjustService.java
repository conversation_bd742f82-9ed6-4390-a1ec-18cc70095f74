package com.metaorg.library.service;

import java.util.List;
import com.metaorg.library.domain.BizStatisticPositionAdjust;
import com.metaorg.library.domain.BizPersonPositionAdjustProposed;

/**
 * 干部职务调整统计的Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public interface IBizStatisticPositionAdjustService {
    /**
     * 查询干部调整统计记录
     * 
     * @param bizStatisticPositionAdjust 干部调整统计记录
     * @return 干部调整统计记录
     */
    public List<BizStatisticPositionAdjust> selectBizStatisticPositionAdjust(BizStatisticPositionAdjust bizStatisticPositionAdjust);

    /**
     * 查询干部职务拟调整详情
     * 
     * @param bizPersonPositionAdjustProposed 干部职务拟调整详情
     * @return 干部职务拟调整详情
     */
    public List<BizPersonPositionAdjustProposed> selectBizPersonPositionAdjustProposed(BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed);

    /**
     * 查询干部调整统计记录
     * 
     * @param id 干部调整统计记录主键
     * @return 干部调整统计记录
     */
    public BizStatisticPositionAdjust selectBizStatisticPositionAdjustById(String id);
    
    /**
     * 查询干部职务拟调整详情
     * 
     * @param id 干部职务拟调整详情主键
     * @return 干部职务拟调整详情
     */
    public BizPersonPositionAdjustProposed selectBizPersonPositionAdjustProposedById(String id);

    /**
     * 批最新增干部调整统计记录
     * 
     * @param bizStatisticPositionAdjust 干部调整统计记录
     * @return 结果
     */
    public int batchInsertBizStatisticPositionAdjust(List<BizStatisticPositionAdjust> bizStatisticPositionAdjustList    );
    
    /**
     * 批最新增干部职务拟调整详情
     * 
     * @param bizPersonPositionAdjustProposed 干部职务拟调整详情
     * @return 结果
     */
    public int batchInsertBizPersonPositionAdjustProposed(List<BizPersonPositionAdjustProposed> bizPersonPositionAdjustProposedList);

    /**
     * 删除干部调整统计记录
     * 
     * @param bizStatisticPositionAdjust 干部调整统计记录
     * @return 结果
     */
    public int deleteBizStatisticPositionAdjust(BizStatisticPositionAdjust bizStatisticPositionAdjust);

    /**
     * 删除干部职务拟调整详情
     * 
     * @param bizPersonPositionAdjustProposed 干部职务拟调整详情
     * @return 结果
     */
    public int deleteBizPersonPositionAdjustProposed(BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed);

    /**
     * 统计干部职务调整
     * 
     * @param bizStatisticPositionAdjust 统计参数
     * @return 结果
     */
    public int invokeStatisticPositionAdjust(BizStatisticPositionAdjust bizStatisticPositionAdjust);
}
