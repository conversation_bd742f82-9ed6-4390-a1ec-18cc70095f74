package com.metaorg.web.controller.api;

import com.metaorg.common.annotation.Log;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.ResponseBodyApi;
import com.metaorg.common.core.text.Convert;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.domain.BizPersonFavorite;
import com.metaorg.library.service.IBizPersonFavoriteService;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.utils.DataScopeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.metaorg.library.service.IBizPersonService;

import javax.servlet.http.HttpServletResponse;
import com.metaorg.common.utils.StringUtils;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/api/favorite")
public class FavoriteController extends BaseController {
    @Autowired
    private IBizPersonFavoriteService bizPersonFavoriteService;

    @Autowired
    private IBizPersonService bizPersonService;

    /**
     * 获取列表查询条件
    */
    private void getQueryOptions(BizPersonFavorite bizPersonFavorite, Map<String, Object> paramMap) {
        long userId = getUserId();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonFavorite, getSysUser(), "vpd", "", "", getUserDeptId());
        }
        bizPersonFavorite.setUserId(userId);
        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizPersonFavorite.getParams().put("keyword", keyword);
        }
    }

    @GetMapping("/list")
    @ResponseBody
    public ResponseBodyApi<List<BizPersonFavorite>> getPersons(HttpServletResponse response, @RequestParam Map<String, Object> paramMap) {
        BizPersonFavorite bizPersonFavorite = new BizPersonFavorite();
        getQueryOptions(bizPersonFavorite, paramMap);
        startPage();
        
        List<BizPersonFavorite> list = bizPersonFavoriteService.selectBizPersonFavoriteList(bizPersonFavorite);

        ResponseBodyApi<List<BizPersonFavorite>> rsBody = new ResponseBodyApi<>(list);
        rsBody.setMessage("OK");
        return rsBody;
    }

    // 检查是否收藏
    @GetMapping("/check/{personId}")
    @ResponseBody
    public ResponseBodyApi<Boolean> checkFavorite(@PathVariable("personId") String personId) {
        long userId = getUserId();
        List<BizPersonFavorite> favorites = bizPersonFavoriteService.selectBizPersonFavoriteByPersonId(personId, userId);
        return new ResponseBodyApi<>(favorites.isEmpty() ? false : true);
    }

    @Log(title = "PAD端-人员收藏", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @PostMapping("/add")
    @ResponseBody
    public ResponseBodyApi<String> addFavorite(HttpServletResponse response, @RequestParam Map<String, Object> paramMap) {
        long userId = getUserId();
        String personId = "";
        if(paramMap.get("personId") != null) {
            personId = paramMap.get("personId").toString();
        }
        if(StringUtils.isEmpty(personId)) {
            return new ResponseBodyApi<>("400",false, "人员标识不能为空");
        }

        BizPerson bizPerson = bizPersonService.selectBizPersonById(personId);

        if(bizPerson == null || !bizPerson.getDelFlag().equals("0")) {
            return new ResponseBodyApi<>("400",false, "人员不存在");
        }

        String[] personIds = { personId };
        bizPersonFavoriteService.insertBizPersonFavorite(userId, personIds);
        return new ResponseBodyApi<>(true, "OK");
    }

    @Log(title = "PAD端-人员收藏", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @PostMapping("/remove")
    @ResponseBody
    public ResponseBodyApi<String> removeFavorite(HttpServletResponse response, @RequestParam Map<String, Object> paramMap) {
        long userId = getUserId();
        // 检查ids是否为空
        if (paramMap.get("ids") == null || paramMap.get("ids").toString().isEmpty()) {
            return new ResponseBodyApi<>("400",false, "ids不能为空");
        }
        String[] idsArray = Convert.toStrArray(paramMap.get("ids").toString());
        bizPersonFavoriteService.deleteBizPersonFavoriteByUserId(userId, idsArray);
        return new ResponseBodyApi<>("OK");
    }
}