<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>OAuth2认证中 - 干部管理信息系统</title>
    <meta name="description" content="干部管理信息系统OAuth2认证页面">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.7.7}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">
        .sso-auth-panel {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 480px;
            margin: 120px auto;
        }
        
        .auth-logo {
            margin-bottom: 30px;
        }
        
        .auth-logo img {
            max-height: 60px;
        }
        
        .auth-title {
            font-size: 28px;
            color: #262626;
            margin-bottom: 12px;
            font-weight: 500;
        }
        
        .auth-subtitle {
            font-size: 16px;
            color: #8c8c8c;
            margin-bottom: 40px;
        }
        
        .auth-status {
            margin-bottom: 40px;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-text {
            font-size: 16px;
            color: #595959;
            margin-bottom: 8px;
        }
        
        .status-detail {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
        }
        
        .step {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .step.active {
            color: #1890ff;
        }
        
        .step.completed {
            color: #52c41a;
        }
        
        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 12px;
        }
        
        .step.active .step-icon {
            background: #1890ff;
            color: white;
        }
        
        .step.completed .step-icon {
            background: #52c41a;
            color: white;
        }
        
        .step-connector {
            width: 40px;
            height: 2px;
            background: #f0f0f0;
            margin: 0 16px;
        }
        
        .step.completed + .step .step-connector {
            background: #52c41a;
        }
        
        .auth-actions {
            margin-top: 30px;
        }
        
        .btn-cancel {
            background: transparent;
            border: 1px solid #d9d9d9;
            color: #595959;
            padding: 8px 24px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .btn-cancel:hover {
            border-color: #1890ff;
            color: #1890ff;
            text-decoration: none;
        }
        
        .system-footer {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #8c8c8c;
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="sso-auth-panel">
        <div class="auth-logo">
            <img alt="[ METAVUN ]" src="../static/logo.png" th:src="@{/logo.png}">
        </div>
        
        <div class="auth-title">单点登录认证</div>
        <div class="auth-subtitle">正在进行OAuth2身份验证</div>
        
        <div class="progress-steps">
            <div class="step completed" id="step1">
                <div class="step-icon">
                    <i class="fa fa-check"></i>
                </div>
                <span>发起认证</span>
            </div>
            <div class="step-connector"></div>
            <div class="step active" id="step2">
                <div class="step-icon">
                    <i class="fa fa-spinner fa-spin"></i>
                </div>
                <span>身份验证</span>
            </div>
            <div class="step-connector"></div>
            <div class="step" id="step3">
                <div class="step-icon">3</div>
                <span>登录系统</span>
            </div>
        </div>
        
        <div class="auth-status">
            <div class="loading-spinner" id="loadingSpinner"></div>
            <div class="status-text" id="statusText">正在验证您的身份...</div>
            <div class="status-detail" id="statusDetail">请稍候，系统正在与认证服务器通信</div>
        </div>
        
        <div class="auth-actions">
            <a href="/login" class="btn-cancel">
                <i class="fa fa-times"></i> 取消认证
            </a>
        </div>
    </div>
    
    <div class="system-footer">
        <p>干部管理信息系统</p>
        <p>OAuth2单点登录 - 安全可信赖</p>
    </div>

    <!-- 全局js -->
    <script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
    <script th:inline="javascript">
        $(document).ready(function() {
            var step = 1;
            var messages = [
                { text: "正在验证您的身份...", detail: "请稍候，系统正在与认证服务器通信" },
                { text: "正在获取用户信息...", detail: "认证成功，正在获取您的账户信息" },
                { text: "正在登录系统...", detail: "即将跳转到系统首页" }
            ];
            
            // 模拟认证过程
            function updateStatus() {
                if (step <= 3) {
                    // 更新步骤状态
                    if (step > 1) {
                        $('#step' + (step - 1)).removeClass('active').addClass('completed');
                        $('#step' + (step - 1) + ' .step-icon').html('<i class="fa fa-check"></i>');
                    }
                    
                    if (step <= 3) {
                        $('#step' + step).addClass('active');
                        if (step < 3) {
                            $('#step' + step + ' .step-icon').html('<i class="fa fa-spinner fa-spin"></i>');
                        }
                    }
                    
                    // 更新状态文本
                    if (step <= messages.length) {
                        $('#statusText').text(messages[step - 1].text);
                        $('#statusDetail').text(messages[step - 1].detail);
                    }
                    
                    step++;
                    
                    if (step <= 3) {
                        setTimeout(updateStatus, 2000);
                    } else {
                        // 认证完成
                        $('#loadingSpinner').hide();
                        $('#statusText').text('认证完成');
                        $('#statusDetail').text('正在跳转到系统首页...');
                        $('#step3').removeClass('active').addClass('completed');
                        $('#step3 .step-icon').html('<i class="fa fa-check"></i>');
                        
                        setTimeout(function() {
                            window.location.href = '/index';
                        }, 1500);
                    }
                }
            }
            
            // 开始认证过程
            setTimeout(updateStatus, 1000);
            
            // 检查URL参数
            var urlParams = new URLSearchParams(window.location.search);
            var code = urlParams.get('code');
            
            if (code) {
                // 如果有code参数，直接处理认证
                $('#statusText').text('正在处理认证回调...');
                $('#statusDetail').text('系统正在验证授权码');
                
                // 实际的认证处理会在后端进行
                // 这里只是前端的状态展示
            }
        });
    </script>
</body>
</html>
