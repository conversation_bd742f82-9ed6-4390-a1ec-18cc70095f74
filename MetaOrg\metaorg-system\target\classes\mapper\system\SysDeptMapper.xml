<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.system.mapper.SysDeptMapper">

	<resultMap type="SysDept" id="SysDeptResult">
		<id     property="deptId"     column="dept_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="deptName"   column="dept_name"   />
		<result property="orderNum"   column="order_num"   />
		<result property="leader"     column="leader"      />
		<result property="phone"      column="phone"       />
		<result property="email"      column="email"       />
		<result property="status"     column="status"      />
		<result property="unitProvince"    column="unit_province"    />
		<result property="unitCity"    column="unit_city"    />
		<result property="unitArea"    column="unit_area"    />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
		<result property="deptType" column="dept_type" />
		<result property="deptLevel" column="dept_level" />
		<result property="deptRegion" column="dept_region" />
		<result property="maxPositions" column="max_positions" />
		<result property="assignedPositions" column="assigned_positions" />
		<result property="extras" column="extras" javaType="java.util.HashMap" jdbcType="OTHER" typeHandler="com.metaorg.common.core.handler.JsonTypeHandler" />
	</resultMap>

	<sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.status, d.unit_province, d.unit_city, d.unit_area, d.del_flag, d.create_by, d.create_time,
               d.dept_type,d.dept_level,d.dept_region,d.max_positions,d.assigned_positions,d.extras
        from sys_dept d
    </sql>

	<select id="selectRoleDeptTree" parameterType="Long" resultType="String">
		select concat(d.dept_id, d.dept_name) as dept_name
		from sys_dept d
			left join sys_role_dept rd on d.dept_id = rd.dept_id
		where d.del_flag = '0' and rd.role_id = #{roleId}
		order by d.parent_id, d.order_num
	</select>

	<select id="selectBizPersonDeptTree" parameterType="String" resultType="String">
		select concat(d.dept_id, d.dept_name) as dept_name
		from sys_dept d
				 left join biz_person_dept pd on d.dept_id = pd.dept_id
		where d.del_flag = '0' and pd.person_id = #{personId}
		order by d.parent_id, d.order_num
	</select>

	<select id="selectDeptList" parameterType="SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
		<if test="deptId != null and deptId != 0">
			AND dept_id = #{deptId}
		</if>
        <if test="parentId != null and parentId != 0">
			AND parent_id = #{parentId}
		</if>
		<if test="deptName != null and deptName != ''">
			AND dept_name like concat('%', #{deptName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		<if test="unitProvince != null and unitProvince != ''"> and unit_province = #{unitProvince}</if>
		<if test="unitCity != null and unitCity != ''"> and unit_city = #{unitCity}</if>
		<if test="unitArea != null and unitArea != ''"> and unit_area = #{unitArea}</if>
		<if test="params.deptIds != null"> and dept_id in
			<foreach item="item" index="index" collection="params.deptIds" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="params.deptNames != null"> and dept_name in
			<foreach item="item" index="index" collection="params.deptNames" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<!-- 数据范围过滤 -->
		 AND 'params.dataScope' = 'params.dataScope'
		order by d.parent_id, d.order_num
    </select>

	<select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user where dept_id = #{deptId} and del_flag = '0'
	</select>

	<select id="selectDeptCount" parameterType="SysDept" resultType="int">
		select count(1) from sys_dept
		where del_flag = '0'
		<if test="deptId != null and deptId != 0"> and dept_id = #{deptId} </if>
		<if test="parentId != null and parentId != 0"> and parent_id = #{parentId} </if>
	</select>

	<select id="checkDeptNameUnique" resultMap="SysDeptResult">
	    <include refid="selectDeptVo"/>
		where dept_name=#{deptName} and parent_id = #{parentId} and del_flag = '0' limit 1
	</select>

	<select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
		select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.status, d.unit_province, d.unit_city, d.unit_area,
			(select dept_name from sys_dept where dept_id = d.parent_id) parent_name,d.dept_type,d.dept_level,d.dept_region,d.max_positions,d.assigned_positions,d.extras
		from sys_dept d
		where d.dept_id = #{deptId}
	</select>

	<select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where find_in_set(#{deptId}, ancestors)
	</select>

	<select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = 0 and del_flag = '0' and find_in_set(#{deptId}, ancestors)
	</select>

	<insert id="insertDept" parameterType="SysDept">
 		insert into sys_dept
		<trim prefix="(" suffix=")" suffixOverrides=",">
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="parentId != null and parentId != 0">parent_id,</if>
 			<if test="deptName != null and deptName != ''">dept_name,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="orderNum != null">order_num,</if>
 			<if test="leader != null and leader != ''">leader,</if>
 			<if test="phone != null and phone != ''">phone,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="status != null">status,</if>
			<if test="unitProvince != null">unit_province,</if>
			<if test="unitCity != null">unit_city,</if>
			<if test="unitArea != null">unit_area,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="deptType != null">dept_type,</if>
			<if test="deptLevel != null">dept_level,</if>
			<if test="deptRegion != null">dept_region,</if>
			<if test="maxPositions != null">max_positions,</if>
			<if test="assignedPositions != null">assigned_positions,</if>
			<if test="extras != null">extras,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
 			<if test="deptId != null and deptId != 0">#{deptId},</if>
 			<if test="parentId != null and parentId != 0">#{parentId},</if>
 			<if test="deptName != null and deptName != ''">#{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="orderNum != null">#{orderNum},</if>
 			<if test="leader != null and leader != ''">#{leader},</if>
 			<if test="phone != null and phone != ''">#{phone},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="status != null">#{status},</if>
			<if test="unitProvince != null">#{unitProvince},</if>
			<if test="unitCity != null">#{unitCity},</if>
			<if test="unitArea != null">#{unitArea},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="deptType != null">#{deptType},</if>
			<if test="deptLevel != null">#{deptLevel},</if>
			<if test="deptRegion != null">#{deptRegion},</if>
			<if test="maxPositions != null">#{maxPositions},</if>
			<if test="assignedPositions != null">#{assignedPositions},</if>
			<if test="extras != null">#{extras, javaType=java.util.HashMap, jdbcType=OTHER, typeHandler=com.metaorg.common.core.handler.JsonTypeHandler}::jsonb,</if>
		</trim>
	</insert>

	<update id="updateDept" parameterType="SysDept">
 		update sys_dept
 		<set>
 			<if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
 			<if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="orderNum != null">order_num = #{orderNum},</if>
 			<if test="leader != null">leader = #{leader},</if>
 			<if test="phone != null">phone = #{phone},</if>
 			<if test="email != null">email = #{email},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
			<if test="unitProvince != null">unit_province = #{unitProvince},</if>
			<if test="unitCity != null">unit_city = #{unitCity},</if>
			<if test="unitArea != null">unit_area = #{unitArea},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="deptType != null">dept_type = #{deptType},</if>
			<if test="deptLevel != null">dept_level = #{deptLevel},</if>
			<if test="deptRegion != null">dept_region = #{deptRegion},</if>
			<if test="maxPositions != null">max_positions = #{maxPositions},</if>
			<if test="assignedPositions != null">assigned_positions = #{assignedPositions},</if>
			<if test="extras != null">extras = #{extras, javaType=java.util.HashMap, jdbcType=OTHER, typeHandler=com.metaorg.common.core.handler.JsonTypeHandler}::jsonb,</if>
			</set>
 		where dept_id = #{deptId}
	</update>

	<update id="updateDeptChildren" parameterType="java.util.List">
	    update sys_dept set ancestors =
	    <foreach collection="depts" item="item" index="index"
	        separator=" " open="case dept_id" close="end">
	        when #{item.deptId} then #{item.ancestors}
	    </foreach>
	    where dept_id in
	    <foreach collection="depts" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.deptId}
	    </foreach>
	 </update>

	<delete id="deleteDeptById" parameterType="Long">
		update sys_dept set del_flag = '2' where dept_id = #{deptId}
	</delete>

	<update id="updateDeptStatusNormal" parameterType="Long">
 	    update sys_dept set status = '0' where dept_id in
 	    <foreach collection="array" item="deptId" open="(" separator="," close=")">
        	#{deptId}
        </foreach>
	</update>

</mapper>