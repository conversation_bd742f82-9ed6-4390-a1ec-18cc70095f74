<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收藏夹')" />
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .select-table .table td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
        #selectedPeopleModal .ui-layout-content{
            min-height:300px;
        }
        #selectedPeopleModal .fixed-table-container{
            max-height:380px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" id="deptId" name="deptId" th:value="${deptId}">
                        <input type="hidden" id="parentId" name="parentId">
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>关键字：</label>
                                    <input type="text" name="params[keyword]" placeholder="请输入关键字，支持模糊查询" data-toggle="tooltip" data-placement="bottom" title="支持按姓名、部门、工作单位及职务、性别、民族、籍贯等模糊查询"/>
                                </li>
                                <li>
                                    <label>姓名：</label>
                                    <input type="text" name="params[name]" placeholder="请输入姓名，支持模糊查询"/>
                                </li>
                                <li class="select-time">
									<label>收藏时间： </label>
									<input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]"/>
									<span>-</span>
									<input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]"/>
								</li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="searchFormReset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="btn-group-sm" id="my-favorite-toolbar" role="group">
                    <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                        <i class="fa fa-remove"></i> 删除
                    </a>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table" data-resizable="true" data-use-row-attr-func="true"></table>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js"/>
    <th:block th:include="include :: bootstrap-table-reorder-rows-js" />
    <th:block th:include="include :: bootstrap-table-resizable-js" />
    <th:block th:include="include :: bootstrap-table-fixed-columns-js" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:src="@{/js/moment.min.js}"></script>
    <script th:inline="javascript">
        /*<![CDATA[*/
        const editFlag = /*[[${@permission.hasPermi('library:person:edit')}]]*/ false;
        /*]]>*/
        const prefix = ctx + "library/favorite"

        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
            let panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 255 });
            // 回到顶部绑定
            if ($.fn.toTop !== undefined) {
                const opt = {
                    win:$('.ui-layout-center'),
                    doc:$('.ui-layout-center')
                };
                $('#scroll-up').toTop(opt);
            }

             $(document).on('keypress', function(e) {
                 if (e.keyCode == 13) {
                     e.preventDefault();
                     $.table.search();
                 }
             });
         });

        $(function() {
            const options = {
                toolbar: "my-favorite-toolbar",
                url: prefix + "/my_favorite/list",
                updateUrl: ctx + "library/person/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "收藏夹",
                sortName: "a.createTime desc, a.id",
				sortOrder: "desc",
                showToggle: false,
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                fixedColumns: true,
                fixedNumber: 3,
                fixedRightNumber: 1,
                uniqueId: 'id',
                columns: [
                    {
                        field: 'state',
                        checkbox: true
                    },
                    {
                        field: 'id',
                        title: 'ID',
                        visible: false
                    },
                    {
                        field: 'personId',
                        title: '人员ID',
                        visible: false
                    },
                    {
                        field: 'person.name',
                        title: '姓名'
                    },
                    {
                        field: 'person.dept.deptName',
                        title: '部门',
                        sortable: false
                    },
                    {
                        field: 'person.jobUnit',
                        title: '职务',
                        sortable: false
                    },
                    {
                        field: 'person.sex',
                        title: '性别',
                        sortable: false
                    },
                    // {
                    //     field: 'person.birthday',
                    //     title: '出生日期',
                    //     sortable: false
                    // },
                    {
                        field: 'person.nation',
                        title: '民族',
                        sortable: false
                    },
                    {
                        field: 'person.nativePlace',
                        title: '籍贯',
                        sortable: false
                    },
                    {
                        field: 'createTime',
                        title: '收藏时间',
                        formatter: function(value, row, index) {
                            if (!value || typeof value !== 'string') {
                                return '-';
                            }
                            return value.indexOf(' ') > -1 ? value.split(' ')[0] : value;
                        },
                        sortable: false
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.metaOperate.editTab(\'' + row.person.name + '的资料\', \'' + row.personId + '\')"><i class="fa fa-edit"></i>查看人员</a> ');
                            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        
        //重置筛选条件
        function searchFormReset() {
            $("#formId")[0].reset();  //不使用$.form.reset()，因为没办法设置默认搜索条件
            $(".selectpicker").val("");
            $(".selectpicker").selectpicker('refresh');
            $.table.search();
        }
    </script>
</body>
</html>