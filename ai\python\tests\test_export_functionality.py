"""
Excel导出功能测试
"""

import os
import sys
import tempfile
import pytest
import asyncio
from pathlib import Path
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from nfra_crawler.main import parse_args, NFRACrawlerApp
from nfra_crawler.services.excel_service import excel_service
from nfra_crawler.services.business_service import business_service
from nfra_crawler.models.database import BizSyncPunishItem, PunishDetailData


class TestExportFunctionality:
    """Excel导出功能测试类"""

    def test_parse_args_with_export(self):
        """测试命令行参数解析 - 包含--export参数"""
        with patch('sys.argv', ['main.py', '--export', 'test_output.xlsx']):
            args = parse_args()
            assert args.export == 'test_output.xlsx'

    def test_parse_args_without_export(self):
        """测试命令行参数解析 - 不包含--export参数"""
        with patch('sys.argv', ['main.py']):
            args = parse_args()
            assert args.export is None

    def test_nfra_crawler_app_init_with_export(self):
        """测试NFRACrawlerApp初始化 - 包含导出文件"""
        app = NFRACrawlerApp(export_file="test.xlsx")
        assert app.export_file == "test.xlsx"
        assert app.running is False

    def test_nfra_crawler_app_init_without_export(self):
        """测试NFRACrawlerApp初始化 - 不包含导出文件"""
        app = NFRACrawlerApp()
        assert app.export_file is None
        assert app.running is False

    @pytest.mark.asyncio
    async def test_app_run_export_mode(self):
        """测试应用运行 - 导出模式"""
        app = NFRACrawlerApp(export_file="test.xlsx")
        
        # Mock business_service.export_to_excel
        with patch.object(business_service, 'export_to_excel', new_callable=AsyncMock) as mock_export:
            with patch.object(business_service, 'health_check', new_callable=AsyncMock) as mock_health:
                with patch.object(app, '_validate_config') as mock_validate:
                    # 设置健康检查返回成功
                    mock_health.return_value = {"status": "healthy"}
                    
                    await app.run()
                    
                    # 验证调用
                    mock_validate.assert_called_once()
                    mock_health.assert_called_once()
                    mock_export.assert_called_once_with("test.xlsx")

    @pytest.mark.asyncio
    async def test_app_run_queue_mode(self):
        """测试应用运行 - 队列模式"""
        app = NFRACrawlerApp()  # 没有export_file
        
        with patch.object(app, 'startup', new_callable=AsyncMock) as mock_startup:
            with patch.object(app, 'cleanup', new_callable=AsyncMock) as mock_cleanup:
                with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                    # 模拟运行一次后停止
                    app.running = True
                    
                    async def side_effect(*args):
                        app.running = False  # 第一次sleep后停止
                    
                    mock_sleep.side_effect = side_effect
                    
                    await app.run()
                    
                    mock_startup.assert_called_once()
                    mock_cleanup.assert_called_once()

    def test_excel_service_validate_file_path(self):
        """测试Excel服务文件路径验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试正常文件路径
            file_path = os.path.join(temp_dir, "test.xlsx")
            abs_path = excel_service._validate_file_path(file_path)
            assert abs_path.endswith(".xlsx")
            assert os.path.isabs(abs_path)
            
            # 测试没有扩展名的文件路径
            file_path_no_ext = os.path.join(temp_dir, "test")
            abs_path_no_ext = excel_service._validate_file_path(file_path_no_ext)
            assert abs_path_no_ext.endswith(".xlsx")

    def test_excel_service_prepare_items_data(self):
        """测试Excel服务处罚公示列表数据准备"""
        # 创建测试数据
        items = [
            BizSyncPunishItem(
                id="test_id_1",
                sync_record_id="sync_1",
                request_id="req_1",
                title="测试处罚公示1",
                reward_punish_org="测试机构1",
                published_date=datetime(2024, 1, 1),
                source_name="新疆金融监管局",
                source_id="source_1",
                source_url="http://test1.com"
            ),
            BizSyncPunishItem(
                id="test_id_2",
                sync_record_id="sync_2",
                request_id="req_2",
                title="测试处罚公示2",
                reward_punish_org="测试机构2",
                published_date=datetime(2024, 1, 2),
                source_name="新疆金融监管局",
                source_id="source_2",
                source_url="http://test2.com"
            )
        ]
        
        data = excel_service._prepare_items_data(items)
        
        assert len(data) == 2
        assert data[0]['处罚公示标识'] == "test_id_1"
        assert data[0]['标题'] == "测试处罚公示1"
        assert data[0]['发布日期'] == "2024-01-01"
        assert data[1]['处罚公示标识'] == "test_id_2"
        assert data[1]['标题'] == "测试处罚公示2"
        assert data[1]['发布日期'] == "2024-01-02"

    def test_excel_service_prepare_details_data(self):
        """测试Excel服务处罚明细数据准备"""
        # 创建测试数据
        details = [
            PunishDetailData(
                serial_no="1",
                party_name="测试当事人1",
                party_position="测试职务1",
                punish_doc_no="测试文号1",
                violation="测试违法行为1",
                punish_basis="测试处罚依据1",
                punish_content="测试处罚内容1",
                punish_date=datetime(2024, 1, 1),
                decision_authority="测试决定机关1",
                party_type="个人",
                source_id="detail_1",
                source_url="http://detail1.com"
            )
        ]
        
        data = excel_service._prepare_details_data(details)
        
        assert len(data) == 1
        assert data[0]['序号'] == "1"
        assert data[0]['当事人名称'] == "测试当事人1"
        assert data[0]['当事人类型'] == "个人"
        assert data[0]['作出处罚决定的日期'] == "2024-01-01"

    def test_excel_service_export_punish_data(self):
        """测试Excel服务导出功能"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试数据
            items = [
                BizSyncPunishItem(
                    id="test_id",
                    sync_record_id="sync_id",
                    request_id="req_id",
                    title="测试处罚公示",
                    reward_punish_org="测试机构",
                    published_date=datetime(2024, 1, 1),
                    source_name="新疆金融监管局",
                    source_id="source_id",
                    source_url="http://test.com"
                )
            ]
            
            details = [
                PunishDetailData(
                    serial_no="1",
                    party_name="测试当事人",
                    party_position="测试职务",
                    punish_doc_no="测试文号",
                    violation="测试违法行为",
                    punish_content="测试处罚内容",
                    decision_authority="测试决定机关",
                    party_type="个人"
                )
            ]
            
            # 执行导出
            file_path = os.path.join(temp_dir, "test_export.xlsx")
            result_path = excel_service.export_punish_data(items, details, file_path)
            
            # 验证结果
            assert os.path.exists(result_path)
            assert result_path.endswith(".xlsx")
            assert os.path.isabs(result_path)

    @pytest.mark.asyncio
    async def test_business_service_export_to_excel_mock(self):
        """测试业务服务Excel导出功能 - 使用Mock"""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_business_export.xlsx")
            
            # Mock所有依赖
            with patch('nfra_crawler.services.business_service.data_service') as mock_data_service:
                with patch('nfra_crawler.services.business_service.crawler_service') as mock_crawler_service:
                    with patch('nfra_crawler.services.business_service.ai_service') as mock_ai_service:
                        with patch('nfra_crawler.services.business_service.excel_service') as mock_excel_service:
                            # 设置Mock返回值
                            mock_sync_record = MagicMock()
                            mock_sync_record.id = "test_sync_id"
                            mock_data_service.create_sync_record = AsyncMock(return_value=mock_sync_record)
                            mock_data_service.update_sync_record_progress = AsyncMock()
                            
                            mock_punish_items = [MagicMock()]
                            mock_crawler_service.crawl_punish_list = AsyncMock(return_value=mock_punish_items)
                            mock_crawler_service.crawl_punish_detail = AsyncMock(return_value="<html>test</html>")
                            
                            mock_detail_data = [MagicMock()]
                            mock_ai_service.clean_html_table = AsyncMock(return_value=mock_detail_data)
                            
                            mock_excel_service.export_punish_data.return_value = file_path
                            
                            # 执行导出
                            await business_service.export_to_excel(file_path)
                            
                            # 验证调用
                            mock_data_service.create_sync_record.assert_called_once()
                            mock_data_service.update_sync_record_progress.assert_called()
                            mock_crawler_service.crawl_punish_list.assert_called_once()
                            mock_excel_service.export_punish_data.assert_called_once()

    def test_command_line_integration(self):
        """测试命令行集成"""
        # 测试帮助信息包含--export参数
        with patch('sys.argv', ['main.py', '--help']):
            with pytest.raises(SystemExit):
                parse_args()

    @pytest.mark.asyncio
    async def test_export_mode_health_check_failure(self):
        """测试导出模式健康检查失败"""
        app = NFRACrawlerApp(export_file="test.xlsx")
        
        with patch.object(business_service, 'health_check', new_callable=AsyncMock) as mock_health:
            with patch.object(app, '_validate_config') as mock_validate:
                # 设置健康检查返回失败
                mock_health.return_value = {"status": "unhealthy", "error": "Database connection failed"}
                
                with pytest.raises(RuntimeError, match="健康检查失败"):
                    await app.run()
                
                mock_validate.assert_called_once()
                mock_health.assert_called_once()

    def test_excel_service_permission_error(self):
        """测试Excel服务权限错误"""
        # 尝试写入到只读目录（模拟权限错误）
        with patch('os.access', return_value=False):
            with pytest.raises(PermissionError):
                excel_service._validate_file_path("/readonly/test.xlsx")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
