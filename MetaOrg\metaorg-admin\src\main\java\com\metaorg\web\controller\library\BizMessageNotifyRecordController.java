package com.metaorg.web.controller.library;

import java.util.*;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.library.domain.*;
import com.metaorg.library.service.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;   
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.page.TableDataInfo;

/**
 * 消息通知记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-28
 */
@Controller
@RequestMapping("/library/message/record")
public class BizMessageNotifyRecordController extends BaseController {
    private String prefix = "library/message/record";

    @Autowired
    private IBizMessageNotifyRecordService bizMessageNotifyRecordService;

    @RequiresPermissions("library:message_notify_record:view")
    @GetMapping()
    public String record() {
        return prefix + "/record";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizMessageNotifyRecord bizMessageNotifyRecord, Map<String, Object> paramMap) {
        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizMessageNotifyRecord.getParams().put("keyword", keyword);
        }

        String businessType = Convert.toStr(paramMap.get("business_type"), "");
        if (StringUtils.isNotEmpty(businessType)) {
            bizMessageNotifyRecord.setBusinessType(businessType);
        }

        String messageType = Convert.toStr(paramMap.get("message_type"), "");
        if (StringUtils.isNotEmpty(messageType)) {
            bizMessageNotifyRecord.setMessageType(messageType);
        }

        Boolean isRead = Convert.toBool(paramMap.get("is_read"), null);
        if (isRead != null) {
            bizMessageNotifyRecord.setIsRead(isRead);
        }

        String sendTimeBegin = Convert.toStr(paramMap.get("sendTimeBegin"), "");
        if (StringUtils.isNotEmpty(sendTimeBegin)) {
            bizMessageNotifyRecord.getParams().put("sendTimeBegin", sendTimeBegin);
        }

        String sendTimeEnd = Convert.toStr(paramMap.get("sendTimeEnd"), "");
        if (StringUtils.isNotEmpty(sendTimeEnd)) {
            bizMessageNotifyRecord.getParams().put("sendTimeEnd", sendTimeEnd);
        }
    }

    /**
     * 查询消息通知记录列表
     */
    @RequiresPermissions("library:message_notify_record:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BizMessageNotifyRecord bizMessageNotifyRecord, @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizMessageNotifyRecord, paramMap);
        startPage();
        List<BizMessageNotifyRecord> list = bizMessageNotifyRecordService.selectBizMessageNotifyRecordList(bizMessageNotifyRecord);
        return getDataTable(list);
    }
}
