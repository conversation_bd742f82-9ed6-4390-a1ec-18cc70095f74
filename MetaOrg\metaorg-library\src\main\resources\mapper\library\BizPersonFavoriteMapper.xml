<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.library.mapper.BizPersonFavoriteMapper">

	<resultMap type="BizPersonFavorite" id="BizPersonFavoriteResult">
		<result property="id"     column="id"      />
		<result property="personId"     column="person_id"      />
		<result property="userId"     column="user_id"      />
		<result property="createTime"     column="create_time"      />
		<result property="createBy"     column="create_by"      />
		<result property="updateTime"     column="update_time"      />
		<result property="updateBy"     column="update_by"      />
	</resultMap>

	 <resultMap type="BizPersonFavorite" id="BizPersonFavoriteWithPersonResult" extends="BizPersonFavoriteResult">
        <association property="person"     column="person_id" javaType="BizPerson" resultMap="BizPersonResult" />
    </resultMap>

    <resultMap type="BizPerson" id="BizPersonResult">
        <result property="id"    column="person_id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="birthday"    column="birthday"    />
        <result property="nation"    column="nation"    />
        <result property="nativePlace"    column="native_place"    />
        <result property="jobDay"    column="job_day"    />
        <result property="jobUnit"    column="job_unit"    />
        <result property="qualificationJob"    column="qualification_job"    />
        <association property="dept"     column="dept_id" javaType="SysDept" resultMap="deptResult" />
    </resultMap>

    <resultMap id="deptResult" type="SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="deptName"  column="dept_name"   />
    </resultMap>

	<sql id="selectBizPersonFavoriteFields">
		select
		    a.id,
		    a.person_id,
		    a.user_id,
		    a.create_time,
		    a.create_by,
		    a.update_time,
		    a.update_by
	</sql>

	<sql id="selectBizPersonFavoriteWithPersonVo">
		<include refid="selectBizPersonFavoriteFields"/>
		,
		p.id as person_id,
		p.name,
		p.sex,
		p.birthday,
		p.nation,
		p.native_place,
		p.job_day,
		p.job_unit,
		p.qualification_job,
		d.dept_id,
		d.dept_name
		from biz_person_favorite a
		inner join biz_person p on a.person_id = p.id
		inner join sys_dept d on p.dept_id = d.dept_id
	</sql>

	<sql id="selectBizPersonFavoriteVo">
		<include refid="selectBizPersonFavoriteFields"/>
		from biz_person_favorite a
	</sql>

	<select id="selectBizPersonFavoriteList" parameterType="BizPersonFavorite" resultMap="BizPersonFavoriteWithPersonResult">
		<include refid="selectBizPersonFavoriteWithPersonVo"/>
		<where>
			<if test="personId != null and personId != ''">
				and a.person_id = #{personId}
			</if>
			<if test="userId != null">
				and a.user_id = #{userId}
			</if>
			<if test="params.name != null and params.name != ''"><!-- 姓名检索 -->
				AND p.name LIKE '%' || #{params.name} || '%'
			</if>
			<if test="params.keyword != null and params.keyword != ''"><!-- 关键字检索 -->
				AND (
					p.name LIKE '%' || #{params.keyword} || '%'
					OR d.dept_name LIKE '%' || #{params.keyword} || '%'
					OR p.job_unit LIKE '%' || #{params.keyword} || '%'
					OR p.sex LIKE '%' || #{params.keyword} || '%'
					OR p.nation LIKE '%' || #{params.keyword} || '%'
					OR p.native_place LIKE '%' || #{params.keyword} || '%'
				)
			</if>
			<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				AND a.create_time &gt;= #{params.beginTime}::date
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				AND a.create_time &lt;= (#{params.endTime}::date + INTERVAL '1 day' - INTERVAL '1 second')
			</if>
			AND EXISTS (
                SELECT 1 FROM biz_person_dept vpd where vpd.person_id = a.person_id
                <if test="params.deptId != null and params.deptId != ''">
                    AND (vpd.dept_id = #{params.deptId} OR vpd.dept_id in (SELECT t.dept_id FROM sys_dept t where FIND_IN_SET (#{params.deptId},ancestors)))
                </if>
                    AND 'params.dataScope' = 'params.dataScope'
            )
		</where>
	</select>
	
	<select id="selectBizPersonFavoriteByUserId" resultMap="BizPersonFavoriteResult">
		<include refid="selectBizPersonFavoriteWithPersonVo"/>
		where a.user_id = #{userId} 
		<if test="personId != null">
			and person_id = #{personId}
		</if>
	</select>

	<select id="selectBizPersonFavoriteByPersonId" resultMap="BizPersonFavoriteResult">
		<include refid="selectBizPersonFavoriteWithPersonVo"/>
		where a.person_id = #{personId} 
		<if test="userId != null">
			and user_id = #{userId}
		</if>
	</select>

	<select id="selectBizPersonFavoriteByPersonIds" resultMap="BizPersonFavoriteResult">
		<include refid="selectBizPersonFavoriteWithPersonVo"/>
		where a.user_id = #{userId} and a.person_id in
		<foreach collection="personIds" item="personId" open="(" separator="," close=")">
			#{personId}
		</foreach>
	</select>

  <!-- 删除用户收藏，如果personIds不为空，则删除指定人员收藏 -->
	<delete id="deleteBizPersonFavoriteByUserId" parameterType="Long">
		delete from biz_person_favorite where user_id = #{userId} 
		<if test="personIds != null">
			and person_id in
			<foreach collection="personIds" item="personId" open="(" separator="," close=")">
				#{personId}
			</foreach>
		</if>
	</delete>
	
	<delete id="deleteBizPersonFavorite" parameterType="Long">
 		delete from biz_person_favorite where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
    	</foreach>
 	</delete>
	
	<insert id="batchBizPersonFavorite">
		insert into biz_person_favorite(id, person_id, user_id, create_time, create_by_id, create_by, update_time, update_by_id, update_by) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.id},#{item.personId},#{item.userId}, #{item.createTime}, #{item.createById}, #{item.createBy}, #{item.updateTime}, #{item.updateById}, #{item.updateBy})
		</foreach>
	</insert>
	
	<delete id="deleteBizPersonFavoriteInfo" parameterType="BizPersonFavorite">
		delete from biz_person_favorite where person_id=#{personId} and user_id=#{userId}
	</delete>
	
	<delete id="deleteBizPersonFavoriteInfos">
	    delete from biz_person_favorite where person_id=#{personId} and user_id in
 	    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
 	        #{userId}
      </foreach>
	</delete>

	<delete id="deleteBizPersonFavoriteByIds">
        delete from biz_person_favorite where user_id=#{userId} 
		and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 