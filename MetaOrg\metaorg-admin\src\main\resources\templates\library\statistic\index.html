<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <th:block th:include="include :: header('干部统计分析')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <style>
      .col-left {
        border-right: 1px solid #ddd;
      }
      .gray-bg {
        padding-right: 0 !important;
      }
      .chart-container {
        height: 300px;
      }
      .bar-chart-container {
        height: 500px;
      }
      .container-fluid {
        padding: 16px 20px;
      }

      /* Tab 标签页样式 */
      .top-nav-bar {
        display: flex;
        background-color: #fff;
        padding: 0 16px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 16px;
        border-radius: 6px 6px 0 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
      .top-nav-bar .nav-item {
        padding: 12px 16px;
        margin-right: 16px;
        cursor: pointer;
        color: #6c757d;
        font-size: 14px;
        border-bottom: 2px solid transparent;
        transition: color 0.3s, border-color 0.3s;
      }
      .top-nav-bar .nav-item.active {
        color: #007bff;
        font-weight: 500;
        border-bottom: 2px solid #007bff;
      }
      .top-nav-bar .nav-item:hover {
        color: #007bff;
      }
      .top-nav-bar .nav-item i {
        margin-right: 6px;
      }

      .tab-content-wrapper .tab-pane {
        display: none;
      }
      .tab-content-wrapper .tab-pane.active {
        display: block;
      }
      .tab-content-wrapper {
        background-color: #fff;
        border-radius: 0 0 6px 6px;
        min-height: 600px;
      }

      /* 岗位条线分析样式 */
      .position-timeline-container {
        height: 400px;
        margin-bottom: 20px;
      }

      .charts-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px; /* 图表之间的间距 */
        justify-content: center; /* 水平居中 */
      }
      .chart-item {
        width: calc(50% - 20px); /* 每个图表占据容器宽度的一半，减去间距 */
        min-width: 300px; /* 最小宽度，防止过小 */
        height: 350px; /* 固定高度 */
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        box-sizing: border-box; /* 包含 padding 和 border 在宽度内 */
        padding: 15px; /* 内部留白 */
        display: flex; /* 确保 ECharts 容器能撑满 */
        align-items: center;
        justify-content: center;
        overflow: hidden; /* 防止内容溢出 */
      }
      /* 响应式调整：小屏幕下每个图表占据整行 */
      @media (max-width: 768px) {
        .chart-item {
          width: 100%;
          min-width: unset; /* 取消最小宽度限制 */
        }
      }
    </style>
  </head>
  <body class="white-bg container-fluid">
    <div class="ui-layout-west">
      <div class="box box-main">
        <div class="box-header">
          <div class="box-title"><i class="fa fa-sitemap"></i> 组织机构</div>
          <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display: none"><i class="fa fa-chevron-up"></i></button>
            <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
            <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
          </div>
        </div>
        <div class="ui-layout-content">
          <input name="treeId" type="hidden" id="treeId" th:value="${deptId}" />
          <div id="tree" class="ztree"></div>
        </div>
      </div>
    </div>
    <div class="ui-layout-center">
      <div class="container-div">
        <input type="hidden" id="deptId" name="deptId" th:value="${deptId}" />

        <!-- Tab 导航栏 -->
        <div class="top-nav-bar">
          <div class="nav-item active" data-target="#tab-personnel-analysis"><i class="fa fa-users"></i> 人员结构分析</div>
          <div class="nav-item" data-target="#tab-organization-analysis"><i class="fa fa-sitemap"></i> 组织层面分析</div>
          <div class="nav-item" data-target="#tab-position-analysis"><i class="fa fa-line-chart"></i> 岗位条线分析</div>
          <div class="nav-item" data-target="#tab-adjustment-analysis"><i class="fa fa-exchange"></i> 干部调整情况分析</div>
        </div>

        <!-- Tab 内容区域 -->
        <div class="tab-content-wrapper">
          <!-- 人员结构分析标签页 -->
          <div class="tab-pane active" id="tab-personnel-analysis">
            <!-- 年龄和性别分布分析 -->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-birthday-cake"></i> 年龄、性别分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-6 col-left">
                        <div id="personnel_age_chart" class="chart-container"></div>
                      </div>
                      <div class="col-sm-6">
                        <div id="personnel_gender_chart" class="chart-container"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 教育背景分析 -->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-graduation-cap"></i> 学历分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-6 col-left">
                        <div id="personnel_fulltime_education_chart" class="chart-container"></div>
                      </div>
                      <div class="col-sm-6">
                        <div id="personnel_inservice_education_chart" class="chart-container"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 民族分布图表 -->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-globe"></i> 民族分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div id="personnel_nation_chart" class="chart-container"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 组织层面分析标签页 -->
          <div class="tab-pane" id="tab-organization-analysis">
            <!--总体情况-->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-pie-chart"></i> 人员总体分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-12">
                        <div id="overall_chart" class="chart-container"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--性别分析-->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-users"></i> 性别分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-12">
                        <div id="gender-charts-container" class="charts-container">
                          <!-- ECharts 图表将在这里动态添加 -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--配备分析-->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-users"></i> 配备分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-12">
                        <div id="allocation-charts-container" class="charts-container">
                          <!-- ECharts 图表将在这里动态添加 -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--缺位分析-->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-users"></i> 缺位分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-12">
                        <div id="omission-charts-container" class="charts-container">
                          <!-- ECharts 图表将在这里动态添加 -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--学历分析-->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-graduation-cap"></i> 学历分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-12">
                        <div id="edu-charts-container" class="charts-container">
                          <!-- ECharts 图表将在这里动态添加 -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 岗位条线分析标签页 -->
          <div class="tab-pane" id="tab-position-analysis">
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-clock-o"></i> 岗位条线分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div id="position_analysis_timeline_chart" class="position-timeline-container"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 干部调整情况分析标签页 -->
          <div class="tab-pane" id="tab-adjustment-analysis">
            <!-- 已调整情况分析 -->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-check-circle text-success"></i> 已调整情况分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-6 col-left">
                        <div id="completed_adjustment_pie_chart" class="chart-container"></div>
                      </div>
                      <div class="col-sm-6">
                        <div id="completed_adjustment_bar_chart" class="chart-container"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 需调整情况分析 -->
            <div class="row">
              <div class="col-sm-12">
                <div class="ibox float-e-margins">
                  <div class="ibox-title">
                    <h5><i class="fa fa-clock-o text-warning"></i> 需调整情况分析</h5>
                  </div>
                  <div class="ibox-content">
                    <div class="row">
                      <div class="col-sm-6 col-left">
                        <div id="pending_adjustment_pie_chart" class="chart-container"></div>
                      </div>
                      <div class="col-sm-6">
                        <div id="adjustment_flow_chart" class="chart-container"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <th:block th:include="include :: echarts-js" />
    <script th:inline="javascript">
      /*<![CDATA[*/
      var ctx = /*[[@{/}]]*/ '';
      /*]]>*/

      // 存储图表实例
      var chartInstances = {};

      function queryDeptTree() {
        var url = ctx + 'system/user/deptTreeData';
        var options = {
          url: url,
          expandLevel: 1,
          onClick: zOnClick,
        };
        $.tree.init(options);

        function zOnClick(event, treeId, treeNode) {
          $('#deptId').val(treeNode.id);
          if (typeof handlerConfirmRemove === 'function') {
            handlerConfirmRemove();
          }
          // 重新加载当前Tab的数据
          var activeTab = $('.top-nav-bar .nav-item.active').data('target');
          refreshTabData(activeTab);
        }
      }

      $(function () {
        var panehHidden = false;
        if ($(this).width() < 769) {
          panehHidden = true;
        }
        var libraryType = $('#libraryType').val();
        if ($('#personnelType').length) {
          $('#personnelType').val(libraryType);
        }

        $('body').layout({ initClosed: panehHidden, west__size: 255 });

        // 回到顶部绑定
        if ($.fn.toTop !== undefined) {
          var opt = {
            win: $('.ui-layout-center'),
            doc: $('.ui-layout-center'),
          };
          $('#scroll-up').toTop(opt);
        }

        queryDeptTree();

        // 树操作按钮
        $('#btnExpand').click(function () {
          $._tree.expandAll(true);
          $(this).hide();
          $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function () {
          $._tree.expandAll(false);
          $(this).hide();
          $('#btnExpand').show();
        });

        $('#btnRefresh').click(function () {
          queryDeptTree();
        });

        // Tab切换事件
        $('.top-nav-bar .nav-item').on('click', function () {
          var targetPaneId = $(this).data('target');

          // 移除所有Tab的active类
          $('.top-nav-bar .nav-item').removeClass('active');
          // 为当前点击的Tab添加active类
          $(this).addClass('active');

          // 隐藏所有Tab内容
          $('.tab-content-wrapper .tab-pane').removeClass('active');

          // 显示当前Tab对应的内容
          var targetPane = $(targetPaneId);
          if (targetPane.length) {
            targetPane.addClass('active');
          }

          // 加载对应Tab的数据
          refreshTabData(targetPaneId);

          // 延迟重绘图表确保容器已显示
          setTimeout(function () {
            resizeChartsInTab(targetPaneId);
          }, 100);
        });

        // 默认加载第一个Tab的数据
        initPersonnelAnalysisTab();

        // 为组织层面分析Tab初始化占位图表
        initPlaceholderCharts();
      });

      // 刷新Tab数据
      function refreshTabData(tabId) {
        switch (tabId) {
          case '#tab-personnel-analysis':
            initPersonnelAnalysisTab();
            break;
          case '#tab-organization-analysis':
            initOrganizationAnalysisTab();
            break;
          case '#tab-position-analysis':
            initPositionAnalysisTab();
            break;
          case '#tab-adjustment-analysis':
            initAdjustmentAnalysisTab();
            break;
        }
      }

      // 重绘指定Tab中的图表
      function resizeChartsInTab(tabId) {
        var chartsToResize = [];
        switch (tabId) {
          case '#tab-personnel-analysis':
            chartsToResize = [
              'personnel_age_chart',
              'personnel_gender_chart',
              'personnel_fulltime_education_chart',
              'personnel_inservice_education_chart',
              'personnel_nation_chart',
            ];
            break;
          case '#tab-organization-analysis':
            chartsToResize = [
              'overall_chart',
            ];
            // 添加动态创建的图表
            Object.keys(chartInstances).forEach(function(chartId) {
              if (chartId.startsWith('team_gender_chart_') ||
                  chartId.startsWith('team_allocation_chart_') ||
                  chartId.startsWith('team_omission_chart_') ||
                  chartId.startsWith('placeholder_')) {
                chartsToResize.push(chartId);
              }
            });
            break;
          case '#tab-position-analysis':
            chartsToResize = ['position_analysis_timeline_chart'];
            break;
          case '#tab-adjustment-analysis':
            chartsToResize = ['completed_adjustment_pie_chart', 'completed_adjustment_bar_chart', 'pending_adjustment_pie_chart', 'adjustment_flow_chart'];
            break;
        }

        chartsToResize.forEach(function (chartId) {
          if (chartInstances[chartId]) {
            chartInstances[chartId].resize();
          }
        });
      }

      // 初始化人员结构分析Tab
      function initPersonnelAnalysisTab() {
        initPersonnelAgeChart();
        initPersonnelGenderChart();
        initPersonnelFulltimeEducationChart();
        initPersonnelInserviceEducationChart();
        initPersonnelNationChart();
      }

      // 人员年龄分布图表
      function initPersonnelAgeChart() {
        var chart = echarts.init(document.getElementById('personnel_age_chart'));
        chartInstances['personnel_age_chart'] = chart;

        // 模拟数据
        var ageData = [
          { name: '30岁以下', value: 45 },
          { name: '30-35岁', value: 78 },
          { name: '36-40岁', value: 95 },
          { name: '41-45岁', value: 67 },
          { name: '46-50岁', value: 43 },
          { name: '50岁以上', value: 32 },
        ];

        const option = {
          title: {
            text: '年龄分布',
            left: 'left',
          },
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}人',
          },
          legend: {
            left: 'right',
            data: ['人数'],
          },
          color: ['#EFD085'],
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: ageData.map(function (item) {
              return item.name;
            }),
            axisLabel: {
              interval: 0,
              rotate: 0,
            },
          },
          yAxis: {
            type: 'value',
            minInterval: 1,
          },
          series: [
            {
              name: '人数',
              type: 'bar',
              barWidth: '60%',
              data: ageData.map(function (item) {
                return item.value;
              }),
              itemStyle: {
                borderRadius: [4, 4, 0, 0],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 4,
                shadowOffsetY: 2,
              },
              label: {
                show: true,
                position: 'top',
              },
            },
          ],
        };

        chart.setOption(option);
      }

      // 人员性别分布图表
      function initPersonnelGenderChart() {
        var chart = echarts.init(document.getElementById('personnel_gender_chart'));
        chartInstances['personnel_gender_chart'] = chart;

        // 模拟数据
        var genderData = [
          { name: '男', value: 198, percentage: '55.0' },
          { name: '女', value: 162, percentage: '45.0' },
        ];

        const option = {
          title: {
            text: '性别分布',
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}人 ({d}%)',
          },
          legend: {
            left: 'right',
          },
          color: ['#525CBF', '#A0CB7E'],
          series: [
            {
              name: '性别分布',
              type: 'pie',
              radius: '60%',
              center: ['50%', '60%'],
              data: genderData,
              label: {
                formatter: function (data) {
                  return data.name + '\n' + data.value + '人\n(' + data.data.percentage + '%)';
                },
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            },
          ],
        };

        chart.setOption(option);
      }

      // 人员全日制教育分布图表
      function initPersonnelFulltimeEducationChart() {
        var chart = echarts.init(document.getElementById('personnel_fulltime_education_chart'));
        chartInstances['personnel_fulltime_education_chart'] = chart;

        // 模拟数据
        var fulltimeEducationData = [
          { name: '博士', value: 12 },
          { name: '硕士', value: 68 },
          { name: '本科', value: 145 },
          { name: '专科', value: 42 },
          { name: '高中及以下', value: 8 },
        ];

        const option = {
          title: {
            text: '全日制教育',
            left: 'left',
          },
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}人',
          },
          legend: {
            left: 'right',
            data: ['人数'],
          },
          color: ['#9164B0'],
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: fulltimeEducationData.map(function (item) {
              return item.name;
            }),
            axisLabel: {
              interval: 0,
            },
          },
          yAxis: {
            type: 'value',
            minInterval: 1,
          },
          series: [
            {
              name: '人数',
              type: 'bar',
              barWidth: '60%',
              data: fulltimeEducationData.map(function (item) {
                return item.value;
              }),
              itemStyle: {
                borderRadius: [4, 4, 0, 0],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 4,
                shadowOffsetY: 2,
              },
              label: {
                show: true,
                position: 'top',
              },
            },
          ],
        };

        chart.setOption(option);
      }

      // 人员在职教育分布图表
      function initPersonnelInserviceEducationChart() {
        var chart = echarts.init(document.getElementById('personnel_inservice_education_chart'));
        chartInstances['personnel_inservice_education_chart'] = chart;

        // 模拟数据
        var inserviceEducationData = [
          { name: '博士', value: 8 },
          { name: '硕士', value: 35 },
          { name: '本科', value: 67 },
          { name: '专科', value: 28 },
          { name: '高中及以下', value: 12 },
        ];

        const option = {
          title: {
            text: '在职教育',
            left: 'left',
          },
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}人',
          },
          legend: {
            left: 'right',
            data: ['人数'],
          },
          color: ['#73c0de'],
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: inserviceEducationData.map(function (item) {
              return item.name;
            }),
            axisLabel: {
              interval: 0,
            },
          },
          yAxis: {
            type: 'value',
            minInterval: 1,
          },
          series: [
            {
              name: '人数',
              type: 'bar',
              barWidth: '60%',
              data: inserviceEducationData.map(function (item) {
                return item.value;
              }),
              itemStyle: {
                borderRadius: [4, 4, 0, 0],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 4,
                shadowOffsetY: 2,
              },
              label: {
                show: true,
                position: 'top',
              },
            },
          ],
        };

        chart.setOption(option);
      }

      // 人员民族分布图表
      function initPersonnelNationChart() {
        var chart = echarts.init(document.getElementById('personnel_nation_chart'));
        chartInstances['personnel_nation_chart'] = chart;

        // 模拟数据
        var nationData = [
          { name: '汉族', value: 312 },
          { name: '回族', value: 18 },
          { name: '满族', value: 12 },
          { name: '蒙古族', value: 8 },
          { name: '壮族', value: 6 },
          { name: '其他', value: 4 },
        ];

        const option = {
          title: {
            text: '民族分布',
            left: 'left',
          },
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}人',
          },
          legend: {
            left: 'right',
            data: ['人数'],
          },
          color: ['#E67E9A'],
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: nationData.map(function (item) {
              return item.name;
            }),
            axisLabel: {
              interval: 0,
              rotate: nationData.length > 6 ? 30 : 0,
            },
          },
          yAxis: {
            type: 'value',
            minInterval: 1,
          },
          series: [
            {
              name: '人数',
              type: 'bar',
              barWidth: '60%',
              data: nationData.map(function (item) {
                return item.value;
              }),
              itemStyle: {
                borderRadius: [4, 4, 0, 0],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 4,
                shadowOffsetY: 2,
              },
              label: {
                show: true,
                position: 'top',
              },
            },
          ],
        };

        chart.setOption(option);
      }

      // 初始化组织层面分析Tab（原有数据）
      function initOrganizationAnalysisTab() {
        var overallChart = echarts.init(document.getElementById('overall_chart'));

        // 保存图表实例
        chartInstances['overall_chart'] = overallChart;

        // 清空并添加占位图表
        initPlaceholderCharts();

        var deptId = $('#deptId').val();
        var url = ctx + 'library/statistic/orgs/' + deptId + '/levels';
        $.get(url, function (result) {

          var data = {};
          if (result.data) {
            data = result.data;
          }

          var totalCount = data.total_count || 0;
          var totalMaleCount = 0;
          var totalFemaleCount = 0;
          var leadingGroupAllocationList = data.leading_group_allocation || [];
          var positionEduList = data.edu || [];

          if (leadingGroupAllocationList.length > 0) {
            // 清空三个容器，准备添加真实数据图表
            clearChartsContainer('gender-charts-container');
            clearChartsContainer('allocation-charts-container');
            clearChartsContainer('omission-charts-container');
            clearChartsContainer('edu-charts-container');
          }

          const levelDict = {};
          leadingGroupAllocationList.forEach(function (item) {
            if (!levelDict[item.leading_group_level]) {
              levelDict[item.leading_group_level] = { count: 0, allocated_count: 0, male_count: 0, female_count: 0 };
            }

            let allocatedCount = item.allocated_count || 0;
            item.count = item.count || 0;

            if (allocatedCount > item.count) {
              allocatedCount = item.count;
            }

            levelDict[item.leading_group_level].count += item.count;
            levelDict[item.leading_group_level].allocated_count += allocatedCount;
            levelDict[item.leading_group_level].male_count += item.male_count || 0;
            levelDict[item.leading_group_level].female_count += item.female_count || 0;

            totalMaleCount += item.male_count || 0;
            totalFemaleCount += item.female_count || 0;
          });

          const eduCategorySet = new Set();
          positionEduList.forEach(function (item) {
            if (item.edu_category) {
              eduCategorySet.add(item.edu_category);
            }
          });

          let pieData = [];
          let leaderCount = 0;
          let levels = Object.keys(levelDict);
          levels.forEach((level, index) => {
            let allocatedCount = levelDict[level].allocated_count;
            leaderCount = leaderCount + allocatedCount;
            pieData.push({
              name: level,
              value: allocatedCount,
              percentage: totalCount > 0 ? ((allocatedCount / totalCount) * 100).toFixed(2) : '0.00',
            });

            let maleCount = levelDict[level].male_count || 0;
            let femaleCount = levelDict[level].female_count || 0;
            if (maleCount < 0) {
              maleCount = 0;
            }
            if (femaleCount < 0) {
              femaleCount = 0;
            }

            const genderData = [{
              name: '男',
              value: maleCount,
              percentage: allocatedCount > 0 ? ((maleCount / allocatedCount) * 100).toFixed(2) : '0.00',
            },
            {
              name: '女',
              value: femaleCount,
              percentage: allocatedCount > 0 ? ((femaleCount / allocatedCount) * 100).toFixed(2) : '0.00',
            }];

            initTeamGenderCharts(`team_gender_chart_${index}`, `${level}性别分布`, genderData);

            let teamCount = levelDict[level].count || 0;
            let teamAllocatedCount = levelDict[level].allocated_count || 0;
            let teamOmissionCount = teamCount - teamAllocatedCount;
            if (teamOmissionCount < 0) {
              teamOmissionCount = 0;
            }

            const allocationData = [{
              name: level,
              value: teamAllocatedCount,
              percentage: teamCount > 0 ? ((teamAllocatedCount / teamCount) * 100).toFixed(2) : '0.00',
            },
            {
              name: '缺位',
              value: teamOmissionCount,
              percentage: teamCount > 0 ? ((teamOmissionCount / teamCount) * 100).toFixed(2) : '0.00',
            }];

            initTeamAllocationCharts(`team_allocation_chart_${index}`, `${level}配备分布`, allocationData);
            
            const omissionData = leadingGroupAllocationList
              .filter(item => item.leading_group_level === level)
              .map(item => {
                let omissionCount = item.count - (item.allocated_count || 0);
                if (omissionCount < 0) {
                  omissionCount = 0;
                }
                return {
                  name: item.position_name,
                  value: omissionCount,
                };
              });
            initTeamOmissionCharts(`team_omission_chart_${index}`, `${level}缺位分布`, omissionData);

            eduCategorySet.forEach(function (eduCategory, categoryIndex) {
              const eduData = positionEduList
                .filter(item => item.leading_group_level === level && item.edu_category === eduCategory)
                .map(item => {
                  return {
                    name: item.name,
                    value: item.value,
                  };
                });
              initTeamEduCharts(`team_edu_chart_${index}_${categoryIndex}`, `${level}学历分布-${eduCategory}`, eduData);
            });
          });

          let otherTeamCount = totalCount - leaderCount;
          if (otherTeamCount < 0) {
            otherTeamCount = 0;
          }

          pieData.push({
            name: '其他',
            value: otherTeamCount,
            percentage: totalCount > 0 ? ((otherTeamCount / totalCount) * 100).toFixed(2) : '0.00',
          });

          var overallOption = {
            title: {
              text: '人员总体情况',
              left: 'left',
            },
            tooltip: {
              trigger: 'item',
            },
            grid: {
              x: 30,
              x2: 40,
              y2: 24,
            },
            color: ['#525CBF', '#A0CB7E', '#E68C59'],
            legend: {
              left: 'right',
            },
            series: [
              {
                name: '总体情况',
                type: 'pie',
                data: pieData,
                label: {
                  formatter: function (data) {
                    return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          };

          overallChart.setOption(overallOption);
        });
      }

      // 初始化岗位条线分析Tab
      function initPositionAnalysisTab() {
        var chart = echarts.init(document.getElementById('position_analysis_timeline_chart'));
        chartInstances['position_analysis_timeline_chart'] = chart;

        // 模拟时间轴数据
        var timelineData = [
          {
            name: '董事长',
            data: [
              ['1998-01-01', '2020-12-31', '王明'],
              ['2021-01-01', '2023-12-31', '张三'],
              ['2024-01-01', '2025-12-31', '李四'],
            ],
          },
          {
            name: '总经理',
            data: [
              ['2000-06-01', '2018-12-31', '赵五'],
              ['2019-01-01', '2022-12-31', '钱六'],
              ['2023-01-01', '2025-12-31', '孙七'],
            ],
          },
          {
            name: '副总经理',
            data: [
              ['2002-03-01', '2019-12-31', '周八'],
              ['2020-01-01', '2024-06-30', '吴九'],
              ['2024-07-01', '2025-12-31', '郑十'],
            ],
          },
        ];

        // 构建时间轴图表数据
        var categories = timelineData.map(function (item) {
          return item.name;
        });
        var series = [];
        var personColors = {};
        var colorPalette = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];
        var colorIndex = 0;

        timelineData.forEach(function (position, positionIndex) {
          position.data.forEach(function (period, periodIndex) {
            var startDate = new Date(period[0]);
            var endDate = new Date(period[1]);
            var personName = period[2];

            // 为每个人员分配唯一颜色
            if (!personColors[personName]) {
              personColors[personName] = colorPalette[colorIndex % colorPalette.length];
              colorIndex++;
            }

            series.push([positionIndex, startDate.getTime(), endDate.getTime(), endDate.getTime() - startDate.getTime(), personName, personColors[personName]]);
          });
        });

        const option = {
          title: {
            text: '岗位人员变动时间线',
            left: 'left',
          },
          tooltip: {
            formatter: function (params) {
              var start = new Date(params.value[1]);
              var end = new Date(params.value[2]);
              return (
                params.value[4] +
                '<br/>' +
                start.getFullYear() +
                '年' +
                (start.getMonth() + 1) +
                '月 - ' +
                end.getFullYear() +
                '年' +
                (end.getMonth() + 1) +
                '月'
              );
            },
          },
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          xAxis: {
            type: 'time',
            axisLabel: {
              formatter: function (value) {
                return new Date(value).getFullYear() + '年';
              },
            },
          },
          yAxis: {
            type: 'category',
            data: categories,
          },
          series: [
            {
              type: 'custom',
              renderItem: function (params, api) {
                var categoryIndex = api.value(0);
                var start = api.coord([api.value(1), categoryIndex]);
                var end = api.coord([api.value(2), categoryIndex]);
                var height = api.size([0, 1])[1] * 0.6;
                var personColor = api.value(5); // 使用预分配的颜色
                var personName = api.value(4);

                var rectShape = echarts.graphic.clipRectByRect(
                  {
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  }
                );

                var textX = (start[0] + end[0]) / 2;
                var textY = start[1];
                var textWidth = end[0] - start[0];

                var group = {
                  type: 'group',
                  children: [],
                };

                // 添加矩形
                if (rectShape) {
                  group.children.push({
                    type: 'rect',
                    shape: rectShape,
                    style: {
                      fill: personColor,
                      stroke: '#333',
                      lineWidth: 1,
                      opacity: 0.8,
                    },
                  });

                  // 如果矩形足够宽，添加人员姓名标签
                  if (textWidth > 60) {
                    group.children.push({
                      type: 'text',
                      style: {
                        x: textX,
                        y: textY,
                        text: personName.length > 4 ? personName.substring(0, 4) + '...' : personName,
                        textAlign: 'center',
                        textVerticalAlign: 'middle',
                        fontSize: 12,
                        fontWeight: 'bold',
                        fill: '#fff',
                        stroke: '#000',
                        lineWidth: 1,
                      },
                    });
                  }
                }

                return rectShape ? group : null;
              },
              encode: {
                x: [1, 2],
                y: 0,
              },
              data: series,
            },
          ],
        };

        chart.setOption(option);
      }

      // 干部性别图表
      function initTeamGenderCharts(id, title, data) {
        const chartsContainer = document.getElementById('gender-charts-container');

        let chartDiv = document.getElementById(id);
        if (!chartDiv) {
          chartDiv = document.createElement('div');
          chartDiv.id = id;
          chartDiv.className = 'chart-item';
          chartsContainer.appendChild(chartDiv);
        }

        let chart = echarts.init(chartDiv);
        chartInstances[id] = chart;

        if (!data) {
          data = [];
        }

        const option = {
          title: {
            text: title,
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
          },
          legend: {
            left: 'right',
          },
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          color: ['#525CBF', '#A0CB7E'],
          series: [
            {
              name: '性别分布',
              type: 'pie',
              radius: '60%',
              center: ['50%', '60%'],
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                formatter: function (data) {
                  return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                },
              },
            },
          ],
        };

        if (data.length == 0) {
          option.graphic = {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 16,
              fill: '#999'
            }
          };
        }

        chart.setOption(option);
      }

      // 干部配备图表
      function initTeamAllocationCharts(id, title, data) {
        const chartsContainer = document.getElementById('allocation-charts-container');

        let chartDiv = document.getElementById(id);
        if (!chartDiv) {
          chartDiv = document.createElement('div');
          chartDiv.id = id;
          chartDiv.className = 'chart-item';
          chartsContainer.appendChild(chartDiv);
        }

        let chart = echarts.init(chartDiv);
        chartInstances[id] = chart;

        if (!data) {
          data = [];
        }

        const option = {
          title: {
            text: title,
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
          },
          legend: {
            left: 'right',
          },
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          color: ['#525CBF', '#A0CB7E', '#E68C59'],
          series: [
            {
              name: '配备分布',
              type: 'pie',
              radius: '60%',
              center: ['50%', '60%'],
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                formatter: function (data) {
                  return data.name + ' ' + data.value + '人 (' + data.data.percentage + '%)';
                },
              },
            },
          ],
        };

        if (data.length == 0) {
          option.graphic = {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 16,
              fill: '#999'
            }
          };
        }

        chart.setOption(option);
      }

      // 干部缺位图表
      function initTeamOmissionCharts(id, title, data) {
        const chartsContainer = document.getElementById('omission-charts-container');

        let chartDiv = document.getElementById(id);
        if (!chartDiv) {
          chartDiv = document.createElement('div');
          chartDiv.id = id;
          chartDiv.className = 'chart-item';
          chartsContainer.appendChild(chartDiv);
        }

        let chart = echarts.init(chartDiv);
        chartInstances[id] = chart;

        if (!data) {
          data = [];
        }

        const option = {
          title: {
            text: title,
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: 缺 {c} 人 ({d}%)',
          },
          legend: {
            orient: 'vertical',
            left: 'right',
          },
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          series: [
            {
              name: '缺位分布',
              type: 'pie',
              radius: '60%',
              center: ['50%', '60%'],
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                formatter: '{b},缺{c}人 ({d}%)',
              },
            },
          ],
        };

        if (data.length == 0) {
          option.graphic = {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 16,
              fill: '#999'
            }
          };
        }

        chart.setOption(option);
      }

      // 干部学历图表
      function initTeamEduCharts(id, title, data) {
        const chartsContainer = document.getElementById('edu-charts-container');

        let chartDiv = document.getElementById(id);
        if (!chartDiv) {
          chartDiv = document.createElement('div');
          chartDiv.id = id;
          chartDiv.className = 'chart-item';
          chartsContainer.appendChild(chartDiv);
        }

        let chart = echarts.init(chartDiv);
        chartInstances[id] = chart;

        if (!data) {
          data = [];
        }

        const option = {
          title: {
            text: title,
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}人',
          },
          legend: {
            orient: 'vertical',
            left: 'right',
          },
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          series: [
            {
              name: '数量',
              type: 'pie',
              radius: '60%',
              center: ['50%', '60%'],
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                formatter: '{b}: {c}人',
              },
            },
          ],
        };

        if (data.length == 0) {
          option.graphic = {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 16,
              fill: '#999'
            }
          };
        }

        chart.setOption(option);
      }

      // 初始化占位图表
      function initPlaceholderCharts() {
        // 清空三个容器
        clearChartsContainer('gender-charts-container');
        clearChartsContainer('allocation-charts-container');
        clearChartsContainer('omission-charts-container');
        clearChartsContainer('edu-charts-container');

        // 为每个容器添加2个占位图表
        createPlaceholderChart('gender-charts-container', 'placeholder_gender_1', '中层性别分析');
        createPlaceholderChart('gender-charts-container', 'placeholder_gender_2', '班子性别分析');

        createPlaceholderChart('allocation-charts-container', 'placeholder_allocation_1', '中层配备分析');
        createPlaceholderChart('allocation-charts-container', 'placeholder_allocation_2', '班子配备分析');

        createPlaceholderChart('omission-charts-container', 'placeholder_omission_1', '中层缺位分析');
        createPlaceholderChart('omission-charts-container', 'placeholder_omission_2', '班子缺位分析');

        createPlaceholderChart('edu-charts-container', 'placeholder_edu_1', '中层学历分析-全日制教育');
        createPlaceholderChart('edu-charts-container', 'placeholder_edu_2', '中层学历分析-在职教育');
        createPlaceholderChart('edu-charts-container', 'placeholder_edu_3', '班子学历分析-全日制教育');
        createPlaceholderChart('edu-charts-container', 'placeholder_edu_4', '班子学历分析-在职教育');
      }

      // 清空图表容器
      function clearChartsContainer(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
          // 销毁容器内的所有ECharts实例
          const chartDivs = container.querySelectorAll('.chart-item');
          chartDivs.forEach(function(chartDiv) {
            if (chartInstances[chartDiv.id]) {
              chartInstances[chartDiv.id].dispose();
              delete chartInstances[chartDiv.id];
            }
          });
          // 清空容器内容
          container.innerHTML = '';
        }
      }

      // 创建占位图表
      function createPlaceholderChart(containerId, chartId, title) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 创建图表容器div
        const chartDiv = document.createElement('div');
        chartDiv.id = chartId;
        chartDiv.className = 'chart-item';
        container.appendChild(chartDiv);

        // 初始化ECharts实例
        const chart = echarts.init(chartDiv);
        chartInstances[chartId] = chart;

        // 占位图表配置
        const placeholderOption = {
          title: {
            text: title,
            left: 'left',
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 16,
              fill: '#999'
            }
          },
          series: [{
            type: 'pie',
            radius: '60%',
            center: ['50%', '60%'],
            data: [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          }]
        };

        chart.setOption(placeholderOption);
      }

      // 初始化干部调整情况分析Tab
      function initAdjustmentAnalysisTab() {
        initCompletedAdjustmentCharts();
        initPendingAdjustmentCharts();
      }

      // 已调整情况图表
      function initCompletedAdjustmentCharts() {
        // 已调整饼图
        let pieChart = echarts.init(document.getElementById('completed_adjustment_pie_chart'));
        chartInstances['completed_adjustment_pie_chart'] = pieChart;

        var completedData = [
          { name: '提拔', value: 45, color: '#28a745' },
          { name: '退休', value: 23, color: '#6c757d' },
          { name: '轮换', value: 18, color: '#007bff' },
          { name: '交流', value: 15, color: '#17a2b8' },
          { name: '离职', value: 8, color: '#fd7e14' },
          { name: '免职', value: 5, color: '#dc3545' },
          { name: '其他', value: 3, color: '#6f42c1' },
        ];

        const option = {
          title: {
            text: '已调整类型分布',
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}人 ({d}%)',
          },
          legend: {
            orient: 'vertical',
            left: 'right',
          },
          color: completedData.map(function (item) {
            return item.color;
          }),
          series: [
            {
              name: '已调整',
              type: 'pie',
              radius: '60%',
              center: ['40%', '50%'],
              data: completedData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                formatter: '{b}\n{c}人 ({d}%)',
              },
            },
          ],
        };

        pieChart.setOption(option);

        // 已调整柱状图
        var barChart = echarts.init(document.getElementById('completed_adjustment_bar_chart'));
        chartInstances['completed_adjustment_bar_chart'] = barChart;

        var barOption = {
          title: {
            text: '已调整数量统计',
            left: 'left',
          },
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}人',
          },
          color: ['#28a745'],
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: completedData.map(function (item) {
              return item.name;
            }),
            axisLabel: {
              interval: 0,
              rotate: 30,
            },
          },
          yAxis: {
            type: 'value',
            minInterval: 1,
          },
          series: [
            {
              name: '已调整',
              type: 'bar',
              data: completedData.map(function (item) {
                return item.value;
              }),
              itemStyle: {
                borderRadius: [4, 4, 0, 0],
              },
              label: {
                show: true,
                position: 'top',
              },
            },
          ],
        };

        barChart.setOption(barOption);
      }

      // 需调整情况图表
      function initPendingAdjustmentCharts() {
        // 需调整饼图（虚线样式）
        let pieChart = echarts.init(document.getElementById('pending_adjustment_pie_chart'));
        chartInstances['pending_adjustment_pie_chart'] = pieChart;

        var pendingData = [
          { name: '预计提拔', value: 32, color: '#28a745' },
          { name: '预计退休', value: 28, color: '#6c757d' },
          { name: '预计轮换', value: 20, color: '#007bff' },
          { name: '预计交流', value: 12, color: '#17a2b8' },
          { name: '其他调整', value: 8, color: '#6f42c1' },
        ];

        const option = {
          title: {
            text: '需调整类型分布',
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}人 ({d}%)',
          },
          legend: {
            orient: 'vertical',
            left: 'right',
          },
          color: pendingData.map(function (item) {
            return item.color;
          }),
          series: [
            {
              name: '需调整',
              type: 'pie',
              radius: '60%',
              center: ['40%', '50%'],
              data: pendingData,
              itemStyle: {
                borderType: 'dashed',
                borderWidth: 2,
                opacity: 0.7,
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                formatter: '⏳{b}\n{c}人 ({d}%)',
              },
            },
          ],
        };

        pieChart.setOption(option);

        // 调整流向桑基图
        var flowChart = echarts.init(document.getElementById('adjustment_flow_chart'));
        chartInstances['adjustment_flow_chart'] = flowChart;

        var flowOption = {
          title: {
            text: '人员调整流向分析',
            left: 'left',
          },
          tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove',
          },
          series: [
            {
              type: 'sankey',
              layout: 'none',
              top: '10%',
              bottom: '10%',
              emphasis: {
                focus: 'adjacency',
              },
              data: [
                { name: '现有班子' },
                { name: '现有中层' },
                { name: '现有其他' },
                { name: '提拔后班子' },
                { name: '提拔后中层' },
                { name: '退休人员' },
                { name: '调出人员' },
              ],
              links: [
                { source: '现有中层', target: '提拔后班子', value: 15 },
                { source: '现有其他', target: '提拔后中层', value: 25 },
                { source: '现有班子', target: '退休人员', value: 12 },
                { source: '现有中层', target: '退休人员', value: 8 },
                { source: '现有班子', target: '调出人员', value: 5 },
                { source: '现有中层', target: '调出人员', value: 10 },
              ],
              itemStyle: {
                borderWidth: 1,
                borderColor: '#aaa',
              },
              lineStyle: {
                color: 'source',
                curveness: 0.5,
              },
            },
          ],
        };

        flowChart.setOption(flowOption);
      }

      // 全局resize事件
      $(window).on('resize', function () {
        Object.keys(chartInstances).forEach(function (key) {
          if (chartInstances[key]) {
            chartInstances[key].resize();
          }
        });
      });
    </script>
  </body>
</html>
