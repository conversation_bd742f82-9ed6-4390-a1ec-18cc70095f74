package com.metaorg.common.utils.http;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 第三方接入接口工具类
 * 参考IdpOAuthUtils设计，直接返回JSON字符串，业务端通过JsonUtils解析
 * 
 * 功能：
 * - 单点登录token校验
 * - MD5签名验证
 * - UTF-8编码支持
 * - JSON格式请求响应处理
 */
public final class MbsUtils {

    private static final Logger log = LoggerFactory.getLogger(MbsUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private MbsUtils() { }

    /**
     * 单点登录接口 - token校验
     * 直接返回JSON字符串，业务端通过JsonUtils解析获取需要的字段
     *
     * @param request 校验请求参数
     * @return JSON响应字符串
     * @throws IOException 网络异常
     */
    public static String checkToken(Request request) throws IOException {
        Objects.requireNonNull(request, "request must not be null");
        String url = request.getUrl();

        if (!HttpUtils.validateUriSsrf(url)) {
            throw new IOException("Illegal or unsafe URL: " + safe(url));
        }
        
        // 验证URL安全性
        if (!HttpUtils.validateUriSsrf(url)) {
            throw new IOException("Illegal or unsafe URL: " + url);
        }

        HttpURLConnection conn = null;
        try {
            URL u = new URL(url);
            conn = (HttpURLConnection) u.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setConnectTimeout(request.getConnectTimeoutMs());
            conn.setReadTimeout(request.getReadTimeoutMs());

            // 设置请求头
            conn.setRequestProperty("Content-Type", request.getContentType());
            if (!request.getHeaders().containsKey("Accept")) {
                conn.setRequestProperty("Accept", "application/json; charset=utf-8");
            }
            
            // 设置其他请求头
            for (Map.Entry<String, String> e : request.getHeaders().entrySet()) {
                conn.setRequestProperty(e.getKey(), e.getValue());
            }

            // 构建请求体
            byte[] bodyBytes = buildBody(request);
            conn.setRequestProperty("Content-Length", String.valueOf(bodyBytes.length));

            log.info("POST {} (Content-Length: {})", url, bodyBytes.length);
            log.debug("Request body: {}", maskSensitiveInfo(new String(bodyBytes, StandardCharsets.UTF_8)));

            // 发送请求体
            try (OutputStream os = conn.getOutputStream()) {
                os.write(bodyBytes);
                os.flush();
            }

            // 处理响应
            int responseCode = conn.getResponseCode();
            InputStream is = responseCode >= 200 && responseCode < 300 ? 
                conn.getInputStream() : conn.getErrorStream();
            String responseBody = readAll(is);
            
            log.info("Response (status={}): {}", responseCode, truncate(responseBody));

            if (responseCode < 200 || responseCode >= 300) {
                throw new HttpCallException(responseCode, url, truncate(responseBody), null);
            }

            return responseBody;
            
        } catch (IOException e) {
            throw new HttpCallException(-1, url, e.getMessage(), e);
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    /**
     * 计算MD5签名
     * 签名规则: MD5(appkey + orgCode + token + secretkey)
     * 
     * @param appkey 接入账号
     * @param orgCode 机构编码
     * @param token 用户令牌
     * @param secretkey 密钥
     * @return MD5签名字符串（小写）
     */
    public static String calculateSign(String appkey, String orgCode, String token, String secretkey) {
        Objects.requireNonNull(appkey, "appkey must not be null");
        Objects.requireNonNull(orgCode, "orgCode must not be null");
        Objects.requireNonNull(token, "token must not be null");
        Objects.requireNonNull(secretkey, "secretkey must not be null");
        
        String data = appkey + orgCode + token + secretkey;
        return md5(data);
    }

    /**
     * MD5加密
     * 
     * @param input 输入字符串
     * @return MD5哈希值（32位小写十六进制）
     */
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not available", e);
        }
    }

    /**
     * 构建请求体
     */
    private static byte[] buildBody(Request req) throws IOException {
        if (req.getRawBody() != null) {
            return req.getRawBody().getBytes(StandardCharsets.UTF_8);
        }
        // JSON格式
        return objectMapper.writeValueAsString(req.getJsonParams()).getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 读取输入流内容
     */
    private static String readAll(InputStream is) throws IOException {
        if (is == null) return "";
        try (BufferedReader br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        }
    }

    /**
     * 屏蔽敏感信息用于日志记录
     */
    private static String maskSensitiveInfo(String json) {
        if (json == null) return null;
        // 简单的敏感信息屏蔽，实际项目中可以使用更复杂的正则表达式
        return json.replaceAll("\"token\"\\s*:\\s*\"[^\"]+\"", "\"token\":\"***\"")
                  .replaceAll("\"sign\"\\s*:\\s*\"[^\"]+\"", "\"sign\":\"***\"");
    }

    private static String safe(String s) {
        return StringEscapeUtils.unescapeJava(StringEscapeUtils.escapeJava(s));
    }

    /**
     * 截断长文本用于日志记录
     */
    private static String truncate(String s) {
        if (s == null) return null;
        return s.length() > 512 ? s.substring(0, 512) + "..." : s;
    }

    /**
     * HTTP调用异常
     */
    public static class HttpCallException extends IOException {
        private final int statusCode;
        private final String url;
        private final String responseSnippet;

        public HttpCallException(int statusCode, String url, String responseSnippet, Throwable cause) {
            super("HTTP " + statusCode + " error: " + responseSnippet, cause);
            this.statusCode = statusCode;
            this.url = url;
            this.responseSnippet = responseSnippet;
        }

        public int getStatusCode() { return statusCode; }
        public String getUrl() { return url; }
        public String getResponseSnippet() { return responseSnippet; }
    }

    // === Request模型类 ===
    public static final class Request {
        private final String url;
        private final Map<String, String> headers;
        private final Map<String, Object> jsonParams;
        private final String rawBody;
        private final String contentType;
        private final int connectTimeoutMs;
        private final int readTimeoutMs;

        private Request(Builder b) {
            this.url = b.url;
            this.headers = Collections.unmodifiableMap(new HashMap<String, String>(b.headers));
            this.jsonParams = Collections.unmodifiableMap(new HashMap<String, Object>(b.jsonParams));
            this.rawBody = b.rawBody;
            this.contentType = b.contentType;
            this.connectTimeoutMs = b.connectTimeoutMs;
            this.readTimeoutMs = b.readTimeoutMs;
        }

        public String getUrl() { return url; }
        public Map<String, String> getHeaders() { return headers; }
        public Map<String, Object> getJsonParams() { return jsonParams; }
        public String getRawBody() { return rawBody; }
        public String getContentType() { return contentType; }
        public int getConnectTimeoutMs() { return connectTimeoutMs; }
        public int getReadTimeoutMs() { return readTimeoutMs; }

        public static Builder newBuilder(String url) { return new Builder(url); }

        public static Builder checkToken(String url, String orgCode, String token, String appkey, String[] extendAttr, String sign) {
            return new Builder(url)
                    .contentType("application/json; charset=utf-8")
                    .acceptJsonUtf8()
                    .jsonParam("orgCode", orgCode)
                    .jsonParam("token", token)
                    .jsonParam("appkey", appkey)
                    .jsonParam("extendAttr", extendAttr)
                    .jsonParam("sign", sign);
        }

        public static final class Builder {
            private final String url;
            private Map<String, String> headers = new HashMap<String, String>();
            private Map<String, Object> jsonParams = new HashMap<String, Object>();
            private String rawBody;
            private String contentType = "application/json; charset=utf-8";
            private int connectTimeoutMs = 10_000;
            private int readTimeoutMs = 15_000;

            public Builder(String url) { this.url = Objects.requireNonNull(url, "url must not be null"); }
            public Builder header(String name, String value) { if (name != null && value != null) this.headers.put(name, value); return this; }
            public Builder headers(Map<String,String> headers) { if (headers != null) this.headers.putAll(headers); return this; }
            public Builder jsonParam(String name, Object value) { if (name != null && value != null) this.jsonParams.put(name, value); return this; }
            public Builder jsonParams(Map<String,Object> params) { if (params != null) this.jsonParams.putAll(params); return this; }
            public Builder rawBody(String body) { this.rawBody = body; return this; }
            public Builder contentType(String contentType) { if (contentType != null) this.contentType = contentType; return this; }
            public Builder acceptJsonUtf8() { this.headers.put("Accept", "application/json; charset=utf-8"); return this; }
            public Builder connectTimeoutMs(int ms) { this.connectTimeoutMs = ms; return this; }
            public Builder readTimeoutMs(int ms) { this.readTimeoutMs = ms; return this; }
            public Request build() { return new Request(this); }
        }
    }
} 