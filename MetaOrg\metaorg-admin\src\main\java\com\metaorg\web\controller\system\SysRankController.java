package com.metaorg.web.controller.system;

import com.metaorg.common.annotation.Log;
import com.metaorg.common.constant.UserConstants;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.Ztree;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.system.domain.SysRank;
import com.metaorg.system.service.ISysRankService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 职级信息
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/rank")
public class SysRankController extends BaseController
{
    private String prefix = "system/rank";

    @Autowired
    private ISysRankService rankService;

    @RequiresPermissions("system:rank:view")
    @GetMapping()
    public String rank()
    {
        return prefix + "/rank";
    }

    @RequiresPermissions("system:rank:list")
    @PostMapping("/list")
    @ResponseBody
    public List<SysRank> list(SysRank rank)
    {
        List<SysRank> list = rankService.selectRankList(rank);
        return list;
    }

    /**
     * 新增职级分类（一级职级）
     */
    @GetMapping("/add_first")
    public String add_first(ModelMap mmap)
    {
        mmap.put("maxOrderNum", getMaxOrderNum("0"));

        return prefix + "/add_first";
    }

    /**
     * 新增职级
     */
    @GetMapping("/add/{parentId}")
    public String add(@PathVariable("parentId") String parentId, ModelMap mmap)
    {
        mmap.put("rank", rankService.selectRankById(parentId));
        mmap.put("maxOrderNum", getMaxOrderNum(parentId));

        return prefix + "/add";
    }

    /**
     * 新增保存职级
     */
    @Log(title = "职级管理", businessType = BusinessType.INSERT)
    @RequiresPermissions("system:rank:add")
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysRank rank)
    {
        if (!rankService.checkRankNameUnique(rank))
        {
            return error("新增职级'" + rank.getRankName() + "'失败，职级名称已存在");
        }
        rank.setCreateBy(getLoginName());

        return toAjax(rankService.insertRank(rank));
    }

    /**
     * 修改职级分类（一级职级）
     */
    @RequiresPermissions("system:rank:edit")
    @GetMapping("/edit_first/{rankId}")
    public String edit_first(@PathVariable("rankId") String rankId, ModelMap mmap)
    {
        SysRank rank = rankService.selectRankById(rankId);
        mmap.put("rank", rank);

        return prefix + "/edit_first";
    }

    /**
     * 修改职级
     */
    @RequiresPermissions("system:rank:edit")
    @GetMapping("/edit/{rankId}")
    public String edit(@PathVariable("rankId") String rankId, ModelMap mmap)
    {
        SysRank rank = rankService.selectRankById(rankId);
        if (StringUtils.isNotNull(rank) && "0" == rankId)
        {
            rank.setParentName("无");
        }
        mmap.put("rank", rank);
        return prefix + "/edit";
    }

    /**
     * 修改保存职级
     */
    @Log(title = "职级管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("system:rank:edit")
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysRank rank)
    {
        String rankId = rank.getRankId();
        if (!rankService.checkRankNameUnique(rank))
        {
            return error("修改职级'" + rank.getRankName() + "'失败，职级名称已存在");
        }
        else if (rank.getParentId() == rankId)
        {
            return error("修改职级'" + rank.getRankName() + "'失败，上级职级不能是自己");
        }
        else if (StringUtils.equals(UserConstants.PERSONPOST_DISABLE, rank.getStatus()) && rankService.selectNormalChildrenRankById(rankId) > 0)
        {
            return AjaxResult.error("该职级包含未停用的子职级！");
        }
        rank.setUpdateBy(getLoginName());
        return toAjax(rankService.updateRank(rank));
    }

    /**
     * 删除
     */
    @Log(title = "职级管理", businessType = BusinessType.DELETE)
    @RequiresPermissions("system:rank:remove")
    @GetMapping("/remove/{rankId}")
    @ResponseBody
    public AjaxResult remove(@PathVariable("rankId") String rankId)
    {
        if (rankService.selectRankCount(rankId) > 0)
        {
            return AjaxResult.warn("存在下级职级,不允许删除");
        }
        return toAjax(rankService.deleteRankById(rankId));
    }

    /**
     * 校验职级名称
     */
    @PostMapping("/checkRankNameUnique")
    @ResponseBody
    public boolean checkRankNameUnique(SysRank rank)
    {
        return rankService.checkRankNameUnique(rank);
    }

    /**
     * 选择职级树
     * 
     * @param rankId 职级ID
     * @param excludeId 排除ID
     */
    @GetMapping(value = { "/selectRankTree/{rankId}", "/selectRankTree/{rankId}/{excludeId}" })
    public String selectDeptTree(@PathVariable("rankId") String rankId,
            @PathVariable(value = "excludeId", required = false) String excludeId, ModelMap mmap)
    {
        SysRank rank = rankService.selectRankById(rankId);
        mmap.put("rank", rank);
        mmap.put("excludeId", excludeId);
        return prefix + "/tree";
    }

    /**
     * 加载职级列表树（排除下级）
     */
    @GetMapping("/treeData/{excludeId}")
    @ResponseBody
    public List<Ztree> treeDataExcludeChild(@PathVariable(value = "excludeId", required = false) String excludeId)
    {
        SysRank rank = new SysRank();
        rank.setExcludeId(excludeId);
        List<Ztree> ztrees = rankService.selectRankTreeExcludeChild(rank);
        return ztrees;
    }

    private long getMaxOrderNum(String parentId)
    {
        SysRank rank = new SysRank();
        rank.setParentId(parentId);
        List<SysRank> rankList = rankService.selectRankList(rank);
        // 使用 Stream 排序
        rankList = rankList.stream().sorted(Comparator.comparing(SysRank::getOrderNum).reversed())
                .collect(Collectors.toList());
        if(!rankList.isEmpty()) {
            return rankList.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
