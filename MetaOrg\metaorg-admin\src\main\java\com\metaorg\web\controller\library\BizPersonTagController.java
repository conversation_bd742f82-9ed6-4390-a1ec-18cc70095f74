package com.metaorg.web.controller.library;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonTag;
import com.metaorg.library.service.IBizPersonTagService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 干部标签Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/tag")
public class BizPersonTagController extends BaseController
{
    private String prefix = "library/tag";

    @Autowired
    private IBizPersonTagService bizPersonTagService;

    @RequiresPermissions("library:tag:view")
    @GetMapping()
    public String tag()
    {
        return prefix + "/tag";
    }

    /**
     * 查询干部标签列表
     */
    @RequiresPermissions("library:tag:list")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonTag bizPersonTag)
    {
        startPage();
        List<BizPersonTag> list = bizPersonTagService.selectBizPersonTagList(bizPersonTag);
        return getDataTable(list);
    }

    /**
     * 导出干部标签列表
     */
    @RequiresPermissions("library:tag:export")
    @Log(title = "干部标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonTag bizPersonTag)
    {
        List<BizPersonTag> list = bizPersonTagService.selectBizPersonTagList(bizPersonTag);
        ExcelUtil<BizPersonTag> util = new ExcelUtil<BizPersonTag>(BizPersonTag.class);
        return util.exportExcel(list, "干部标签数据");
    }

    /**
     * 新增干部标签
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        return prefix + "/add";
    }

    /**
     * 新增保存干部标签
     */
    @RequiresPermissions("library:tag:add")
    @Log(title = "干部标签", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "干部标签", tableName = PersonTable.BIZ_PERSON_TAG, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonTag bizPersonTag)
    {
        return toAjax(bizPersonTagService.insertBizPersonTag(bizPersonTag));
    }

    /**
     * 修改干部标签
     */
    @RequiresPermissions("library:tag:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonTag bizPersonTag = bizPersonTagService.selectBizPersonTagById(id);
        mmap.put("bizPersonTag", bizPersonTag);
        return prefix + "/edit";
    }

    /**
     * 修改保存干部标签
     */
    @RequiresPermissions("library:tag:edit")
    @Log(title = "干部标签", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "干部标签", tableName = PersonTable.BIZ_PERSON_TAG, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonTag bizPersonTag)
    {
        return toAjax(bizPersonTagService.updateBizPersonTag(bizPersonTag));
    }

    /**
     * 删除干部标签
     */
    @RequiresPermissions("library:tag:remove")
    @Log(title = "干部标签", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "干部标签", tableName = PersonTable.BIZ_PERSON_TAG, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonTagService.deleteBizPersonTagByIds(ids));
    }
}
