# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 添加src目录到路径
src_path = Path('src').absolute()
sys.path.insert(0, str(src_path))

block_cipher = None

# 隐藏导入
hiddenimports = [
    'fake_http_header.data',
    'playwright_stealth',
    'playwright_stealth.stealth',
    'playwright_stealth.core',
    'playwright_stealth.properties',
    'crawl4ai',
    'crawl4ai.async_webcrawler',
    'pika',
    'psycopg2',
    'openai',
    'pandas',
    'openpyxl',
    'loguru',
    'pydantic',
    'nanoid',
]

# 数据文件收集
datas = [
    ('.env.example', '.'),
]

# 二进制文件收集
binaries = []

a = Analysis(
    ['src\\nfra_crawler\\main.py'],
    pathex=[str(src_path)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
# 收集所有数据文件
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集fake_http_header的数据文件
try:
    fake_http_header_datas = collect_data_files('fake_http_header')
    a.datas += fake_http_header_datas
    print(f"收集到fake_http_header数据文件: {len(fake_http_header_datas)}")
except Exception as e:
    print(f"收集fake_http_header数据文件失败: {e}")

# 收集playwright_stealth的数据文件
try:
    playwright_stealth_datas = collect_data_files('playwright_stealth')
    a.datas += playwright_stealth_datas
    print(f"收集到playwright_stealth数据文件: {len(playwright_stealth_datas)}")
except Exception as e:
    print(f"收集playwright_stealth数据文件失败: {e}")

# 收集crawl4ai的数据文件
try:
    crawl4ai_datas = collect_data_files('crawl4ai')
    a.datas += crawl4ai_datas
    print(f"收集到crawl4ai数据文件: {len(crawl4ai_datas)}")
except Exception as e:
    print(f"收集crawl4ai数据文件失败: {e}")

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='nfra_crawler',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
