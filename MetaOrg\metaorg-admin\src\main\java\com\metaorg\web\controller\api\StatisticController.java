package com.metaorg.web.controller.api;

import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.BaseEntity;
import com.metaorg.common.core.domain.ResponseBodyApi;
import com.metaorg.common.core.text.Convert;
import com.metaorg.library.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/api/statistic")
public class StatisticController extends BaseController {

    @Autowired
    private IBizPersonReportService personReportService;

    /**
     * 获取人员结构的统计分析数据
     */
    @PostMapping("/personnel/structure")
    @ResponseBody
    public ResponseBodyApi<Map<String, Object>> selectPersonnelStructureStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }
        Map<String, Object> analysisData = personReportService.selectPersonnelStructureStatistic(baseEntity);

        return new ResponseBodyApi<>(analysisData);
    }

    /**
     * 获取组织层面的统计分析数据
     */
    @PostMapping("/orgs/levels")
    @ResponseBody
    public ResponseBodyApi<Map<String, Object>> selectOrgLevelStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }
        Map<String, Object> analysisData = personReportService.selectOrgLevelStatistic(baseEntity);

        return new ResponseBodyApi<>(analysisData);
    }

    /**
     * 获取岗位条线分析数据
     */
    @PostMapping("/orgs/levels/post_line")
    @ResponseBody
    public ResponseBodyApi<Map<String, Object>> selectPostLineStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }

        if (paramMap.get("postIds") != null && paramMap.get(("postIds")) != "") {
            baseEntity.getParams().put("postIds", Convert.toStrArray(paramMap.get("postIds").toString()));
        }

        if (paramMap.get("year") != null && paramMap.get(("year")) != "") {
            baseEntity.getParams().put("year", Convert.toInt(paramMap.get("year").toString()));
        }

        Map<String, Object> analysisData = personReportService.selectOrgLevelPostLineStatistic(baseEntity);
        return new ResponseBodyApi<>(analysisData);
    }

    /**
     * 获取干部已调整情况分析
     */
    @PostMapping("/position/adjusts/adjusted")
    @ResponseBody
    public ResponseBodyApi<List<Map<String, Object>>> selectPositionAdjustedStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }

        if (paramMap.get("adjustTypes") != null && paramMap.get(("adjustTypes")) != "") {
            baseEntity.getParams().put("adjustTypeList", Convert.toStrArray(paramMap.get("adjustTypes").toString()));
        }

        if (paramMap.get("year") != null && paramMap.get(("year")) != "") {
            baseEntity.getParams().put("year", Convert.toInt(paramMap.get("year").toString()));
        }

        List<Map<String, Object>> result = personReportService.selectPositionAdjustedStatistic(baseEntity);
        return new ResponseBodyApi<>(result);
    }

    /**
     * 获取干部拟调整情况分析
     */
    @PostMapping("/position/adjusts/proposed")
    @ResponseBody
    public ResponseBodyApi<List<Map<String, Object>>> selectPositionProposedStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }

        if (paramMap.get("adjustTypes") != null && paramMap.get(("adjustTypes")) != "") {
            baseEntity.getParams().put("adjustTypeList", Convert.toStrArray(paramMap.get("adjustTypes").toString()));
        }

        if (paramMap.get("year") != null && paramMap.get(("year")) != "") {
            baseEntity.getParams().put("year", Convert.toInt(paramMap.get("year").toString()));
        }

        List<Map<String, Object>> result = personReportService.selectPositionProposedStatistic(baseEntity);
        return new ResponseBodyApi<>(result);
    }
}