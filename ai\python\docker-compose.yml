version: '3.8'

services:
  # 爬虫应用
  nfra-crawler:
    build: .
    container_name: nfra-crawler
    restart: unless-stopped
    environment:
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=metaorg
      - DATABASE_USER=postgres
      - DATABASE_PWD=password
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=admin
      - RABBITMQ_PWD=admin123
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - postgres
      - rabbitmq
    networks:
      - nfra-network

  # PostgreSQL数据库
  postgres:
    image: postgres:13
    container_name: nfra-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=metaorg
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - nfra-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management
    container_name: nfra-rabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"  # 管理界面
    networks:
      - nfra-network
volumes:
  postgres_data:
  rabbitmq_data:

networks:
  nfra-network:
    driver: bridge
