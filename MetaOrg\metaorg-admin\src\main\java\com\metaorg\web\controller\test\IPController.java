package com.metaorg.web.controller.test;

import com.metaorg.common.core.domain.ResponseBodyApi;
import com.metaorg.common.utils.IpUtils;
import com.metaorg.common.utils.ShiroUtils;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/test")
public class IPController {

    @Autowired
    private Ip2regionSearcher ip2regionSearcher;

    @GetMapping("/ip")
    @ResponseBody
    public ResponseBodyApi ip()
    {
        String ip = ShiroUtils.getIp();
        String address = ip2regionSearcher.getAddress(ip);
//        String address = ip2regionSearcher.getAddress("**************");
        HashMap<String, String > data = new HashMap<String, String>(){{
            put("ip", ip);
            put("address", address);
        }};
        return new ResponseBodyApi(data);
//        return AjaxResult.success("list success");
    }

    @GetMapping("/ip2")
    @ResponseBody
    public ResponseBodyApi ip2(HttpServletRequest request)
    {
        String ip = IpUtils.getIpAddr(request);// request.getRemoteHost();
        String address = ip2regionSearcher.getAddress(ip);
//        String address = ip2regionSearcher.getAddress("**************");
        HashMap<String, String > data = new HashMap<String, String>(){{
            put("ip", ip);
            put("address", address);
        }};
        return new ResponseBodyApi(data);
//        return AjaxResult.success("list success");
    }


}


