package com.metaorg.web.service;

import com.alibaba.fastjson.JSONObject;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.http.HttpClientUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.metaorg.framework.utils.LogOperUtils;
import com.metaorg.system.service.ISysConfigService;
import com.metaorg.common.utils.http.JsonUtils;
import com.metaorg.system.service.ISysUserService;

import java.util.HashMap;
import java.util.Map;

/**
 * OAuth2服务类
 * 处理与OAuth2认证服务器的交互
 * 
 * <AUTHOR>
 */
@Service
public class OAuth2Service {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ISysUserService userService;
    
    public OAuth2Service() {
        
    }

    public String getTokenByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("授权码不能为空");
        }

        String idpOauthSettings = sysConfigService.selectConfigByKey("idp.oauth.settings");
        if (StringUtils.isEmpty(idpOauthSettings)) {
            idpOauthSettings = "";
        }
        String idpBaseUrl = StringUtils.getStrBySepIndex(idpOauthSettings, ",", 0);
        String idpOauthClientId = StringUtils.getStrBySepIndex(idpOauthSettings, ",", 1);
        String idpOauthClientSecret = StringUtils.getStrBySepIndex(idpOauthSettings, ",", 2);

        Map<String, String> params = new HashMap<String, String>();
        params.put("grant_type", "authorization_code");
        params.put("client_id", idpOauthClientId);
        params.put("client_secret", idpOauthClientSecret);
        params.put("code", code);
        String baseUrl = idpBaseUrl + "/idp/oauth2/getToken";
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append(baseUrl.contains("?") ? "&" : "?");
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!first) urlBuilder.append("&");
            urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }
        String paramsJson = JSONObject.toJSONString(HttpClientUtils.maskSensitiveStringParams(params));

        String logTitle = "统一平台获取票据token";
        String callMethod = "com.metaorg.web.service.OAuth2Service.getTokenByCode()";
        long startTime = System.currentTimeMillis();
        try {
            Map<String, String> headers = new HashMap<>();
            String resp = HttpClientUtils.simplePost(urlBuilder.toString(), headers);
            long costMs = System.currentTimeMillis() - startTime;
            String maskSensitiveRes = "{}";
            try{
                maskSensitiveRes = JSONObject.toJSONString(HttpClientUtils.maskSensitiveObjectParams(HttpClientUtils.parseJsonResponse(resp)));
            }catch(Exception ex){}
            LogOperUtils.logOperation(null, baseUrl, true, logTitle, callMethod, "POST", maskSensitiveRes, null, costMs, paramsJson);
            String accessToken = JsonUtils.getString(resp, "access_token", "");
            return accessToken;
        } catch (Exception ex) {
            long costMs = System.currentTimeMillis() - startTime;
            String err = StringUtils.substring(ex.getMessage(), 0, 2000);
            LogOperUtils.logOperation(null, baseUrl, true, logTitle, callMethod, "POST", null, err, costMs, paramsJson);
            return "";
        }
    }

    public String getUserInfo(String accessToken) {
        if (accessToken == null || accessToken.trim().isEmpty()) {
            throw new IllegalArgumentException("访问令牌不能为空");
        }

        String idpOauthSettings = sysConfigService.selectConfigByKey("idp.oauth.settings");
        if (StringUtils.isEmpty(idpOauthSettings)) {
            idpOauthSettings = "";
        }
        String idpBaseUrl = StringUtils.getStrBySepIndex(idpOauthSettings, ",", 0);
        String idpOauthClientId = StringUtils.getStrBySepIndex(idpOauthSettings, ",", 1);
        
        Map<String, String> params = new HashMap<String, String>();
        params.put("access_token", accessToken);
        params.put("client_id", idpOauthClientId);
        String baseUrl = idpBaseUrl + "/idp/oauth2/getUserInfo";

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append(baseUrl.contains("?") ? "&" : "?");
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!first) urlBuilder.append("&");
            urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }
        String paramsJson = JSONObject.toJSONString(HttpClientUtils.maskSensitiveStringParams(params));
        
        String logTitle = "统一平台获取登录用户";
        String callMethod = "com.metaorg.web.service.OAuth2Service.getUserInfo()";
        long startTime = System.currentTimeMillis();
        try {
            Map<String, String> headers = new HashMap<>();
            String resp = HttpClientUtils.simpleGet(urlBuilder.toString(), headers);
            String loginName = JsonUtils.getString(resp, "loginName", "");
            long costMs = System.currentTimeMillis() - startTime;
            SysUser user = null;
            if(StringUtils.isNotEmpty(loginName)){
                if(!StringUtils.isEmpty(loginName)){
                    user = userService.selectUserByUnionLoginName(loginName);
                    if(user == null){
                        user = userService.selectUserByLoginName(loginName);
                    }
                }
            }
            LogOperUtils.logOperation(user, baseUrl, true, logTitle, callMethod, "POST", resp, null, costMs, paramsJson);
            return loginName;
        } catch (Exception ex) {
            long costMs = System.currentTimeMillis() - startTime;
            String err = StringUtils.substring(ex.getMessage(), 0, 2000);
            LogOperUtils.logOperation(null, baseUrl, true, logTitle, callMethod, "POST", null, err, costMs, paramsJson);
            return "";
        }
    }
}
