package com.metaorg.library.domain;

import com.metaorg.common.annotation.Excel;
import com.metaorg.common.core.domain.BaseEntity;
import javax.validation.constraints.*;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Objects;

/**
 * 同步处罚详情对象
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@NoArgsConstructor
public class BizSyncPunishDetailTplVM extends BaseEntity
{
    /** 处罚公示项ID */
    @NotBlank
    @Excel(name = "处罚公示标识")
    private String punishItemId;

    /** 请求ID */
    @NotBlank
    @Excel(name = "同步请求标识")
    private String requestId;

    /** 排序号 */
    @Excel(name = "序号")
    private Integer orderNum;

    /** 当事人姓名 */
    @Excel(name = "当事人名称")
    private String partyName;

    /** 当事人时任职务 */
    @Excel(name = "当事人时任职务")
    private String partyPosition;

    /** 违法违规事实 */
    @Excel(name = "主要违法违规行为")
    private String violation;

    /** 处罚依据 */
    @Excel(name = "处罚依据")
    private String punishBasis;

    /** 处罚内容 */
    @Excel(name = "行政处罚内容")
    private String punishContent;

    /** 批准机关 */
    @Excel(name = "作出决定机关")
    private String decisionAuthority;

    /** 行政处罚决定书文号 */
    @Excel(name = "行政处罚决定书文号")
    private String punishDocNo;

    /** 作出处罚决定的日期 */
    @Excel(name = "作出处罚决定的日期")
    private String punishDate;

    /** 当事人类型 */
    @Excel(name = "当事人类型")
    private String partyType;

    /** 处罚详情JSON内容 */
    @Excel(name = "行政处罚内容JSON内容")
    private String punishJsonContent;

    /** 来源ID */
    @Excel(name = "来源标识")
    private String sourceId;

    /** 来源URL */
    @Excel(name = "来源网址")
    private String sourceUrl;

    /** 错误信息 */
    private String errMsg;

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof BizSyncPunishDetailTplVM)) {
            return false;
        }

        BizSyncPunishDetailTplVM other = (BizSyncPunishDetailTplVM) o;
        return (
            Objects.equals(partyName, other.partyName) &&
            Objects.equals(punishDate, other.punishDate) &&    
            Objects.equals(sourceId, other.sourceId)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(partyName, punishDate, sourceId);
    }
} 