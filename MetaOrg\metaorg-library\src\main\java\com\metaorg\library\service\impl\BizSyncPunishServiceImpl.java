package com.metaorg.library.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Date;
import java.util.HashMap;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.metaorg.library.mapper.BizSyncPunishMapper;
import com.metaorg.library.domain.BizSyncPunishItem;
import com.metaorg.library.domain.BizSyncPunishDetail;
import com.metaorg.library.service.IBizSyncPunishService;
import com.metaorg.common.utils.uuid.IdUtils;
import com.metaorg.common.utils.DictUtils;
import org.apache.commons.lang3.StringUtils;
import com.metaorg.library.service.RabbitService;
import com.metaorg.common.core.text.Convert;
import com.metaorg.library.config.RabbitConfig;

/**
 * 同步处罚公示Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Service
public class BizSyncPunishServiceImpl implements IBizSyncPunishService
{
    @Autowired
    private BizSyncPunishMapper bizSyncPunishMapper;

    @Autowired
    private RabbitService rabbitService;

    /**
     * 查询处罚公示项
     *
     * @param id 处罚公示项主键
     * @return 处罚公示项
     */
    @Override
    public BizSyncPunishItem selectBizSyncPunishItemById(String id)
    {
        return bizSyncPunishMapper.selectBizSyncPunishItemById(id);
    }

    /**
     * 查询处罚公示项列表
     *
     * @param bizSyncPunishItem 处罚公示项
     * @return 处罚公示项
     */
    @Override
    public List<BizSyncPunishItem> selectBizSyncPunishItemList(BizSyncPunishItem bizSyncPunishItem)
    {
        return bizSyncPunishMapper.selectBizSyncPunishItemList(bizSyncPunishItem);
    }

    /**
     * 查询处罚详情
     *
     * @param id 处罚详情主键
     * @return 处罚详情
     */
    @Override
    public BizSyncPunishDetail selectBizSyncPunishDetailById(String id)
    {
        return bizSyncPunishMapper.selectBizSyncPunishDetailById(id);
    }

    /**
     * 查询处罚详情列表
     *
     * @param bizSyncPunishDetail 处罚详情
     * @return 处罚详情
     */
    @Override
    public List<BizSyncPunishDetail> selectBizSyncPunishDetailList(BizSyncPunishDetail bizSyncPunishDetail)
    {
        return bizSyncPunishMapper.selectBizSyncPunishDetailList(bizSyncPunishDetail);
    }

    /**
     * 发起同步处罚详情请求
     *
     * @param params 同步参数
     * @return 同步结果
     */
    @Override
    public Boolean syncPunishDetailsRequest(Map<String, Object> params)
    {
        if (params == null || params.isEmpty()) {
            params = new HashMap<>();
        }

        if (!params.containsKey("request_id")) {
            // 生成唯一请求ID
            String requestId = IdUtils.fastSimpleUUID();
            params.put("request_id", requestId);
        }

        if (!params.containsKey("create_time")) {
            // 获取当前时间戳
            long createTime = System.currentTimeMillis();
            params.put("create_time", createTime);
        }

        // 从字典中获取处罚公示的URL
        String dictTypeCode = "biz_sync_punish_nfra";
        String url = DictUtils.getDictValue(dictTypeCode, "url");
        if (StringUtils.isEmpty(url)) {
            url = "https://www.nfra.gov.cn/branch/xinjiang/view/pages/common/ItemList.html?itemPId=2120&itemId=2124&itemUrl=ItemListRightList.html&itemName=%E8%A1%8C%E6%94%BF%E5%A4%84%E7%BD%9A#1";
        }
        params.put("url", url);

        if (!params.containsKey("page_count")) {
            String pageCount = DictUtils.getDictValue(dictTypeCode, "page_count");
            if (StringUtils.isEmpty(pageCount)) {
                params.put("page_count", 0);
            } else {
                params.put("page_count", Convert.toInt(pageCount, 0));
            }
        }

        if (!params.containsKey("begin_date")) {
            String beginDate = DictUtils.getDictValue(dictTypeCode, "begin_date");
            if (StringUtils.isEmpty(beginDate)) {
                beginDate = "2024-01-01";
            }
            params.put("begin_date", beginDate);
        }

        if (!params.containsKey("end_date")) {
            // 获取当前日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String endDate = sdf.format(new Date());
            params.put("end_date", endDate);
        }
        
        // 发送消息到RabbitMQ队列
        rabbitService.sendMessage("", RabbitConfig.SYNC_PUNISH_CRAWLER_REQUEST_QUEUE, params);
        
        return true;
    }

    @Override
    public void batchInsertBizSyncPunishItem(List<BizSyncPunishItem> bizSyncPunishItemList)
    {
        bizSyncPunishMapper.batchInsertBizSyncPunishItem(bizSyncPunishItemList);
    }

    @Override
    public void batchInsertBizSyncPunishDetail(List<BizSyncPunishDetail> bizSyncPunishDetailList)
    {
        bizSyncPunishMapper.batchInsertBizSyncPunishDetail(bizSyncPunishDetailList);
    }

    @Override
    public void updateBizSyncPunishDetailStatus(BizSyncPunishDetail bizSyncPunishDetail)
    {
        bizSyncPunishMapper.updateBizSyncPunishDetailStatus(bizSyncPunishDetail);
    }

    @Override
    public void deleteBizSyncPunishItem(BizSyncPunishItem bizSyncPunishItem)
    {
        bizSyncPunishMapper.deleteBizSyncPunishItem(bizSyncPunishItem);
    }

    @Override
    public void deleteBizSyncPunishDetail(BizSyncPunishDetail bizSyncPunishDetail)
    {
        bizSyncPunishMapper.deleteBizSyncPunishDetail(bizSyncPunishDetail);
    }
} 