#!/usr/bin/env python3
"""
PyInstaller打包脚本
解决fake_http_header.data等依赖问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  ✅ 删除 {dir_name}")

def install_dependencies():
    """安装打包依赖"""
    print("📦 检查打包依赖...")
    try:
        import PyInstaller
        print("  ✅ PyInstaller 已安装")
    except ImportError:
        print("  📥 安装 PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)

def build_with_spec():
    """使用.spec文件构建"""
    print("🔨 使用.spec文件构建...")
    
    # 确保.spec文件存在
    spec_file = "main.spec"
    if not os.path.exists(spec_file):
        print(f"❌ 找不到 {spec_file} 文件")
        return False
    
    try:
        # 运行PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", spec_file, "--clean"]
        print(f"  🚀 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("  ✅ 构建成功!")
            return True
        else:
            print("  ❌ 构建失败:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"  ❌ 构建异常: {e}")
        return False

def build_with_command():
    """使用命令行参数构建（备用方案）"""
    print("🔨 使用命令行参数构建...")
    
    cmd = [
        sys.executable, "-m", "PyInstaller", "-F",
        "--hidden-import=fake_http_header.data",
        "--hidden-import=playwright_stealth",
        "--hidden-import=playwright_stealth.stealth", 
        "--hidden-import=playwright_stealth.core",
        "--hidden-import=playwright_stealth.properties",
        "--collect-data=fake_http_header",
        "--collect-data=playwright_stealth",
        "--collect-data=crawl4ai",
        "--add-data=.env.example;.",
        "--name=nfra_crawler",
        "--clean",
        "src/nfra_crawler/main.py"
    ]
    
    try:
        print(f"  🚀 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("  ✅ 构建成功!")
            return True
        else:
            print("  ❌ 构建失败:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"  ❌ 构建异常: {e}")
        return False

def test_exe():
    """测试生成的exe文件"""
    print("🧪 测试生成的exe文件...")
    
    exe_path = Path("dist/nfra_crawler.exe")
    if not exe_path.exists():
        exe_path = Path("dist/main.exe")
    
    if not exe_path.exists():
        print("  ❌ 找不到生成的exe文件")
        return False
    
    try:
        # 测试帮助信息
        result = subprocess.run([str(exe_path), "--help"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("  ✅ exe文件可以正常运行")
            print(f"  📁 exe文件位置: {exe_path.absolute()}")
            return True
        else:
            print("  ❌ exe文件运行失败:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("  ⚠️ exe文件运行超时")
        return False
    except Exception as e:
        print(f"  ❌ 测试exe文件异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 NFRA爬虫项目打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("src/nfra_crawler/main.py"):
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    # 步骤1: 清理构建目录
    clean_build()
    
    # 步骤2: 安装依赖
    install_dependencies()
    
    # 步骤3: 构建
    success = False
    
    # 优先使用.spec文件
    if os.path.exists("main.spec"):
        success = build_with_spec()
    
    # 如果.spec文件构建失败，尝试命令行方式
    if not success:
        print("\n🔄 .spec文件构建失败，尝试命令行方式...")
        success = build_with_command()
    
    if not success:
        print("\n❌ 构建失败，请检查错误信息")
        return 1
    
    # 步骤4: 测试exe文件
    if test_exe():
        print("\n🎉 打包完成！")
        print("\n📋 使用方法:")
        print("  ./dist/nfra_crawler.exe --export data.xlsx")
        print("  ./dist/nfra_crawler.exe --help")
        return 0
    else:
        print("\n⚠️ 打包完成但测试失败，请手动测试exe文件")
        return 1

if __name__ == "__main__":
    sys.exit(main())
