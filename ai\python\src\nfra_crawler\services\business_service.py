"""
主业务流程服务
"""

import asyncio
from typing import Optional
from datetime import datetime, date, timedelta
from nanoid import generate
from nfra_crawler.services.excel_service import excel_service
from nfra_crawler.utils.logger import logger

from nfra_crawler.models.database import BizSyncPunishItem, BizSyncPunishDetail, CrawlRequest, BizSyncRecord
from nfra_crawler.services.crawler_service import crawler_service
from nfra_crawler.services.ai_service import ai_service
from nfra_crawler.services.data_service import data_service
from nfra_crawler.config.settings import settings

class BusinessService:
    """主业务流程服务类"""

    def __init__(self):
        logger.info("业务服务初始化完成")
    
    async def process_crawl_request(self, crawl_request: CrawlRequest, sync_record: BizSyncRecord):
        """处理爬虫请求的主业务流程"""
        logger.info(f"开始处理爬虫请求，任务ID: {crawl_request.request_id}")
        
        try:
            # 更新同步记录状态为处理中
            await data_service.update_sync_record_progress(
                sync_record, "processing", "处理中"
            )

            # 第一步：爬取处罚公示列表
            logger.info("步骤1: 爬取处罚公示列表")
            punish_items = await crawler_service.crawl_punish_list(
                crawl_request, sync_record.id
            )
            
            if not punish_items:
                error_msg = "未获取到任何处罚公示项"
                logger.error(error_msg)
                await data_service.update_sync_record_progress(
                    sync_record, "failed", error_msg
                )
                return
            
            logger.info(f"获取到 {len(punish_items)} 条处罚公示项")
            
            # 第二步：处理和保存处罚公示项（去重、批量插入）
            logger.info("步骤2: 处理和保存处罚公示项")
            saved_items = await data_service.process_punish_items(punish_items)
            
            if not saved_items:
                logger.info("所有处罚公示项都已存在，无需重复处理")
                await data_service.update_sync_record_progress(
                    sync_record, "succeeded", "所有数据已存在，无需重复处理"
                )
                return
            
            logger.info(f"成功保存 {len(saved_items)} 条新的处罚公示项")
            
            # 第三步：爬取每个处罚项的详情页面并进行AI清洗
            logger.info("步骤3: 爬取详情页面并进行AI清洗")
            total_details_processed = 0
            
            for i, item in enumerate(saved_items):
                try:
                    logger.info(f"处理第 {i+1}/{len(saved_items)} 个处罚项: {item.title}")
                    
                    # 爬取详情页面HTML
                    detail_html = await crawler_service.crawl_punish_detail(item)
                    
                    if not detail_html:
                        logger.warning(f"未获取到详情页面内容: {item.title}")
                        continue
                    
                    # AI清洗HTML表格数据
                    detail_data_list = await ai_service.clean_html_table(detail_html, item.source_url)
                    
                    if not detail_data_list:
                        logger.warning(f"AI清洗未获取到有效数据: {item.title}")
                        continue
                    
                    # 保存处罚详情数据
                    saved_details = await data_service.process_punish_details(
                        item, detail_data_list
                    )
                    
                    total_details_processed += len(saved_details)
                    logger.info(f"处罚项 {item.title} 处理完成，获得 {len(saved_details)} 条详情记录")
                    
                    # 添加延迟，避免请求过于频繁
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"处理处罚项时发生错误: {e}, 项目: {item.title}")
                    continue
            
            # 第四步：更新同步记录状态为成功
            success_msg = f"处理完成，共处理 {len(saved_items)} 个处罚项，{total_details_processed} 条详情记录"
            logger.info(success_msg)
            
            await data_service.update_sync_record_progress(
                sync_record, "succeeded", success_msg
            )
            
            # 输出处理统计信息
            stats = data_service.get_processing_statistics(crawl_request.request_id)
            logger.info(f"处理统计: {stats}")
            
        except Exception as e:
            error_msg = f"处理爬虫请求时发生未知错误: {e}"
            logger.error(error_msg)
            await data_service.update_sync_record_progress(
                sync_record, "failed", error_msg
            )
    
    async def health_check(self) -> dict:
        """健康检查"""
        try:
            # 检查数据库连接
            from nfra_crawler.utils.database import db_manager
            test_query = "SELECT 1"
            db_manager.execute_query(test_query)
            
            # 检查AI服务
            ai_available = bool(ai_service.client.api_key)
            
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "database": "connected",
                "ai_service": "available" if ai_available else "unavailable",
                "crawler": "ready"
            }
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    async def get_task_status(self, request_id: str) -> Optional[dict]:
        """获取任务状态"""
        try:
            stats = data_service.get_processing_statistics(request_id)
            if "error" in stats:
                return None
            
            sync_record = stats["sync_record"]
            
            return {
                "request_id": request_id,
                "status": sync_record["status"],
                "request_time": sync_record["request_time"],
                "completed_time": sync_record["completed_time"],
                "elapsed": sync_record["elapsed"],
                "remark": sync_record["remark"],
                "punish_item_count": stats["punish_item_count"],
                "punish_detail_count": stats["punish_detail_count"],
                "party_type_distribution": stats["party_type_distribution"]
            }
            
        except Exception as e:
            logger.error(f"获取任务状态时发生错误: {e}")
            return None

    async def export_to_excel(self, file_path: str):
        """导出数据到Excel文件"""
        try:
            logger.info(f"开始导出数据到Excel文件: {file_path}")
            start_time = datetime.now()
            
            # 创建爬虫请求
            current_timestamp = int(datetime.now().timestamp() * 1000)  # 毫秒时间戳
            crawl_request = CrawlRequest(
                create_time=current_timestamp,
                request_id=generate(),
                url=settings.crawler.target_url,
                page_count=settings.crawler.page_count,  # 限制页数避免导出时间过长
                begin_date=date.today() - timedelta(days=35),
                end_date=date.today().isoformat()
            )
            
            sync_record_id = generate()
            
            # 爬取处罚公示列表
            logger.info("步骤1: 爬取处罚公示列表")
            punish_items = await crawler_service.crawl_punish_list(
                crawl_request, sync_record_id
            )
            
            if not punish_items:
                error_msg = "未获取到任何处罚公示项"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
            
            logger.info(f"获取到 {len(punish_items)} 条处罚公示项")
            
            # 爬取详情并进行AI清洗
            logger.info("步骤2: 爬取详情页面并进行AI清洗")
            all_details = []
            
            for i, punish_item in enumerate(punish_items):
                try:
                    logger.info(f"处理第 {i+1}/{len(punish_items)} 个处罚项: {punish_item.title}")
                    
                    # 爬取详情页面HTML
                    detail_html = await crawler_service.crawl_punish_detail(punish_item)
                    
                    if not detail_html:
                        logger.warning(f"未获取到详情页面内容: {punish_item.title}")
                        continue
                    
                    # AI清洗HTML表格数据
                    detail_data_list = await ai_service.clean_html_table(detail_html, punish_item.source_url)
                 
                    if not detail_data_list:
                        logger.warning(f"AI清洗未获取到有效数据: {punish_item.title}")
                        continue
                    
                    for detail_data in detail_data_list:
                        punish_detail = BizSyncPunishDetail(
                            punish_item_id=punish_item.id,
                            request_id=punish_item.request_id,
                            order_num=int(detail_data.serial_no) if detail_data.serial_no.isdigit() else i + 1,
                            party_name=detail_data.party_name,
                            party_position=detail_data.party_position,
                            punish_doc_no=detail_data.punish_doc_no,
                            violation=detail_data.violation,
                            punish_basis=detail_data.punish_basis,
                            punish_content=detail_data.punish_content,
                            punish_date=detail_data.punish_date,
                            decision_authority=detail_data.decision_authority,
                            party_type=detail_data.party_type,
                            punish_json_content=detail_data.model_dump_json(),
                            source_id=detail_data.source_id,
                            source_url=detail_data.source_url
                        )
                        all_details.append(punish_detail)
                    
                    # 添加延迟，避免请求过于频繁
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"处理处罚项时发生错误: {e}, 项目: {punish_item.title}")
                    continue
            
            # 导出到Excel
            logger.info("步骤3: 导出数据到Excel文件")
            abs_file_path = excel_service.export_punish_data(punish_items, all_details, file_path)
            
            # 计算处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            # 输出成功日志
            logger.info("=" * 60)
            logger.info("数据导出完成！")
            logger.info(f"导出记录数量: 处罚公示项 {len(punish_items)} 条，处罚明细 {len(all_details)} 条")
            logger.info(f"导出文件路径: {abs_file_path}")
            logger.info(f"导出完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"总处理时间: {processing_time:.2f} 秒")
            logger.info("=" * 60)
            
            # 更新同步记录状态为成功
            success_msg = f"导出完成，共导出 {len(punish_items)} 个处罚项，{len(all_details)} 条详情记录到 {abs_file_path}"
        except Exception as e:
            error_msg = f"导出数据到Excel失败: {e}"
            logger.error(error_msg)
            raise

# 全局业务服务实例
business_service = BusinessService()
