---
description: 数据库开发模式
globs: 
alwaysApply: false
---
# MetaOrg 数据库开发模式

## 数据库配置
- **数据库类型**: PostgreSQL (华为 GaussDB)
- **连接配置**: 在 application.yml 中配置
- **默认端口**: 15433
- **默认Schema**: public
- **连接池**: HikariCP

## 表命名规范
- 系统表前缀: `sys_` (如 sys_user, sys_role)
- 业务表前缀: `biz_` (如 biz_person, biz_check)
- 字典表前缀: `sys_dict_`
- 日志表前缀: `sys_log_`

## 字段命名规范
- 主键字段: `id` (varchar类型UUID)
- 创建时间: `create_time`
- 更新时间: `update_time`
- 创建人: `create_by`
- 更新人: `update_by`
- 删除标记: `del_flag` (0正常，1删除)
- 状态字段: `status` (0正常，1停用)

## MyBatis 映射模式

### Mapper XML 配置
```xml
<!-- 基础字段映射 -->
<sql id="baseColumns">
    id, create_time, update_time, create_by, update_by, del_flag
</sql>

<!-- 分页查询 -->
<select id="selectList" parameterType="Entity" resultMap="BaseResultMap">
    SELECT <include refid="baseColumns"/>, other_columns
    FROM table_name
    <where>
        del_flag = '0'
        <if test="condition != null and condition != ''">
            AND field = #{condition}
        </if>
    </where>
    ORDER BY create_time DESC
</select>
```

### 结果映射
```xml
<resultMap id="BaseResultMap" type="Entity">
    <id column="id" property="id"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <!-- 关联查询 -->
    <association property="dept" javaType="SysDept">
        <id column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
    </association>
</resultMap>
```

## 实体类模式
```java
// 基础实体类继承
public class BaseEntity {
    private String id;
    private Date createTime;
    private Date updateTime;
    private String createBy;
    private String updateBy;
    private String delFlag;
    // getter/setter...
}

// 业务实体类
@Entity
@Table(name = "biz_person")
public class BizPerson extends BaseEntity {
    @Column(name = "person_name")
    private String personName;
    
    @Column(name = "dept_id") 
    private String deptId;
    
    // 关联对象
    @Transient
    private SysDept dept;
    // getter/setter...
}
```

## 查询模式

### 分页查询
- 使用 PageHelper 插件实现分页
- Controller 层使用 `startPage()` 方法
- 返回 `PageInfo<T>` 包装分页信息

### 动态查询
- 使用 MyBatis 动态 SQL
- 查询条件封装在实体类中
- 支持模糊查询、范围查询、精确匹配

### 关联查询
- 一对一关联使用 `<association>`
- 一对多关联使用 `<collection>`
- 懒加载配置避免 N+1 问题

## 数据库操作规范

### 事务管理
- Service 层方法添加 `@Transactional`
- 只读操作使用 `readOnly=true`
- 异常回滚配置 `rollbackFor=Exception.class`

### 批量操作
- 大批量插入使用 `insertBatch` 方法
- 批量删除使用逻辑删除（del_flag）
- 批量更新避免循环调用

### 数据安全
- 敏感信息加密存储
- SQL 注入防护（参数化查询）
- 数据备份和恢复策略

## 索引优化
- 主键自动创建聚集索引
- 外键字段创建普通索引  
- 查询频繁字段添加复合索引
- 定期分析索引使用情况
