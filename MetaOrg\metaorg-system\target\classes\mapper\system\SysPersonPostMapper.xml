<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.system.mapper.SysPersonPostMapper">

	<resultMap type="SysPersonPost" id="SysPersonPostResult">
		<id     property="personPostId"     column="person_post_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="postName"   column="post_name"   />
		<result property="postCode"   column="post_code"   />
		<result property="postLevel"   column="post_level"   />
		<result property="postCategory"   column="post_category"   />
		<result property="orderNum"   column="order_num"   />
		<result property="status"     column="status"      />
		<result property="prePostTerm"     column="pre_post_term"      />
		<result property="remark"    column="remark"    />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="parentCode" column="parent_code" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>

	<resultMap type="SysPersonPost" id="SysPersonPostChildResult">
		<id     property="personPostId"     column="child_person_post_id"     />
		<result property="parentId"   column="child_parent_id"   />
		<result property="ancestors"  column="child_ancestors"   />
		<result property="postName"   column="child_post_name"   />
		<result property="postCode"   column="child_post_code"   />
		<result property="postLevel"   column="child_post_level"   />
		<result property="postCategory"   column="child_post_category"   />
		<result property="orderNum"   column="child_order_num"   />
		<result property="status"     column="child_status"      />
		<result property="prePostTerm"     column="child_pre_post_term"      />
		<result property="remark"    column="child_remark"    />
		<result property="delFlag"    column="child_del_flag"    />
		<result property="parentName" column="child_parent_name" />
		<result property="parentCode" column="child_parent_code" />
		<result property="createBy"   column="child_create_by"   />
		<result property="createTime" column="child_create_time" />
		<result property="updateBy"   column="child_update_by"   />
		<result property="updateTime" column="child_update_time" />
	</resultMap>

	<resultMap type="SysPersonPost" id="SysPersonPostWithTermResult" extends="SysPersonPostResult">
		<collection property="terms"  column="person_post_id" javaType="java.util.List"
					 foreignColumn="person_post_id" resultMap="termResult" />
	</resultMap>

	<resultMap id="termResult" type="SysPersonPostTerm">
		<id     property="id"    column="term_id"     />
		<result property="deptLevelCode"  column="dept_level_code"   />
		<result property="deptLevelName"  column="dept_level_name"   />
		<result property="postTerm"  column="post_term"   />
	</resultMap>

	<resultMap type="SysPersonPost" id="SysPersonPostTreeResult" extends="SysPersonPostResult">
		<collection  property="childs"  column="person_post_id" javaType="java.util.List"  foreignColumn="parent_id" resultMap="SysPersonPostChildResult" />
	</resultMap>

	<!--职务查询基础字段-->
	<sql id="selectPersonPostBaseFields">
		select a.person_post_id, a.parent_id, a.ancestors, a.post_name, a.post_code, a.post_level, a.post_category, a.order_num, a.status,
			   a.pre_post_term,a.remark, a.del_flag, a.create_by, a.create_time,t.id as term_id,t.dept_level_code,
			   dd.dict_label as dept_level_name, t.post_term
	</sql>
	<!--子职务查询基础字段-->
	<sql id="selectPersonPostChildBaseFields">
		,ca.person_post_id as child_person_post_id, ca.parent_id as child_parent_id, ca.ancestors as child_ancestors, ca.post_name as child_post_name,
		ca.post_code as child_post_code, ca.post_level as child_post_level, ca.post_category as child_post_category, ca.order_num as child_order_num,
		ca.status as child_status,ca.pre_post_term as child_pre_post_term,ca.remark as child_remark, ca.del_flag as child_del_flag, ca.create_by as child_create_by,
		ca.create_time as child_create_time
	</sql>
	<!--职务查询基础表-->
	<sql id="selectPersonPostBaseBaseTable">
		from sys_person_post a
        left join sys_person_post_term t on t.person_post_id = a.person_post_id
		left join sys_dict_data dd on t.dept_level_code = dd.dict_value and dd.dict_type = 'sys_dept_level'
	</sql>
	<sql id="selectPersonPostVo">
		<include refid="selectPersonPostBaseFields"/>
		<include refid="selectPersonPostBaseBaseTable"/>
    </sql>

	<sql id="selectPersonPostTreeVo">
		<include refid="selectPersonPostBaseFields"/>
		<include refid="selectPersonPostChildBaseFields"/>
		from sys_person_post a
		left join sys_person_post_term t on t.person_post_id = a.person_post_id
		left join sys_dict_data dd on t.dept_level_code = dd.dict_value and dd.dict_type = 'sys_dept_level'
		left join sys_person_post ca on ca.parent_id = a.person_post_id and ca.del_flag = '0'
	</sql>
	
	<select id="selectPersonPostList" parameterType="SysPersonPost" resultMap="SysPersonPostWithTermResult">
        <include refid="selectPersonPostVo"/>
        where a.del_flag = '0'
		<if test="personPostId != null and personPostId != ''">
			AND a.person_post_id = #{personPostId}
		</if>
        <if test="parentId != null and parentId != ''">
			AND a.parent_id = #{parentId}
		</if>
		<if test="postName != null and postName != ''">
			AND a.post_name like concat('%', #{postName}, '%')
		</if>
		<if test="postLevel != null and postLevel != 0">
			AND a.post_level = #{postLevel}
		</if>
		<if test="postCategory != null and postCategory != ''">
			AND a.postCategory = #{post_category}
		</if>
		<if test="status != null and status != ''">
			AND a.status = #{status}
		</if>
		order by a.parent_id, a.order_num
    </select>
	<select id="selectPersonPostTreeList" parameterType="SysPersonPost" resultMap="SysPersonPostTreeResult">
		<include refid="selectPersonPostTreeVo"/>
		where a.del_flag = '0'
		<if test="personPostId != null and personPostId != ''">
			AND a.person_post_id = #{personPostId}
		</if>
		<if test="parentId != null and parentId != ''">
			AND a.parent_id = #{parentId}
		</if>
		<if test="postName != null and postName != ''">
			AND a.post_name like concat('%', #{postName}, '%')
		</if>
		<if test="postLevel != null and postLevel != 0">
			AND a.post_level = #{postLevel}
		</if>
		<if test="postCategory != null and postCategory != ''">
			AND a.postCategory = #{post_category}
		</if>
		<if test="status != null and status != ''">
			AND a.status = #{status}
		</if>
		order by a.order_num, ca.order_num
	</select>
	<!-- 查询下级职务数量 -->
	<select id="selectPersonPostCount" parameterType="SysPersonPost" resultType="int">
		select count(1) from sys_person_post
		where del_flag = '0'
		<if test="personPostId != null and personPostId != ''"> and person_post_id = #{personPostId} </if>
		<if test="parentId != null and parentId != ''"> and parent_id = #{parentId} </if>
	</select>
	<!-- 校验职务名称是否唯一 -->
	<select id="checkPersonPostNameUnique" resultMap="SysPersonPostWithTermResult">
	    <include refid="selectPersonPostVo"/>
		where a.post_name=#{postName} and a.parent_id = #{parentId} and a.del_flag = '0' limit 1
	</select>
	<!-- 根据职务ID查询信息 -->
	<select id="selectPersonPostById" parameterType="String" resultMap="SysPersonPostWithTermResult">
		<include refid="selectPersonPostBaseFields"/>
		,(select post_name from sys_person_post where person_post_id = a.parent_id) parent_name,
		(select post_code from sys_person_post where person_post_id = a.parent_id) parent_code
		<include refid="selectPersonPostBaseBaseTable"/>
		where a.person_post_id = #{personPostId}
	</select>
	<!-- 根据职务ID数组查询信息 -->
	<select id="selectPersonPostByIds" parameterType="String" resultMap="SysPersonPostWithTermResult">
		<include refid="selectPersonPostBaseFields"/>
		,(select post_name from sys_person_post where person_post_id = a.parent_id) parent_name,
		(select post_code from sys_person_post where person_post_id = a.parent_id) parent_code
		<include refid="selectPersonPostBaseBaseTable"/>
		where a.person_post_id in
		<foreach collection="array" item="personPostId" open="(" separator="," close=")">
			#{personPostId}
		</foreach>
	</select>
	<!-- 根据ID查询所有子职务 -->
	<select id="selectChildrenPersonPostById" parameterType="String" resultMap="SysPersonPostResult">
		select * from sys_person_post where find_in_set(#{personPostId}, ancestors)
	</select>
	<!-- 根据ID查询所有子职务（正常状态） -->
	<select id="selectNormalChildrenPersonPostById" parameterType="String" resultType="int">
		select count(*) from sys_person_post where status = 0 and del_flag = '0' and find_in_set(#{personPostId}, ancestors)
	</select>
	
	<insert id="insertPersonPost" parameterType="SysPersonPost">
 		insert into sys_person_post
		<trim prefix="(" suffix=")" suffixOverrides=",">
 			<if test="personPostId != null and personPostId != ''">person_post_id,</if>
 			<if test="parentId != null and parentId != ''">parent_id,</if>
 			<if test="postName != null and postName != ''">post_name,</if>
			<if test="postCode != null and postCode != ''">post_code,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
			<if test="postLevel != null">post_level,</if>
			<if test="postCategory != null and postCategory != ''">post_category,</if>
 			<if test="orderNum != null">order_num,</if>
 			<if test="status != null">status,</if>
			<if test="prePostTerm != null">pre_post_term,</if>
			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
 			<if test="personPostId != null and personPostId != ''">#{personPostId},</if>
 			<if test="parentId != null and parentId != ''">#{parentId},</if>
 			<if test="postName != null and postName != ''">#{postName},</if>
			<if test="postCode != null and postCode != ''">#{postCode},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="postLevel != null">#{postLevel},</if>
			<if test="postCategory != null and postCategory != ''">#{postCategory},</if>
			<if test="orderNum != null">#{orderNum},</if>
 			<if test="status != null">#{status},</if>
			<if test="prePostTerm != null">#{prePostTerm},</if>
			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>
	
	<update id="updatePersonPost" parameterType="SysPersonPost">
 		update sys_person_post
 		<set>
 			<if test="parentId != null and parentId != '0'.toString()">parent_id = #{parentId},</if>
 			<if test="postName != null and postName != ''">post_name = #{postName},</if>
			<if test="postCode != null and postCode != ''">post_code = #{postCode},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
			<if test="postLevel != null">post_level = #{postLevel},</if>
			<if test="postCategory != null and postCategory != ''">post_category = #{postCategory},</if>
 			<if test="orderNum != null">order_num = #{orderNum},</if>
			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="prePostTerm != null">pre_post_term = #{prePostTerm},</if>
			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
 		</set>
 		where person_post_id = #{personPostId}
	</update>
	
	<update id="updatePersonPostChildren" parameterType="java.util.List">
	    update sys_person_post set ancestors =
	    <foreach collection="personPosts" item="item" index="index"
	        separator=" " open="case person_post_id" close="end">
	        when #{item.personPostId} then #{item.ancestors}
	    </foreach>
	    where person_post_id in
	    <foreach collection="personPosts" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.personPostId}
	    </foreach>
	 </update>

	<delete id="deletePersonPostById" parameterType="String">
		update sys_person_post set del_flag = '2' where person_post_id = #{personPostId}
	</delete>
	<!-- 修改所在职务正常状态 -->
	<update id="updatePersonPostStatusNormal" parameterType="String">
 	    update sys_person_post set status = '0' where person_post_id in
 	    <foreach collection="array" item="personPostId" open="(" separator="," close=")">
        	#{personPostId}
        </foreach>
	</update>

</mapper> 