package com.metaorg.web.controller.api.message;

import java.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.metaorg.library.service.IBizMessageNotifyRecordService;
import com.metaorg.common.core.domain.ResponseBodyApi;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import com.metaorg.library.domain.BizMessageNotifyRecord;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.enums.BusinessType;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.utils.DateUtils;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/api/messages/notify/records")
public class MessageNotifyRecordController extends BaseController  {
    
    @Autowired
    private IBizMessageNotifyRecordService bizMessageNotifyRecordService;

    /**
     * 获取列表查询条件
    */
    private void getQueryOptions(BizMessageNotifyRecord bizMessageNotifyRecord, Map<String, Object> paramMap) {
        String businessType = Convert.toStr(paramMap.get("business_type"), "");
        if (StringUtils.isNotEmpty(businessType)) {
            bizMessageNotifyRecord.setBusinessType(businessType);
        }

        String messageType = Convert.toStr(paramMap.get("message_type"), "");
        if (StringUtils.isNotEmpty(messageType)) {
            bizMessageNotifyRecord.setMessageType(messageType);
        }

        Boolean isRead = Convert.toBool(paramMap.get("is_read"), null);
        if (isRead != null) {
            bizMessageNotifyRecord.setIsRead(isRead);
        }

        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizMessageNotifyRecord.getParams().put("keyword", keyword);
        }

        String sendTimeBegin = Convert.toStr(paramMap.get("send_time_begin"), "");
        if (StringUtils.isNotEmpty(sendTimeBegin)) {
            bizMessageNotifyRecord.getParams().put("sendTimeBegin", sendTimeBegin);
        }

        String sendTimeEnd = Convert.toStr(paramMap.get("send_time_end"), "");
        if (StringUtils.isNotEmpty(sendTimeEnd)) {
            bizMessageNotifyRecord.getParams().put("sendTimeEnd", sendTimeEnd);
        }
    }

    @Log(title = "PAD端-查询消息通知记录列表", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("")
    @ResponseBody
    public ResponseBodyApi<List<BizMessageNotifyRecord>> getRecords(HttpServletResponse response, @RequestParam Map<String, Object> paramMap) {
        BizMessageNotifyRecord bizMessageNotifyRecord = new BizMessageNotifyRecord();
        bizMessageNotifyRecord.setReceiverId(getUserId());

        getQueryOptions(bizMessageNotifyRecord, paramMap);

        startPage();
        List<BizMessageNotifyRecord> list = bizMessageNotifyRecordService.selectBizMessageNotifyRecordList(bizMessageNotifyRecord);

        ResponseBodyApi<List<BizMessageNotifyRecord>> rsBody = new ResponseBodyApi<List<BizMessageNotifyRecord>>(list);
        rsBody.setMessage("OK");
        return rsBody;
    }

    @Log(title = "PAD端-查询消息通知记录详情", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("/{id}")
    @ResponseBody
    public ResponseBodyApi<BizMessageNotifyRecord> getRecord(@PathVariable("id") String id) {
        BizMessageNotifyRecord record = bizMessageNotifyRecordService.selectBizMessageNotifyRecordById(id);
        
        if (record != null) {
            record.setReceiverId(getUserId());
            record.setIsRead(true);
            record.setReadTime(DateUtils.getNowDate());
            record.setUpdateTime(DateUtils.getNowDate());
            bizMessageNotifyRecordService.updateBizMessageNotifyRecordByIds(record);
        }

        ResponseBodyApi<BizMessageNotifyRecord> rsBody = new ResponseBodyApi<BizMessageNotifyRecord>(record);
        rsBody.setMessage("OK");
        return rsBody;
    }

    @Log(title = "PAD端-全部已读", businessType = BusinessType.UPDATE, isSaveResponseData = false)
    @PostMapping("/read_all")
    @ResponseBody
    public ResponseBodyApi<BizMessageNotifyRecord> readAll() {
        BizMessageNotifyRecord record = new BizMessageNotifyRecord();
        record.setReceiverId(getUserId());
        record.setIsRead(true);
        record.setReadTime(DateUtils.getNowDate());
        record.setUpdateTime(DateUtils.getNowDate());
        bizMessageNotifyRecordService.updateBizMessageNotifyRecordByIds(record);

        ResponseBodyApi<BizMessageNotifyRecord> rsBody = new ResponseBodyApi<BizMessageNotifyRecord>();
        rsBody.setMessage("OK");
        return rsBody;
    }
}
