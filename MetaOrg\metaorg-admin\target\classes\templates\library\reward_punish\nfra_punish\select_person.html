<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('选择干部')" />
</head>

<body class="gray-bg">
	<div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="person-form">
					<input type="hidden" id="id" name="id" th:value="${id}">
					<input type="hidden" id="bindPersonId" name="bindPersonId">
					<div class="select-list">
						<ul>
							<li>
								姓名：<input type="text" name="name" placeholder="请输入姓名，支持模糊查询" th:value="${personName}" readonly/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
	        
	        <div class="col-sm-12 select-table table-striped">
			    <table id="bootstrap-table"></table>
			</div>
		</div>
	</div>
	<th:block th:include="include :: footer" />
	<script th:inline="javascript">
		/*<![CDATA[*/
        const editFlag = /*[[${@permission.hasPermi('library:nfra_punish:edit')}]]*/ false;
		var sysUserSex = /*[[${@dict.getType('sys_user_sex')}]]*/ [];
		var prefix = ctx + "library/person";

		$(function() {
			var options = {
		        url: ctx + "library/reward_punish/nfra_punish/getPersonList",
		        queryParams: queryParams,
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
		        clickToSelect: true,
		        rememberSelected: true,
				pageSize: 10,
				pageList: [10, 20, 50, 100],
		        columns: [
		        {
		            field: 'id',
		            title: 'ID',
		            visible: false
		        },
		        {
		            field: 'name',
		            title: '姓名',
		        },
		        {
		            field: 'sex',
		            title: '性别',
					formatter: function(value, row, index) {
						return $.table.selectDictLabel(sysUserSex, value);
					}
		        },
				{
					field: 'birthday',
					title: '出生年月',
				},
				{
					field: 'dept.deptName',
					title: '部门',
				},
				{
					field: 'jobUnit',
					title: '职务',
				},
				{
					title: '操作',
					align: 'center',
					width: '100px',
					formatter: function(value, row, index) {
						var actions = [];
						actions.push(`<a class="btn btn-success btn-xs ${editFlag}" href="javascript:void(0)" onclick="bindPerson('${row.id}', '${row.name}')"><i class="fa fa-edit"></i>关联人员</a>`);
						return actions.join('');
					}
				}]
		    };
		    $.table.init(options);
		});
		
		function queryParams(params) {
			var search = $.table.queryParams(params);
			return search;
		}

		function bindPerson(personId, personName) {
			$("#bindPersonId").val(personId);
            $.modal.confirm(`关联后系统将自动在"${personName}"的任务表中添加此项处罚记录，您确认处罚公示中的"${personName}"与系统内的"${personName}"是同一人吗？`, function() {
				$.operate.save(ctx + "library/reward_punish/nfra_punish/bindPerson", 
					$('#person-form').serialize(),
					function(result){
						if (result.code === 0){
							$.modal.close();
							$.modal.msg(result.msg);
						}
					}
				);
			});
		}
		
		function submitHandler() {
			$.modal.close();
		}
	</script>
</body>
</html>