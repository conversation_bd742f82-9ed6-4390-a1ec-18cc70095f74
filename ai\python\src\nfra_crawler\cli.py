"""
命令行工具
"""

import asyncio
import json
import sys
import argparse
from datetime import datetime
from loguru import logger

from .services.business_service import business_service
from .services.crawler_service import crawler_service
from .services.ai_service import ai_service
from .models.database import CrawlRequest, BizSyncRecord
from .utils.logger import setup_logger


async def test_crawler():
    """测试爬虫功能"""
    logger.info("开始测试爬虫功能...")
    
    try:
        # 创建测试请求
        test_request = CrawlRequest(
            create_time=int(datetime.now().timestamp() * 1000),
            request_id=f"test_{int(datetime.now().timestamp())}"
        )
        
        # 创建同步记录
        sync_record = BizSyncRecord(
            business_type="nfra_punish",
            business_id=test_request.request_id,
            request_id=test_request.request_id,
            request_time=datetime.fromtimestamp(test_request.create_time / 1000),
            status="pending"
        )
        
        # 执行爬取
        items = await crawler_service.crawl_punish_list(
            test_request, sync_record.id
        )
        
        logger.info(f"测试完成，获取到 {len(items)} 条记录")
        
        # 显示前几条记录
        for i, item in enumerate(items[:3]):
            logger.info(f"记录 {i+1}: {item.title}")
        
    except Exception as e:
        logger.error(f"测试爬虫功能失败: {e}")


async def test_ai_cleaning():
    """测试AI清洗功能"""
    logger.info("开始测试AI清洗功能...")
    
    # 测试HTML内容
    test_html = """
    <table align="center">
        <tbody>
            <tr>
                <td width="32"><p align="justify"><span>序号</span></p></td>
                <td width="105"><p align="center"><span>当事人名称</span></p></td>
                <td width="121"><p align="center"><span>行政处罚决定书文号</span></p></td>
                <td width="149"><p align="center"><span>主要违法违规行为</span></p></td>
                <td width="82"><p align="center"><span>行政处罚内容</span></p></td>
                <td width="94"><p align="center"><span>作出决定机关</span></p></td>
            </tr>
            <tr>
                <td width="32"><p align="justify"><span>1</span></p></td>
                <td width="105"><p align="center"><span>张三（时任某银行副行长）</span></p></td>
                <td width="121"><p align="center"><span>新金监罚决字〔2024〕1号</span></p></td>
                <td width="149"><p align="center"><span>贷后管理不到位</span></p></td>
                <td width="82"><p align="justify"><span>警告，并处5万元罚款</span></p></td>
                <td width="94"><p align="center"><span>新疆金融监管局</span></p></td>
            </tr>
        </tbody>
    </table>
    """
    
    try:
        result = await ai_service.clean_html_table(test_html)
        logger.info(f"AI清洗完成，获得 {len(result)} 条记录")
        
        for item in result:
            logger.info(f"清洗结果: {item.model_dump()}")
        
    except Exception as e:
        logger.error(f"测试AI清洗功能失败: {e}")


async def test_full_process():
    """测试完整流程"""
    logger.info("开始测试完整流程...")
    
    try:
        # 创建测试请求
        test_request = CrawlRequest(
            create_time=int(datetime.now().timestamp() * 1000),
            request_id=f"full_test_{int(datetime.now().timestamp())}"
        )
        
        # 创建同步记录
        sync_record = BizSyncRecord(
            business_type="nfra_punish",
            business_id=test_request.request_id,
            request_id=test_request.request_id,
            request_time=datetime.fromtimestamp(test_request.create_time / 1000),
            status="pending"
        )
        
        # 执行完整业务流程
        await business_service.process_crawl_request(test_request, sync_record)
        
        logger.info("完整流程测试完成")
        
    except Exception as e:
        logger.error(f"测试完整流程失败: {e}")


async def check_health():
    """健康检查"""
    logger.info("执行健康检查...")
    
    try:
        health_status = await business_service.health_check()
        logger.info(f"健康检查结果: {json.dumps(health_status, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")


async def get_task_status(request_id: str):
    """获取任务状态"""
    logger.info(f"查询任务状态: {request_id}")
    
    try:
        status = await business_service.get_task_status(request_id)
        if status:
            logger.info(f"任务状态: {json.dumps(status, indent=2, ensure_ascii=False, default=str)}")
        else:
            logger.warning(f"未找到任务: {request_id}")
        
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")


def main():
    """命令行主函数"""
    setup_logger()
    
    parser = argparse.ArgumentParser(description="NFRA爬虫命令行工具")
    parser.add_argument("command", choices=[
        "test-crawler", "test-ai", "test-full", "health", "status"
    ], help="要执行的命令")
    parser.add_argument("--task-id", help="任务ID（用于status命令）")
    
    args = parser.parse_args()
    
    try:
        if args.command == "test-crawler":
            asyncio.run(test_crawler())
        elif args.command == "test-ai":
            asyncio.run(test_ai_cleaning())
        elif args.command == "test-full":
            asyncio.run(test_full_process())
        elif args.command == "health":
            asyncio.run(check_health())
        elif args.command == "status":
            if not args.request_id:
                logger.error("status命令需要提供--task-id参数")
                sys.exit(1)
            asyncio.run(get_task_status(args.request_id))
        
    except KeyboardInterrupt:
        logger.info("命令被用户中断")
    except Exception as e:
        logger.error(f"命令执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
