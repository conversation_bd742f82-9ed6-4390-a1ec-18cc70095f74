<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>干部管理信息系统</title>
    <meta name="description" content="干部管理信息系统">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.7.7}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">
        label.error { position:inherit;  }
        #divSsoContent {
            background: rgba(255, 255, 255, .2);
            border: 1px solid rgba(255, 255, 255, .3);
            box-shadow: 0 3px 0 rgba(12, 12, 12, .03);
            border-radius: 3px;
            padding: 30px;
        }
        .error-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
        }
        .btn-retry {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
            padding: 8px 20px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn-retry:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            color: white;
            text-decoration: none;
        }
        .btn-login {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
            padding: 8px 20px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn-login:hover {
            background: #73d13d;
            border-color: #73d13d;
            color: white;
            text-decoration: none;
        }
    </style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body class="signin">
    <div class="signinpanel">
        <div class="row">
            <div class="col-sm-7">
                <div class="signin-info">
                    <div class="logopanel m-b">
                        <h1><img alt="[ METAVUN ]" src="../static/logo-white.png" th:src="@{/logo-white.png}"></h1>
                    </div>
                    <div class="m-b"></div>
                    <h4>欢迎使用 <strong>干部管理信息系统</strong></h4>
                </div>
            </div>
            <div class="col-sm-5">
                <div id="divSsoContent">
                    <div th:if="${error!=null && error!=''}">
                        <div class="alert alert-danger">
                            <p th:text="${error}">认证过程中发生未知错误，请重试或联系管理员</p>
                        </div>
                        <div class="error-actions">
                            <a href="/sso/entry" class="btn-retry">
                                <i class="fa fa-refresh"></i> 重新认证
                            </a>
                            <!-- <a href="/login" class="btn-login">
                                <i class="fa fa-sign-in"></i> 普通登录
                            </a> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="signup-footer">
            <div class="pull-left">
                [[${copyRight}]] <br>
            </div>
        </div>
    </div>
<script th:inline="javascript"> 
/*<![CDATA[*/
var ctx = /*[[@{/}]]*/ ""; var captchaType = /*[[${captchaType}]]*/ ""; 
/*]]>*/
</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.7.7}"></script>
<script src="../static/ruoyi/js/meta-ui.js" th:src="@{/ruoyi/js/meta-ui.js?v=4.7.7}"></script>
</body>
</html>
