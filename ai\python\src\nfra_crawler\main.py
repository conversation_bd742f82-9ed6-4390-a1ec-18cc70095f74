"""
新疆金融监管局行政处罚信息自动化抓取和处理系统 - 主程序
"""

import argparse
import asyncio
import signal
import sys
from pathlib import Path

from nfra_crawler.config.app_config import app_config
from nfra_crawler.services.business_service import business_service
from nfra_crawler.services.rabbitmq_service import rabbitmq_service
from nfra_crawler.utils.db_manager import db_manager
from nfra_crawler.utils.logger import logger


class NFRACrawlerApp:
    def __init__(self, export_file: str = None):
        self.running = False
        self.export_file = export_file
        
    async def run(self):
        """运行应用"""
        if self.export_file:
            # 导出模式
            await self.run_export_mode()
        else:
            # 正常消息队列模式
            await self.run_queue_mode()
    
    async def run_export_mode(self):
        """导出模式运行"""
        try:
            logger.info(f"启动导出模式，目标文件: {self.export_file}")
            
            # 检查配置
            self._validate_config()
            
            # 健康检查
            health_status = await business_service.health_check()
            logger.info(f"健康检查结果: {health_status}")
            
            if health_status["status"] != "healthy":
                raise RuntimeError("健康检查失败，无法启动应用")
            
            # 执行导出
            await business_service.export_to_excel(self.export_file)
            
        except Exception as e:
            logger.error(f"导出模式运行失败: {e}")
            raise
    
    async def run_queue_mode(self):
        """消息队列模式运行"""
        try:
            await self.startup()
            
            # 设置信号处理器
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            logger.info("应用正在运行，按Ctrl+C停止...")
            
            # 主循环
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("接收到停止信号")
        finally:
            await self.cleanup()

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，准备停止应用...")
        self.running = False

    def _validate_config(self):
        """验证配置"""
        required_configs = [
            'database.host',
            'database.port', 
            'database.name',
            'database.user',
            'openai.api_key'
        ]
        
        for config_key in required_configs:
            if not app_config.get(config_key):
                raise ValueError(f"缺少必要配置: {config_key}")
    
    async def startup(self):
        """启动应用"""
        try:
            logger.info("正在启动NFRA爬虫应用...")
            
            # 检查配置
            self._validate_config()
            
            # 连接RabbitMQ
            logger.info("连接RabbitMQ...")
            rabbitmq_service.connect()
            
            # 设置消息处理器
            rabbitmq_service.set_message_handler(business_service.process_crawl_request)
            
            # 健康检查
            health_status = await business_service.health_check()
            logger.info(f"健康检查结果: {health_status}")
            
            if health_status["status"] != "healthy":
                raise RuntimeError("健康检查失败，无法启动应用")
            
            self.running = True
            logger.info("NFRA爬虫应用启动成功")
            
        except Exception as e:
            logger.error(f"启动应用时发生错误: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("正在清理资源...")
            
            # 停止RabbitMQ消费
            if rabbitmq_service.channel:
                rabbitmq_service.stop_consuming()
            
            # 断开RabbitMQ连接
            rabbitmq_service.disconnect()
            
            # 关闭数据库连接池
            db_manager.close_pool()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='新疆金融监管局行政处罚信息自动化抓取和处理系统')
    parser.add_argument('--export', type=str, help='导出Excel文件名（如：punish_data.xlsx）')
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_args()
    app = NFRACrawlerApp(export_file=args.export)
    
    try:
        await app.run()
    except Exception as e:
        logger.error(f"应用运行失败: {e}")
        sys.exit(1)


def cli_main():
    """命令行入口"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("应用被用户中断")
    except Exception as e:
        logger.error(f"应用异常退出: {e}")
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
