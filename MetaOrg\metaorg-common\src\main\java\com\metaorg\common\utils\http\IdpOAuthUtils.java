package com.metaorg.common.utils.http;

import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Map;

/**
 * Minimal OAuth token client for posting application/x-www-form-urlencoded with custom headers.
 *
 * Security notes:
 * - Does NOT log sensitive secrets (Authorization/Password).
 * - Respects SSRF validation via HttpUtils.validateUriSsrf.
 * - Uses default JVM trust store for HTTPS (no trust-all behavior).
 */
public final class IdpOAuthUtils {

    private static final Logger log = LoggerFactory.getLogger(IdpOAuthUtils.class);

    private IdpOAuthUtils() { }

    /**
     * New flexible entry using inner Request model with builder.
     */
    public static String fetchToken(Request request) throws IOException {
        return executeRequest(request);
    }
    
    /**
     * Execute HTTP request with flexible method support (GET/POST).
     */
    public static String executeRequest(Request request) throws IOException {
        Objects.requireNonNull(request, "request must not be null");
        String url = request.getUrl();
        String method = request.getMethod();

        if (!HttpUtils.validateUriSsrf(url)) {
            throw new IOException("Illegal or unsafe URL: " + safe(url));
        }

        HttpURLConnection conn = null;
        try {
            URL u = new URL(url);
            conn = (HttpURLConnection) u.openConnection();
            conn.setRequestMethod(method);
            conn.setDoInput(true);
            conn.setConnectTimeout(request.getConnectTimeoutMs());
            conn.setReadTimeout(request.getReadTimeoutMs());

            // headers
            if (request.getAuthorization() != null) {
                conn.setRequestProperty("Authorization", request.getAuthorization());
            }
            for (Map.Entry<String, String> e : request.getHeaders().entrySet()) {
                conn.setRequestProperty(e.getKey(), e.getValue());
            }
            
            // 只有POST请求才设置Content-Type和输出流
            if ("POST".equalsIgnoreCase(method)) {
                conn.setDoOutput(true);
                conn.setRequestProperty("Content-Type", request.getContentType());
                if (!request.getHeaders().containsKey("Accept")) {
                    conn.setRequestProperty("Accept", "application/json; charset=utf-8");
                }

                byte[] body = buildBody(request);
                conn.setRequestProperty("Content-Length", String.valueOf(body.length));

                log.info("POST {} (Authorization: {}, Content-Length: {})",
                        safe(url), maskBasic(request.getAuthorization()), body.length);

                try (OutputStream os = conn.getOutputStream()) {
                    os.write(body);
                    os.flush();
                }
            } else {
                // GET请求
                if (!request.getHeaders().containsKey("Accept")) {
                    conn.setRequestProperty("Accept", "application/json; charset=utf-8");
                }
                log.info("GET {} (Authorization: {})",
                        safe(url), maskBasic(request.getAuthorization()));
            }

            int code = conn.getResponseCode();
            InputStream is = code >= 200 && code < 300 ? conn.getInputStream() : conn.getErrorStream();
            String resp = readAll(is);
            log.info("recv (status={}): {}", code, truncate(resp));

            if (code < 200 || code >= 300) {
                throw new HttpCallException(code, safe(url), truncate(resp), null);
            }
            return resp;
        }catch (IOException e) {
            throw new HttpCallException(-1, safe(request.getUrl()), truncate(e.getMessage()), e);
        }
        finally {
            if (conn != null) conn.disconnect();
        }
    }

    private static byte[] buildBody(Request req) throws IOException {
        if (req.getRawBody() != null) {
            return req.getRawBody().getBytes(StandardCharsets.UTF_8);
        }
        // x-www-form-urlencoded
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> e : req.getFormParams().entrySet()) {
            if (!first) sb.append('&');
            first = false;
            sb.append(URLEncoder.encode(e.getKey(), StandardCharsets.UTF_8.name()))
              .append('=')
              .append(URLEncoder.encode(e.getValue(), StandardCharsets.UTF_8.name()));
        }
        return sb.toString().getBytes(StandardCharsets.UTF_8);
    }

    private static String readAll(InputStream is) throws IOException {
        if (is == null) return "";
        try (BufferedReader br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        }
    }

    private static String maskBasic(String basic) {
        if (basic == null) return null;
        // Example: "Basic abcdefgh..." -> keep scheme + first 6 chars
        try {
            String s = basic.trim();
            int idx = s.indexOf(' ');
            if (idx > 0 && idx < s.length() - 1) {
                String scheme = s.substring(0, idx);
                String creds = s.substring(idx + 1);
                String head = creds.length() <= 6 ? creds : creds.substring(0, 6) + "***";
                return scheme + " " + head;
            }
            return s.length() <= 6 ? s : s.substring(0, 6) + "***";
        } catch (Exception ignore) {
            return "***";
        }
    }

    private static String safe(String s) {
        return StringEscapeUtils.unescapeJava(StringEscapeUtils.escapeJava(s));
    }

    private static String truncate(String s) {
        if (s == null) return null;
        return s.length() > 512 ? s.substring(0, 512) + "..." : s;
    }

    /**
     * Rich exception carrying status code, url and a truncated response/body snippet for logging.
     */
    public static class HttpCallException extends IOException {
        private final int statusCode;
        private final String url;
        private final String responseSnippet;

        public HttpCallException(int statusCode, String url, String responseSnippet, Throwable cause) {
            super("HTTP " + statusCode + " error: " + responseSnippet, cause);
            this.statusCode = statusCode;
            this.url = url;
            this.responseSnippet = responseSnippet;
        }

        public int getStatusCode() { return statusCode; }
        public String getUrl() { return url; }
        public String getResponseSnippet() { return responseSnippet; }
    }

    // === Inner Request model (merged for simplicity) ===
    public static final class Request {
        private final String url;
        private final String method;
        private final String authorization;
        private final Map<String, String> headers;
        private final Map<String, String> formParams;
        private final String rawBody;
        private final String contentType;
        private final int connectTimeoutMs;
        private final int readTimeoutMs;

        private Request(Builder b) {
            this.url = b.url;
            this.method = b.method;
            this.authorization = b.authorization;
            this.headers = Collections.unmodifiableMap(new HashMap<String, String>(b.headers));
            this.formParams = Collections.unmodifiableMap(new HashMap<String, String>(b.formParams));
            this.rawBody = b.rawBody;
            this.contentType = b.contentType;
            this.connectTimeoutMs = b.connectTimeoutMs;
            this.readTimeoutMs = b.readTimeoutMs;
        }

        public String getUrl() { return url; }
        public String getMethod() { return method; }
        public String getAuthorization() { return authorization; }
        public Map<String, String> getHeaders() { return headers; }
        public Map<String, String> getFormParams() { return formParams; }
        public String getRawBody() { return rawBody; }
        public String getContentType() { return contentType; }
        public int getConnectTimeoutMs() { return connectTimeoutMs; }
        public int getReadTimeoutMs() { return readTimeoutMs; }

        public static Builder newBuilder(String url) { return new Builder(url); }

        public static Builder passwordGrant(String url, String basicAuthorization, String username, String password) {
            return new Builder(url)
                    .authorization(basicAuthorization)
                    .acceptJsonUtf8()
                    .contentType("application/x-www-form-urlencoded")
                    .formParam("grant_type", "password")
                    .formParam("dlzh", username)
                    .formParam("dlmm", password)
                    .header("Referer", "localhost");
        }

        public static final class Builder {
            private final String url;
            private String method = "POST";  // 默认POST方法
            private String authorization;
            private Map<String, String> headers = new HashMap<String, String>();
            private Map<String, String> formParams = new HashMap<String, String>();
            private String rawBody;
            private String contentType = "application/x-www-form-urlencoded";
            private int connectTimeoutMs = 10_000;
            private int readTimeoutMs = 15_000;

            public Builder(String url) { this.url = Objects.requireNonNull(url, "url must not be null"); }
            public Builder method(String method) { this.method = method != null ? method.toUpperCase() : "POST"; return this; }
            public Builder authorization(String authorization) { this.authorization = authorization; return this; }
            public Builder header(String name, String value) { if (name != null && value != null) this.headers.put(name, value); return this; }
            public Builder headers(Map<String,String> headers) { if (headers != null) this.headers.putAll(headers); return this; }
            public Builder formParam(String name, String value) { if (name != null && value != null) this.formParams.put(name, value); return this; }
            public Builder formParams(Map<String,String> params) { if (params != null) this.formParams.putAll(params); return this; }
            public Builder rawBody(String body, String contentType) { this.rawBody = body; if (contentType != null) this.contentType = contentType; return this; }
            public Builder contentType(String contentType) { if (contentType != null) this.contentType = contentType; return this; }
            public Builder acceptJsonUtf8() { this.headers.put("Accept", "application/json; charset=utf-8"); return this; }
            public Builder connectTimeoutMs(int ms) { this.connectTimeoutMs = ms; return this; }
            public Builder readTimeoutMs(int ms) { this.readTimeoutMs = ms; return this; }
            public Request build() { return new Request(this); }
        }
    }
}

