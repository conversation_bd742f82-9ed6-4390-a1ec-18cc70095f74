import os
from datetime import datetime
from pathlib import Path
from typing import List
import json

import pandas as pd

from nfra_crawler.models.database import BizSyncPunishItem,BizSyncPunishDetail
from nfra_crawler.utils.logger import logger

class ExcelService:
    """Excel导出服务"""
    
    def export_punish_data(self, items: List[BizSyncPunishItem], details: List[BizSyncPunishDetail], file_path: str) -> str:
        """
        导出处罚数据到Excel文件
        
        Args:
            items: 处罚公示项列表
            details: 处罚详情数据列表
            file_path: 导出文件路径
            
        Returns:
            str: 导出文件的绝对路径
        """
        try:
            # 验证文件路径
            abs_path = self._validate_file_path(file_path)
            
            # 准备数据
            items_data = self._prepare_items_data(items)
            details_data = self._prepare_details_data(details)
            
            # 创建Excel文件
            with pd.ExcelWriter(abs_path, engine='openpyxl') as writer:
                # 处罚公示列表Sheet
                items_df = pd.DataFrame(items_data)
                items_df.to_excel(writer, sheet_name='处罚公示列表', index=False)
                
                # 处罚公示明细Sheet
                details_df = pd.DataFrame(details_data)
                details_df.to_excel(writer, sheet_name='处罚公示明细', index=False)
            
            logger.info(f"Excel文件导出成功: {abs_path}")
            return abs_path
            
        except Exception as e:
            logger.error(f"导出Excel文件失败: {e}")
            raise
    
    def _validate_file_path(self, file_path: str) -> str:
        """验证并处理文件路径"""
        path = Path(file_path)
        
        # 确保文件扩展名为.xlsx
        if path.suffix.lower() != '.xlsx':
            path = path.with_suffix('.xlsx')
        
        # 转换为绝对路径
        abs_path = path.resolve()
        
        # 检查目录是否存在，不存在则创建
        abs_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 检查目录写权限
        if not os.access(abs_path.parent, os.W_OK):
            raise PermissionError(f"没有写入权限: {abs_path.parent}")
        
        return str(abs_path)
    
    def _prepare_items_data(self, items: List[BizSyncPunishItem]) -> List[dict]:
        """准备处罚公示列表数据"""
        data = []
        for item in items:
            data.append({
                '处罚公示标识': item.id,
                '同步记录标识': item.sync_record_id,
                '同步请求标识': item.request_id,
                '标题': item.title,
                '批准机关': item.reward_punish_org,
                '发布日期': item.published_date.strftime('%Y-%m-%d') if item.published_date else '',
                '来源名称': item.source_name,
                '来源标识': item.source_id,
                '来源网址': item.source_url
            })
        return data
    
    def _prepare_details_data(self, details: List[BizSyncPunishDetail]) -> List[dict]:
        """准备处罚公示明细数据"""
        data = []
        for detail in details:
            data.append({
                '处罚公示标识': detail.punish_item_id,
                '同步请求标识': detail.request_id,
                '序号': detail.order_num,
                '当事人名称': detail.party_name,
                '当事人时任职务': detail.party_position,
                '行政处罚决定书文号': detail.punish_doc_no,
                '主要违法违规行为': detail.violation,
                '处罚依据': detail.punish_basis,
                '行政处罚内容': detail.punish_content,
                '作出处罚决定的日期': detail.punish_date.strftime('%Y-%m-%d') if detail.punish_date else '',
                '作出决定机关': detail.decision_authority,
                '当事人类型': detail.party_type,
                '行政处罚内容JSON内容': json.dumps(detail.punish_json_content, ensure_ascii=False) if detail.punish_json_content else '',
                '来源标识': detail.source_id,
                '来源网址': detail.source_url
            })
        return data


# 创建全局实例
excel_service = ExcelService()