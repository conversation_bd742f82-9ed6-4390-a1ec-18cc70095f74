package com.metaorg.web.controller.system;

import com.metaorg.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * OAuth2认证服务器模拟控制器
 * 用于测试OAuth2单点登录流程
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/idp/oauth2")
public class SsoLoginController {

    /**
     * OAuth2授权端点模拟
     * 模拟OAuth2认证服务器的授权页面
     */
    @GetMapping("/authorize")
    public String authorize(HttpServletRequest request, HttpServletResponse response, ModelMap mmap) throws IOException {
        // 获取OAuth2参数
        String clientId = request.getParameter("client_id");
        String redirectUri = request.getParameter("redirect_uri");
        String responseType = request.getParameter("response_type");
        String state = request.getParameter("state");
        
        // 验证必要参数
        if (clientId == null || redirectUri == null || responseType == null) {
            mmap.put("error", "缺少必要的OAuth2参数");
            return "sso/error";
        }
        
        // 验证response_type
        if (!"code".equals(responseType)) {
            mmap.put("error", "不支持的response_type: " + responseType);
            return "sso/error";
        }
        
        // 生成初始授权码
        String initialAuthCode = "testAuthCode_" + clientId + "_" + System.currentTimeMillis();
        
        // 将参数传递给授权页面
        mmap.put("clientId", clientId);
        mmap.put("redirectUri", redirectUri);
        mmap.put("state", state);
        mmap.put("authCode", initialAuthCode);
        
        return "sso/authorize";
    }
    
//    /**
//     * 处理授权确认
//     * 模拟用户确认授权后，生成code并重定向
//     */
//    @PostMapping("/authorize")
//    public void authorizeConfirm(HttpServletRequest request, HttpServletResponse response) throws IOException {
//        // 获取参数
//        String clientId = request.getParameter("client_id");
//        String redirectUri = request.getParameter("redirect_uri");
//        String state = request.getParameter("state");
//
//
//        // 模拟用户验证（这里简单验证，实际应该查询数据库）
//        // 构建重定向URL
//        StringBuilder redirectUrl = new StringBuilder(redirectUri);
//        redirectUrl.append("?code=").append("admin");
//        if (state != null && !state.trim().isEmpty()) {
//            redirectUrl.append("&state=").append(state);
//        }
//    }
    
    /**
     * 获取Token端点模拟
     * 模拟OAuth2认证服务器的token端点
     */
    @PostMapping("/getToken")
    @ResponseBody
    public AjaxResult getToken(HttpServletRequest request) {
        String code = request.getParameter("code");
        String clientId = request.getParameter("client_id");
        String clientSecret = request.getParameter("client_secret");
        String grantType = request.getParameter("grant_type");
        
        // 验证参数
        if (code == null || clientId == null || clientSecret == null) {
            return AjaxResult.error("缺少必要的参数");
        }
        
        if (!"authorization_code".equals(grantType)) {
            return AjaxResult.error("不支持的grant_type: " + grantType);
        }
        
        // 模拟验证授权码（实际应该验证code的有效性）
        if (code.startsWith("testAuthCode_")) {
            // 生成访问令牌
            //分割code，分割符为下划线，并获取第二部分
            String[] arr = code.split("_");
            if(arr.length < 2) {
                return AjaxResult.error("无效的授权码");
            }
            String clientUserId = arr[1];
            String accessToken = "accessToken_" + clientUserId + "_" + UUID.randomUUID().toString().replace("-", "");
            String refreshToken = "refreshToken_" + UUID.randomUUID().toString().replace("-", "");
            
            AjaxResult result = AjaxResult.success("获取token成功");
            result.put("access_token", accessToken);
            result.put("token_type", "Bearer");
            result.put("expires_in", 3600);
            result.put("refresh_token", refreshToken);
            result.put("scope", "read write");
            
            return result;
        } else {
            return AjaxResult.error("无效的授权码");
        }
    }
    
    /**
     * 获取用户信息端点模拟
     * 模拟OAuth2认证服务器的用户信息端点
     * 接口规范：
     * URL: GET /idp/oauth2/getUserInfo
     * 参数: access_token, client_id
     */
    @GetMapping("/getUserInfo")
    @ResponseBody
    public AjaxResult getUserInfo(HttpServletRequest request) {
        String accessToken = request.getParameter("access_token");
        String clientId = request.getParameter("client_id");
        
        // 参数验证
        if (clientId == null || clientId.trim().isEmpty()) {
            return AjaxResult.error("1001", "缺少参数client_id");
        }
        
        if (accessToken == null || accessToken.trim().isEmpty()) {
            return AjaxResult.error("2001", "缺少参数access_token");
        }
        
        // 验证access_token有效性（模拟验证）
        if (!accessToken.startsWith("accessToken_")) {
            return AjaxResult.error("2002", "参数access_token不正确或过期");
        }

        String[] arr = accessToken.split("_");
        if(arr.length < 2) {
            return AjaxResult.error("无效的Token");
        }
        String clientUserId = arr[1];
        
        // 模拟用户信息返回（符合真实接口规范）
        AjaxResult result = AjaxResult.success("获取用户信息成功");
        result.put("loginName", clientUserId);
        result.put("displayName", "朱立荣");
        result.put("spRoleList", new String[]{"admin"});
        result.put("orgNumber", "8038860");
        result.put("mobile", "13211111111");
        result.put("mail", null);
        
        return result;
    }
}
