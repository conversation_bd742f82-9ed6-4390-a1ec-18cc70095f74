<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>干部统计分析</title>
    <th:block th:include="include :: header('干部统计分析')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: ztree-css" />
    <style>
      .col-left {
        border-right: 1px solid #ddd;
      }
      .gray-bg {
        padding-right: 0 !important;
      }
      /* 企业级表格样式 */
      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .data-table th,
      .data-table td {
        padding: 12px 15px;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
      }

      .data-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid #e9ecef;
      }

      .data-table tbody tr:hover {
        background-color: #f8f9fa;
      }

      .data-table tbody tr:last-child td {
        border-bottom: none;
      }

      .table-container {
        margin: 20px 0;
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }

      .table-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #007bff;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      /* 表格标题左侧内容 */
      .table-title-content {
        display: flex;
        align-items: center;
      }

      /* 图表分析按钮样式*/
      .chart-analysis-btn {
        background: #007bff;
        border: 1px solid #0056b3;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 4px;
        text-decoration: none;
      }

      .chart-analysis-btn:hover {
        background: #0056b3;
        border-color: #004085;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        color: white;
        text-decoration: none;
      }

      .chart-analysis-btn:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 123, 255, 0.3);
      }

      /* 工具按钮样式 */
      .btn-hidden {
        display: none;
      }

      /* 空数据样式 */
      .empty-data {
        text-align: center;
        color: #999;
      }

      /* 粗体文本 */
      .font-bold {
        font-weight: bold;
      }

      /* 企业级表格样式 */
      .enterprise-table {
        border: 1px solid #e7eaec;
        border-collapse: collapse;
        width: 100%;
        background: #ffffff;
        font-size: 14px;
        margin-bottom: 20px;
      }

      .enterprise-table thead th {
        background-color: #f5f5f6;
        border: 1px solid #e7e7e7;
        padding: 12px 8px;
        font-weight: 600;
        color: #333;
        text-align: center;
        vertical-align: middle;
        font-size: 14px;
      }

      .enterprise-table tbody td {
        border: 1px solid #e7eaec;
        padding: 10px 8px;
        vertical-align: middle;
        color: #676a6c;
        text-align: center;
        font-size: 14px;
      }

      .enterprise-table tbody tr:hover {
        background-color: #f9f9f9;
        transition: background-color 0.2s ease;
      }

      /* 表格第一列（机构名称）左对齐 */
      .enterprise-table tbody td:first-child,
      .enterprise-table thead th:first-child {
        text-align: center;
      }

      /* 数值列右对齐 */
      .enterprise-table .number-cell {
        text-align: right;
        font-family: 'Courier New', monospace;
      }

      /* 百分比列居中对齐 */
      .enterprise-table .percentage-cell {
        text-align: center;
        color: #1ab394;
        font-weight: 500;
      }

      /* 响应式表格包装器 */
      .table-wrapper {
        -webkit-overflow-scrolling: touch;
        border: 1px solid #e7eaec;
        border-radius: 4px;
        margin-bottom: 20px;
        position: relative;
        background: #ffffff;
        min-height: 200px; /* 确保有足够的高度显示加载状态 */
      }

      /* 表格容器内的表格去除外边框，避免双重边框 */
      .table-wrapper .enterprise-table {
        border: none;
        margin-bottom: 0;
      }

      /* 移动设备优化 */
      @media (max-width: 768px) {
        .table-wrapper {
          margin: 0 -15px 20px -15px;
          border-radius: 0;
          border-left: none;
          border-right: none;
        }

        .container-fluid {
          padding: 16px 15px;
        }
      }

      /* 平板设备优化 */
      @media (max-width: 992px) and (min-width: 769px) {
        .table-wrapper {
          margin: 0 -10px 20px -10px;
        }
      }

      /* 确保表格在小屏幕上的最小宽度 */
      @media (max-width: 768px) {
        .enterprise-table {
          min-width: 600px;
        }

        .enterprise-table th,
        .enterprise-table td {
          white-space: nowrap;
          min-width: 80px;
        }

        .enterprise-table th:first-child,
        .enterprise-table td:first-child {
          min-width: 120px;
          position: sticky;
          left: 0;
          background: inherit;
          z-index: 1;
        }

        .enterprise-table thead th:first-child {
          background: #f5f5f6;
        }
      }

      /* 加载状态样式 */
      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        display: none;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        border-radius: 4px;
        min-height: 200px; /* 确保有足够的高度进行居中 */
      }

      .loading-overlay.show {
        display: flex;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        background-color: #007bff;
        border-radius: 100%;
        animation: sk-pulseScaleOut 1s infinite ease-in-out;
        margin-bottom: 15px; /* 与文字的间距 */
        flex-shrink: 0; /* 防止压缩 */
      }

      @keyframes sk-pulseScaleOut {
        0% {
          transform: scale(0);
          opacity: 1;
        }
        100% {
          transform: scale(1);
          opacity: 0;
        }
      }

      .loading-text {
        color: #666;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        white-space: nowrap; /* 防止文字换行 */
        margin: 0; /* 移除默认边距 */
      }

      /* 空数据状态样式 */
      .empty-data {
        text-align: center;
        color: #999;
        padding: 40px 20px;
        font-size: 14px;
        background: #fafafa;
        border-radius: 4px;
        margin: 10px;
      }

      .empty-data-icon {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 15px;
        display: block;
      }

      .empty-data-message {
        color: #666;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .empty-data-description {
        color: #999;
        font-size: 13px;
        line-height: 1.5;
      }

      /* 错误状态样式 */
      .error-state {
        text-align: center;
        padding: 40px 20px;
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 4px;
        margin: 10px;
      }

      .error-state-icon {
        font-size: 48px;
        color: #e53e3e;
        margin-bottom: 15px;
        display: block;
      }

      .error-state-message {
        color: #e53e3e;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .error-state-description {
        color: #a0a0a0;
        font-size: 13px;
        margin-bottom: 15px;
      }

      .retry-button {
        background: #1ab394;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        transition: background-color 0.2s ease;
      }

      .retry-button:hover {
        background: #17a085;
      }

      /* 错误状态样式 */
      .error-state-suggestion {
        color: #666;
        font-size: 12px;
        margin-top: 8px;
        margin-bottom: 15px;
        line-height: 1.4;
      }

      .error-state.network-error {
        background: #fff8f0;
        border-color: #ffa940;
      }

      .error-state.timeout-error {
        background: #f6ffed;
        border-color: #52c41a;
      }

      .error-state.permission-error {
        background: #fff2f0;
        border-color: #ff4d4f;
      }

      .error-state.server-error {
        background: #f0f0f0;
        border-color: #8c8c8c;
      }

      /* 错误恢复状态指示器 */
      .error-recovery-indicator {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #1ab394;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        display: none;
      }

      .error-recovery-indicator.show {
        display: block;
      }

      /* 网络状态指示器 */
      .network-status {
        position: fixed;
        top: 10px;
        right: 10px;
        background: #ff4d4f;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        display: none;
      }

      .network-status.offline {
        display: block;
      }

      .network-status.online {
        background: #52c41a;
        display: block;
      }

      /* 加载状态样式 */
      .loading-progress {
        width: 200px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        margin-top: 15px;
        overflow: hidden;
      }

      .loading-progress-fill {
        height: 100%;
        background: #1ab394;
        border-radius: 2px;
        transition: width 0.3s ease;
        width: 0%;
      }

      /* 加载动画变体 */
      .loading-spinner.pulse {
        animation: pulse 1.5s infinite ease-in-out;
      }

      .loading-spinner.spin {
        animation: spin 1s infinite linear;
      }

      .loading-spinner.bounce {
        animation: bounce 1.4s infinite ease-in-out;
      }

      .loading-spinner.fade {
        animation: fade 1.2s infinite ease-in-out;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.1);
          opacity: 0.7;
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @keyframes bounce {
        0%,
        20%,
        53%,
        80%,
        100% {
          transform: translate3d(0, 0, 0);
        }
        40%,
        43% {
          transform: translate3d(0, -8px, 0);
        }
        70% {
          transform: translate3d(0, -4px, 0);
        }
        90% {
          transform: translate3d(0, -2px, 0);
        }
      }

      @keyframes fade {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.3;
        }
      }

      /* 加载状态过渡动画 */
      .loading-overlay {
        transition: opacity 0.3s ease, visibility 0.3s ease;
        opacity: 0;
        visibility: hidden;
      }

      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      /* 强制居中样式（确保完美居中）*/
      .loading-overlay {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
      }

      .loading-overlay.show {
        display: flex !important;
      }

      .table-wrapper.loading {
        transition: opacity 0.3s ease;
      }

      /* 表格渲染性能优化 */
      .enterprise-table {
        /* 启用硬件加速 */
        transform: translateZ(0);
        /* 优化重绘性能 */
        will-change: contents;
      }

      .enterprise-table tbody {
        /* 优化大量行的渲染 */
        contain: layout style;
      }

      .enterprise-table td {
        /* 避免不必要的重排 */
        contain: layout;
      }

      /* 大数据集优化 */
      .enterprise-table.large-dataset {
        table-layout: fixed;
      }

      .enterprise-table.large-dataset td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      /* 加载状态响应式优化 */
      @media (max-width: 768px) {
        .loading-text {
          font-size: 12px;
        }

        .loading-spinner {
          width: 30px;
          height: 30px;
        }

        .loading-progress {
          width: 150px;
        }
      }

      /* 状态转换动画 */
      .table-wrapper {
        transition: opacity 0.3s ease;
      }

      .table-wrapper.loading {
        opacity: 0.6;
      }
      .container-fluid {
        padding: 16px 20px;
      }

      /* Tab 标签页样式 */
      .top-nav-bar {
        display: flex;
        background-color: #fff;
        padding: 0 16px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 16px;
        border-radius: 6px 6px 0 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
      .top-nav-bar .nav-item {
        padding: 12px 16px;
        margin-right: 16px;
        cursor: pointer;
        color: #6c757d;
        font-size: 14px;
        border-bottom: 2px solid transparent;
        transition: color 0.3s, border-color 0.3s;
      }
      .top-nav-bar .nav-item.active {
        color: #007bff;
        font-weight: 500;
        border-bottom: 2px solid #007bff;
      }
      .top-nav-bar .nav-item:hover {
        color: #007bff;
      }
      .top-nav-bar .nav-item i {
        margin-right: 6px;
      }

      .tab-content-wrapper .tab-pane {
        display: none;
      }
      .tab-content-wrapper .tab-pane.active {
        display: block;
      }
      .tab-content-wrapper {
        background-color: #fff;
        border-radius: 0 0 6px 6px;
        min-height: 600px;
      }

      /* 岗位条线分析样式 */
      .position-timeline-container {
        height: 400px;
        margin-bottom: 20px;
      }

      /* 筛选条件样式 */
      .select-list {
        background: #f8f9fa;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
      }

      .select-list ul.filter-row {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        gap: 20px;
      }

      .select-list label {
        color: #333;
        margin-right: 8px;
        white-space: nowrap;
        font-size: 14px;
      }

      .select-list .select-selectpicker {
        display: flex;
        align-items: center;
        min-width: 220px;
      }

      .select-list .year-input {
        display: flex;
        align-items: center;
        white-space: nowrap;
      }

      .select-list .query-button {
        display: flex;
        align-items: center;
        margin-left: 10px;
      }

      .select-list .selectpicker {
        min-width: 200px;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      }

      .select-list .selectpicker:focus {
        border-color: #007bff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      .select-list input[type="number"] {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
        width: 120px;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      }

      .select-list input[type="number"]:focus {
        border-color: #007bff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      /* 查询按钮样式 */
      .query-btn {
        background: #007bff;
        border: 1px solid #0056b3;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 4px;
        text-decoration: none;
        white-space: nowrap;
      }

      .query-btn:hover {
        background: #0056b3;
        border-color: #004085;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        color: white;
        text-decoration: none;
      }

      .query-btn:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 123, 255, 0.3);
      }

      /* 响应式优化 */
      @media (max-width: 768px) {
        .select-list ul.filter-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 15px;
        }

        .select-list .select-selectpicker {
          width: 100%;
          min-width: unset;
        }

        .select-list .selectpicker {
          width: 100%;
          min-width: unset;
        }

        .select-list .year-input {
          width: 100%;
        }

        .select-list input[type="number"] {
          width: 150px;
        }

        .select-list .query-button {
          width: 100%;
          margin-left: 0;
        }

        .query-btn {
          width: 100%;
          justify-content: center;
        }
      }

      .charts-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px; /* 图表之间的间距 */
        justify-content: center; /* 水平居中 */
      }
      .chart-item {
        width: calc(50% - 20px); /* 每个图表占据容器宽度的一半，减去间距 */
        min-width: 300px; /* 最小宽度，防止过小 */
        height: 350px; /* 固定高度 */
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        box-sizing: border-box; /* 包含 padding 和 border 在宽度内 */
        padding: 15px; /* 内部留白 */
        display: flex; /* 确保 ECharts 容器能撑满 */
        align-items: center;
        justify-content: center;
        overflow: hidden; /* 防止内容溢出 */
      }
      /* 响应式调整：小屏幕下每个图表占据整行 */
      @media (max-width: 768px) {
        .chart-item {
          width: 100%;
          min-width: unset; /* 取消最小宽度限制 */
        }
      }

      /* 图表容器样式 */
      .chart-container {
        width: 100%;
        height: 400px;
        margin-top: 20px;
        border: 1px solid #e7eaec;
        border-radius: 4px;
        background: #fff;
        display: none;
      }

      /* 图表视图状态下的样式调整 */
      .table-container.chart-view .chart-container {
        display: block;
      }

      .table-container.chart-view .table-wrapper {
        display: none;
      }

      /* 按钮状态切换动画 */
      .chart-analysis-btn {
        transition: all 0.3s ease;
      }

      .chart-analysis-btn i {
        transition: transform 0.3s ease;
      }

      .table-container.chart-view .chart-analysis-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
      }

      .table-container.chart-view .chart-analysis-btn:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
      }

      /* 选中状态提示 */
      .dept-selection-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px 12px;
        margin: 10px 0;
        font-size: 12px;
        color: #6c757d;
      }

      .dept-selection-info .selected-count {
        color: #007bff;
        font-weight: bold;
      }

      .link-text {
        cursor: pointer;
        color: #007bff !important;
        text-decoration: underline !important;
      }
    </style>
  </head>
  <body class="white-bg container-fluid">
    <div class="ui-layout-west">
      <div class="box box-main">
        <div class="box-header">
          <div class="box-title"><i class="fa fa-sitemap"></i> 组织机构</div>
          <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool btn-hidden" id="btnExpand" title="展开"><i class="fa fa-chevron-up"></i></button>
            <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
            <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
          </div>
        </div>
        <div class="ui-layout-content">
          <input name="treeId" type="hidden" id="treeId" th:value="${deptId}" />
          <input name="deptId" type="hidden" id="deptId" th:value="${deptId}" />

          <!-- 选中状态提示 -->
          <div class="dept-selection-info" id="deptSelectionInfo" style="display: none">
            已选择 <span class="selected-count" id="selectedCount">0</span> 个部门
          </div>

          <div id="tree" class="ztree"></div>
        </div>
      </div>
    </div>
    <div class="ui-layout-center">
      <div class="container-div">
        <!-- Tab 导航栏 -->
        <div class="top-nav-bar">
          <div class="nav-item active" data-target="#tab-personnel-analysis"><i class="fa fa-users"></i> 人员结构分析</div>
          <div class="nav-item" data-target="#tab-organization-analysis"><i class="fa fa-sitemap"></i> 组织层面分析</div>
          <div class="nav-item" data-target="#tab-position-line"><i class="fa fa-line-chart"></i> 岗位条线分析</div>
          <div class="nav-item" data-target="#tab-adjustment-analysis"><i class="fa fa-exchange"></i> 干部调整情况分析</div>
        </div>

        <!-- Tab 内容区域 -->
        <div class="tab-content-wrapper">
          <!-- 人员结构分析标签页 -->
          <div class="tab-pane active" id="tab-personnel-analysis">
            <!-- 年龄分布表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-birthday-cake"></i> 年龄分布统计</div>
                <button class="chart-analysis-btn" onclick="openChartAnalysis('personnel-structure-age-table')">
                  <i class="fa fa-bar-chart"></i> 图表分析
                </button>
              </div>
              <div class="table-wrapper">
                <div class="loading-overlay" id="personnel-structure-age-loading">
                  <div class="loading-spinner"></div>
                  <div class="loading-text">加载中...</div>
                </div>
                <table class="enterprise-table" id="personnel-structure-age-table">
                  <thead>
                    <tr th:with="type=${@dict.getType('dashborad_age_stage')}">
                      <th>机构名称</th>
                      <th>总人数</th>
                      <th th:each="dict : ${type}" th:text="${dict.dictLabel}"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="8" class="empty-data">
                        <i class="fa fa-table empty-data-icon"></i>
                        <div class="empty-data-message">暂无数据</div>
                        <div class="empty-data-description">当前没有可显示的年龄分布数据</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 性别分布表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-users"></i> 性别分布统计</div>
                <button class="chart-analysis-btn" onclick="openChartAnalysis('personnel-structure-gender-table')">
                  <i class="fa fa-pie-chart"></i> 图表分析
                </button>
              </div>
              <div class="table-wrapper">
                <div class="loading-overlay" id="personnel-structure-gender-loading">
                  <div class="loading-spinner"></div>
                  <div class="loading-text">加载中...</div>
                </div>
                <table class="enterprise-table" id="personnel-structure-gender-table">
                  <thead>
                    <tr th:with="type=${@dict.getType('sys_user_sex')}">
                      <th>机构名称</th>
                      <th>总人数</th>
                      <th th:each="dict : ${type}" th:text="${dict.dictLabel}"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="5" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 全日制教育分布表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-graduation-cap"></i> 全日制教育分布统计</div>
                <button class="chart-analysis-btn" onclick="openChartAnalysis('personnel-structure-fulltime-edu')">
                  <i class="fa fa-line-chart"></i> 图表分析
                </button>
              </div>
              <div class="table-wrapper">
                <table class="enterprise-table" id="personnel-structure-fulltime-edu">
                  <thead>
                    <tr th:with="type=${@dict.getType('dashborad_education_full_time')}">
                      <th>机构名称</th>
                      <th>总人数</th>
                      <th th:each="dict : ${type}" th:text="${dict.dictLabel}"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="7" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 在职教育分布表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-book"></i> 在职教育分布统计</div>
                <button class="chart-analysis-btn" onclick="openChartAnalysis('personnel-structure-inservice-edu')">
                  <i class="fa fa-area-chart"></i> 图表分析
                </button>
              </div>
              <div class="table-wrapper">
                <table class="enterprise-table" id="personnel-structure-inservice-edu">
                  <thead>
                    <tr th:with="type=${@dict.getType('dashborad_education_in_service')}">
                      <th>机构名称</th>
                      <th>总人数</th>
                      <th th:each="dict : ${type}" th:text="${dict.dictLabel}"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="7" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 民族分布表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-globe"></i> 民族分布统计</div>
                <button class="chart-analysis-btn" onclick="openChartAnalysis('personnel-structure-ethnicity-table')">
                  <i class="fa fa-pie-chart"></i> 图表分析
                </button>
              </div>
              <div class="table-wrapper">
                <table class="enterprise-table" id="personnel-structure-ethnicity-table">
                  <thead>
                    <tr></tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="5" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 组织层面分析标签页 -->
          <div class="tab-pane" id="tab-organization-analysis">
            <!-- 人员总体分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-pie-chart"></i> 人员总体分析</div>
                <button class="chart-analysis-btn" onclick="openChartAnalysis('org-level-overall-table')"><i class="fa fa-bar-chart"></i> 图表分析</button>
              </div>
              <div class="table-wrapper">
                <div class="loading-overlay" id="org-level-overall-loading">
                  <div class="loading-spinner"></div>
                  <div class="loading-text">加载组织数据...</div>
                </div>
                <table class="enterprise-table" id="org-level-overall-table">
                  <thead>
                    <tr>
                      <th>机构名称</th>
                      <th>总人数</th>
                      <th>中层</th>
                      <th>班子</th>
                      <th>其他人员</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="5" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 性别分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-users"></i> 性别分析</div>
              </div>
              <div id="gender-tables-container">
                <!-- 性别分析表格将在这里动态添加 -->
                <div class="table-wrapper">
                  <table class="enterprise-table" id="org-level-gender-table">
                    <thead>
                      <tr>
                        <th>机构名称</th>
                        <th>层级</th>
                        <th>总人数</th>
                        <th>男性</th>
                        <th>女性</th>
                        <th>男性比例</th>
                        <th>女性比例</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="7" class="empty-data">暂无数据</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- 配备分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-users"></i> 配备分析</div>
              </div>
              <div id="allocation-tables-container">
                <!-- 配备分析表格将在这里动态添加 -->
                <div class="table-wrapper">
                  <table class="enterprise-table" id="org-level-allocation-table">
                    <thead>
                      <tr>
                        <th>机构名称</th>
                        <th>层级</th>
                        <th>应配人数</th>
                        <th>实配人数</th>
                        <th>配备率</th>
                        <th>超配人数</th>
                        <th>缺位人数</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="7" class="empty-data">暂无数据</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 缺位分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-exclamation-triangle"></i> 缺位分析</div>
              </div>
              <div id="omission-tables-container">
                <!-- 缺位分析表格将在这里动态添加 -->
                <div class="table-wrapper">
                  <table class="enterprise-table" id="org-level-omission-table">
                    <thead>
                      <tr>
                        <th>机构名称</th>
                        <th>层级</th>
                        <th>岗位名称</th>
                        <th>应配人数</th>
                        <th>实配人数</th>
                        <th>缺位人数</th>
                        <th>缺位率</th>
                        <th>超配人数</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="6" class="empty-data">暂无数据</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 学历分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-graduation-cap"></i> 学历分析</div>
              </div>
              <div id="edu-tables-container">
                <div class="table-wrapper">
                  <table class="enterprise-table" id="org-level-edu-table">
                    <thead>
                      <tr th:with="type=${@dict.getType('dashborad_education_full_time')}">
                        <th>机构名称</th>
                        <th>层级</th>
                        <th th:each="dict : ${type}" th:text="${dict.dictLabel}"></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="8" class="empty-data">暂无数据</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- 岗位条线分析标签页 -->
          <div class="tab-pane" id="tab-position-line">
            <!-- 岗位条线分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-line-chart"></i> 岗位条线分析</div>
                <!-- <button class="chart-analysis-btn" onclick="openChartAnalysis('position-line-table')"><i class="fa fa-bar-chart"></i> 图表分析</button> -->
              </div>
              <div class="select-list">
                <ul class="filter-row">
                  <li class="select-selectpicker">
                    <label>职务名称：</label>
                    <select title="请选择职务名称" id="positions" class="selectpicker" data-actions-box="true" data-select-all-text="选择所有"
                            data-deselect-all-text="取消选择" data-live-search="true"
                            data-none-selected-text="请选择" multiple>
                        <optgroup th:each="post : ${posts}" th:label="${post.postName}">
                            <option th:each="child : ${post.childs}" th:text="${child.postName}" th:value="${child.personPostId}"></option>
                        </optgroup>
                    </select>
                  </li>
                  <li class="year-input">
                    <label for="positionYear">查询年限：</label>
                    <input id="positionYear" type="number" value="5" min="1" max="20">
                  </li>
                  <li class="query-button">
                    <button class="query-btn" onclick="loadPostLineAnalysisData()">
                      <i class="fa fa-search"></i> 查询
                    </button>
                  </li>
                </ul>
              </div>
              <div class="table-wrapper">
                <table class="enterprise-table" id="position-line-table">
                  <thead>
                    <tr>
                      <th>机构名称</th>
                      <th>岗位名称</th>
                      <th>2021</th>
                      <th>2022</th>
                      <th>2023</th>
                      <th>2024</th>
                      <th>2025</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="7" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 干部调整情况分析标签页 -->
          <div class="tab-pane" id="tab-adjustment-analysis">
            <!-- 已调整情况分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-check-circle text-success"></i> 已调整情况分析</div>
                <!-- <button class="chart-analysis-btn" onclick="openChartAnalysis('position-adjusted-table')"><i class="fa fa-pie-chart"></i> 图表分析</button> -->
              </div>
              <div class="select-list">
                <ul class="filter-row">
                  <li class="select-selectpicker">
                    <label>调整类型：</label>
                    <select title="请选择调整类型" id="positionAdjustedTypes" class="selectpicker" data-actions-box="true" data-select-all-text="选择所有"
                            data-deselect-all-text="取消选择" data-live-search="true"
                            data-none-selected-text="请选择" multiple
                            th:with="type=${@dict.getType('biz_person_position_adjust_tag')}">
                      <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictLabel}"></option>
                    </select>
                  </li>
                  <li class="year-input">
                    <label for="positionAdjustedYear">查询年限：</label>
                    <input id="positionAdjustedYear" type="number" value="5" min="1" max="20">
                  </li>
                  <li class="query-button">
                    <button class="query-btn" onclick="loadPositionAdjustedAnalysisData()">
                      <i class="fa fa-search"></i> 查询
                    </button>
                  </li>
                </ul>
              </div>
              <div class="table-wrapper">
                <table class="enterprise-table" id="position-adjusted-table">
                  <thead>
                    <tr>
                      <th>机构名称</th>
                      <th>调整类型</th>
                      <th>2021</th>
                      <th>2022</th>
                      <th>2023</th>
                      <th>2024</th>
                      <th>2025</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="7" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 需调整情况分析表格 -->
            <div class="table-container">
              <div class="table-title">
                <div class="table-title-content"><i class="fa fa-clock-o text-warning"></i> 需调整情况分析</div>
                <!-- <button class="chart-analysis-btn" onclick="openChartAnalysis('position-proposed-table')"><i class="fa fa-line-chart"></i> 图表分析</button> -->
              </div>
              <div class="select-list">
                <ul class="filter-row">
                  <li class="select-selectpicker">
                    <label>调整类型：</label>
                    <select title="请选择调整类型" id="positionProposedTypes" class="selectpicker" data-actions-box="true" data-select-all-text="选择所有"
                            data-deselect-all-text="取消选择" data-live-search="true"
                            data-none-selected-text="请选择" multiple
                            th:with="type=${@dict.getType('biz_person_position_adjust_proposed_tag')}">
                      <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictLabel}"></option>
                    </select>
                  </li>
                  <li class="year-input">
                    <label for="positionProposedYear">查询年限：</label>
                    <input id="positionProposedYear" type="number" value="5" min="1" max="20">
                  </li>
                  <li class="query-button">
                    <button class="query-btn" onclick="loadPositionProposedAnalysisData()">
                      <i class="fa fa-search"></i> 查询
                    </button>
                  </li>
                </ul>
              </div>
              <div class="table-wrapper">
                <table class="enterprise-table" id="position-proposed-table">
                  <thead>
                    <tr>
                      <th>机构名称</th>
                      <th>调整类型</th>
                      <th>2021</th>
                      <th>2022</th>
                      <th>2023</th>
                      <th>2024</th>
                      <th>2025</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="7" class="empty-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <th:block th:include="include :: bootstrap-select-js" />
    <th:block th:include="include :: echarts-js" />
    <script th:src="@{/js/lodash.min.js}"></script>
    <script th:src="@{/js/moment.min.js}"></script>
    <script th:inline="javascript">
      /*<![CDATA[*/
      var ctx = /*[[@{/}]]*/ '';
      const ageDict = /*[[${@dict.getType('dashborad_age_stage')}]]*/ [];
      const ageValueDict = {};
      ageDict.forEach(item => {
        ageValueDict[item.dictLabel] = item.dictValue;
      });
      /*]]>*/

      const enableDataLink = true;

      // 加载状态配置
      var LoadingConfig = {
        defaultTimeout: 30000, // 30秒默认超时
        animationDuration: 300, // 动画持续时间
        minDisplayTime: 500, // 最小显示时间，避免闪烁
        messages: {
          loading: '加载中...',
          loadingData: '正在获取数据...',
          processing: '正在处理...',
          saving: '正在保存...',
          updating: '正在更新...',
          deleting: '正在删除...',
        },
      };

      // 加载状态跟踪
      var loadingStates = {};

      // 显示加载状态（增强版）
      function showLoadingState(containerId, options) {
        options = options || {};

        try {
          var loadingId = containerId.replace('-table', '');
          var loadingOverlay = document.getElementById(loadingId + '-loading');

          if (!loadingOverlay) {
            // 如果没有加载遮罩，创建一个
            loadingOverlay = createLoadingOverlay(loadingId, options);
          }

          if (!loadingOverlay) {
            return;
          }

          // 更新加载消息
          var loadingText = loadingOverlay.querySelector('.loading-text');
          if (loadingText && options.message) {
            loadingText.textContent = options.message;
          }

          // 记录加载开始时间
          var startTime = Date.now();
          loadingStates[containerId] = {
            startTime: startTime,
            timeout: options.timeout || LoadingConfig.defaultTimeout,
            message: options.message || LoadingConfig.messages.loading,
          };

          // 显示加载遮罩
          loadingOverlay.classList.add('show');

          // 添加表格加载样式
          var tableWrapper = loadingOverlay.parentElement;
          if (tableWrapper && tableWrapper.classList.contains('table-wrapper')) {
            tableWrapper.classList.add('loading');
          }

          // 设置超时处理
          if (options.timeout !== false) {
            setTimeout(function () {
              if (loadingStates[containerId] && loadingStates[containerId].startTime === startTime) {
                hideLoadingState(containerId);
                displayErrorMessage(containerId, '加载超时', '请求处理时间过长，请稍后重试');
              }
            }, options.timeout || LoadingConfig.defaultTimeout);
          }

          // 触发加载开始事件
          triggerLoadingEvent(containerId, 'loadingStart', { startTime: startTime });
        } catch (error) {}
      }

      // 隐藏加载状态（增强版）
      function hideLoadingState(containerId, options) {
        options = options || {};

        try {
          var loadingId = containerId.replace('-table', '');
          var loadingOverlay = document.getElementById(loadingId + '-loading');

          if (!loadingOverlay) {
            return;
          }

          var loadingState = loadingStates[containerId];
          var currentTime = Date.now();

          // 计算加载持续时间
          var duration = loadingState ? currentTime - loadingState.startTime : 0;

          // 确保最小显示时间，避免闪烁
          var minDisplayTime = options.minDisplayTime || LoadingConfig.minDisplayTime;
          var remainingTime = Math.max(0, minDisplayTime - duration);

          var hideFunction = function () {
            // 隐藏加载遮罩
            loadingOverlay.classList.remove('show');

            // 移除表格加载样式
            var tableWrapper = loadingOverlay.parentElement;
            if (tableWrapper && tableWrapper.classList.contains('table-wrapper')) {
              tableWrapper.classList.remove('loading');
            }

            // 清除加载状态记录
            delete loadingStates[containerId];

            // 触发加载完成事件
            triggerLoadingEvent(containerId, 'loadingEnd', {
              duration: duration,
              success: options.success !== false,
            });
          };

          if (remainingTime > 0) {
            setTimeout(hideFunction, remainingTime);
          } else {
            hideFunction();
          }
        } catch (error) {
          console.error('隐藏加载状态失败:', error);
        }
      }

      // 创建加载遮罩
      function createLoadingOverlay(containerId, options) {
        options = options || {};

        try {
          // 查找表格包装器
          var container = document.getElementById(containerId + '-table');
          if (!container) {
            container = document.getElementById(containerId);
          }

          var tableWrapper = container ? container.closest('.table-wrapper') : null;
          if (!tableWrapper) {
            return null;
          }

          // 创建加载遮罩元素
          var loadingOverlay = document.createElement('div');
          loadingOverlay.className = 'loading-overlay';
          loadingOverlay.id = containerId + '-loading';

          // 创建加载动画
          var loadingSpinner = document.createElement('div');
          loadingSpinner.className = 'loading-spinner';

          // 创建加载文本
          var loadingText = document.createElement('div');
          loadingText.className = 'loading-text';
          loadingText.textContent = options.message || LoadingConfig.messages.loading;

          // 组装加载遮罩
          loadingOverlay.appendChild(loadingSpinner);
          loadingOverlay.appendChild(loadingText);

          // 插入到表格包装器中
          tableWrapper.insertBefore(loadingOverlay, tableWrapper.firstChild);

          return loadingOverlay;
        } catch (error) {
          console.error('创建加载遮罩失败:', error);
          return null;
        }
      }

      // 加载事件触发器
      function triggerLoadingEvent(containerId, eventType, data) {
        try {
          var event = new CustomEvent('tableLoading', {
            detail: {
              containerId: containerId,
              eventType: eventType,
              data: data,
              timestamp: Date.now(),
            },
          });

          document.dispatchEvent(event);
        } catch (error) {
          console.warn('触发加载事件失败:', error);
        }
      }

      // 批量加载状态管理
      function showMultipleLoadingStates(containerIds, options) {
        options = options || {};

        containerIds.forEach(function (containerId) {
          showLoadingState(containerId, options);
        });
      }

      function hideMultipleLoadingStates(containerIds, options) {
        options = options || {};

        containerIds.forEach(function (containerId) {
          hideLoadingState(containerId, options);
        });
      }

      // 加载状态查询
      function isLoading(containerId) {
        return containerId in loadingStates;
      }

      function getLoadingDuration(containerId) {
        var loadingState = loadingStates[containerId];
        if (!loadingState) return 0;

        return Date.now() - loadingState.startTime;
      }

      function getAllLoadingStates() {
        var states = {};
        for (var containerId in loadingStates) {
          states[containerId] = {
            duration: getLoadingDuration(containerId),
            message: loadingStates[containerId].message,
            startTime: loadingStates[containerId].startTime,
          };
        }
        return states;
      }

      // 加载进度管理
      function updateLoadingProgress(containerId, progress, message) {
        try {
          var loadingId = containerId.replace('-table', '');
          var loadingOverlay = document.getElementById(loadingId + '-loading');

          if (!loadingOverlay || !loadingOverlay.classList.contains('show')) {
            return;
          }

          // 更新进度条（如果存在）
          var progressBar = loadingOverlay.querySelector('.loading-progress');
          if (!progressBar && progress !== undefined) {
            // 创建进度条
            progressBar = document.createElement('div');
            progressBar.className = 'loading-progress';

            var progressFill = document.createElement('div');
            progressFill.className = 'loading-progress-fill';
            progressBar.appendChild(progressFill);

            loadingOverlay.appendChild(progressBar);
          }

          if (progressBar && progress !== undefined) {
            var progressFill = progressBar.querySelector('.loading-progress-fill');
            if (progressFill) {
              progressFill.style.width = Math.max(0, Math.min(100, progress)) + '%';
            }
          }

          // 更新消息
          if (message) {
            var loadingText = loadingOverlay.querySelector('.loading-text');
            if (loadingText) {
              loadingText.textContent = message;
            }
          }
        } catch (error) {
          console.error('更新加载进度失败:', error);
        }
      }

      // 加载状态动画控制
      function setLoadingAnimation(containerId, animationType) {
        try {
          var loadingId = containerId.replace('-table', '');
          var loadingSpinner = document.querySelector('#' + loadingId + '-loading .loading-spinner');

          if (!loadingSpinner) return;

          // 移除现有动画类
          loadingSpinner.classList.remove('pulse', 'spin', 'bounce', 'fade');

          // 添加新动画类
          switch (animationType) {
            case 'pulse':
              loadingSpinner.classList.add('pulse');
              break;
            case 'spin':
              loadingSpinner.classList.add('spin');
              break;
            case 'bounce':
              loadingSpinner.classList.add('bounce');
              break;
            case 'fade':
              loadingSpinner.classList.add('fade');
              break;
            default:
              // 默认使用脉冲动画
              loadingSpinner.classList.add('pulse');
          }
        } catch (error) {
          console.error('设置加载动画失败:', error);
        }
      }

      // 加载状态清理
      function clearAllLoadingStates() {
        var containerIds = Object.keys(loadingStates);
        containerIds.forEach(function (containerId) {
          hideLoadingState(containerId, { minDisplayTime: 0 });
        });
      }

      // 加载状态监听器
      function addLoadingStateListener(callback) {
        document.addEventListener('tableLoading', callback);
      }

      function removeLoadingStateListener(callback) {
        document.removeEventListener('tableLoading', callback);
      }

      // 初始化加载状态管理系统
      function initLoadingStateManagement() {
        // 添加页面卸载时的清理
        window.addEventListener('beforeunload', function () {
          clearAllLoadingStates();
        });

        // 添加加载状态事件监听器
        addLoadingStateListener(function (event) {
          var detail = event.detail;

          // 根据事件类型执行相应操作
          switch (detail.eventType) {
            case 'loadingStart':
              // 设置加载动画
              setLoadingAnimation(detail.containerId, 'pulse');
              break;

            case 'loadingEnd':
              // 记录加载性能
              if (detail.data.duration > 2000) {
                console.warn('加载时间较长:', detail.containerId, detail.data.duration + 'ms');
              }
              break;
          }
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function (event) {
          // Ctrl+Shift+L: 显示所有加载状态
          if (event.ctrlKey && event.shiftKey && event.key === 'L') {
            event.preventDefault();
          }

          // Ctrl+Shift+C: 清除所有加载状态
          if (event.ctrlKey && event.shiftKey && event.key === 'C') {
            event.preventDefault();
            clearAllLoadingStates();
          }
        });
      }

      /**
       * 图表分析功能
       * 为每个表格提供图表分析入口，实现表格和图表视图切换
       */
      function openChartAnalysis(tableId) {
        // 获取表格数据
        var data = tableData[tableId];
        if (!data || data.length === 0) {
          alert('当前表格暂无数据，请先加载数据后再进行图表分析');
          return;
        }

        // 获取表格容器和按钮
        var tableElement = document.getElementById(tableId);
        if (!tableElement) {
          return;
        }

        var tableContainer = tableElement.closest('.table-container');
        if (!tableContainer) {
          return;
        }

        var button = tableContainer.querySelector('.chart-analysis-btn');
        if (!button) {
          return;
        }

        // 检查当前状态
        var isChartView = tableContainer.classList.contains('chart-view');

        if (isChartView) {
          // 切换回表格视图
          switchToTableView(tableContainer, button);
        } else {
          // 切换到图表视图
          switchToChartView(tableContainer, button, tableId, data);
        }
      }

      /**
       * 切换到图表视图
       */
      function switchToChartView(tableContainer, button, tableId, data) {
        // 隐藏表格
        var tableWrapper = tableContainer.querySelector('.table-wrapper');
        if (tableWrapper) {
          tableWrapper.style.display = 'none';
        }

        // 创建或显示图表容器
        var chartContainer = tableContainer.querySelector('.chart-container');
        if (!chartContainer) {
          chartContainer = document.createElement('div');
          chartContainer.className = 'chart-container';
          chartContainer.style.width = '100%';
          chartContainer.style.height = '400px';
          chartContainer.style.marginTop = '20px';
          tableContainer.appendChild(chartContainer);
        }
        chartContainer.style.display = 'block';

        // 更新按钮状态
        button.innerHTML = '<i class="fa fa-table"></i> 表格分析';
        tableContainer.classList.add('chart-view');

        // 渲染图表
        renderChart(chartContainer, tableId, data);
      }

      /**
       * 切换到表格视图
       */
      function switchToTableView(tableContainer, button) {
        // 显示表格
        var tableWrapper = tableContainer.querySelector('.table-wrapper');
        if (tableWrapper) {
          tableWrapper.style.display = 'block';
        }

        // 隐藏图表
        var chartContainer = tableContainer.querySelector('.chart-container');
        if (chartContainer) {
          chartContainer.style.display = 'none';
        }

        // 更新按钮状态
        button.innerHTML = '<i class="fa fa-bar-chart"></i> 图表分析';
        tableContainer.classList.remove('chart-view');
      }

      /**
       * 获取图表配置（根据表格类型）
       */
      function getChartConfig(tableId) {
        var configs = {
          'personnel-structure-age-table': {
            type: '柱状图',
            title: '年龄分布图表',
            xAxis: '年龄段',
            yAxis: '人数',
            chartType: 'bar',
          },
          'personnel-structure-gender-table': {
            type: '饼图',
            title: '性别分布图表',
            chartType: 'pie',
          },
          'personnel-structure-fulltime-edu': {
            type: '堆叠柱状图',
            title: '全日制教育分布图表',
            xAxis: '机构',
            yAxis: '人数',
            chartType: 'stackedBar',
          },
          'personnel-structure-inservice-edu': {
            type: '面积图',
            title: '在职教育分布图表',
            xAxis: '机构',
            yAxis: '人数',
            chartType: 'area',
          },
          'personnel-structure-ethnicity-table': {
            type: '饼图',
            title: '民族分布图表',
            chartType: 'pie',
          },
          'org-level-overall-table': {
            type: '柱状图',
            title: '人员总体分析图表',
            xAxis: '层级',
            yAxis: '人数',
            chartType: 'bar',
          },
          'org-level-gender-table': {
            type: '分组柱状图',
            title: '性别分析图表',
            xAxis: '层级',
            yAxis: '人数',
            chartType: 'groupedBar',
          },
          'org-level-allocation-table': {
            type: '对比柱状图',
            title: '配备分析图表',
            xAxis: '层级',
            yAxis: '人数',
            chartType: 'comparisonBar',
          },
          'org-level-omission-table': {
            type: '折线图',
            title: '缺位分析图表',
            xAxis: '层级',
            yAxis: '缺位率',
            chartType: 'line',
          },
          'org-level-edu-table': {
            type: '堆叠面积图',
            title: '学历分析图表',
            xAxis: '层级',
            yAxis: '人数',
            chartType: 'stackedArea',
          },
          'position-line-table': {
            type: '柱状图',
            title: '岗位条线分析图表',
            xAxis: '条线',
            yAxis: '配备率',
            chartType: 'bar',
          },
          'position-adjusted-table': {
            type: '时间线图',
            title: '已调整情况图表',
            xAxis: '时间',
            yAxis: '调整数量',
            chartType: 'timeline',
          },
          'position-proposed-table': {
            type: '甘特图',
            title: '待调整情况图表',
            xAxis: '时间',
            yAxis: '调整计划',
            chartType: 'gantt',
          },
        };

        return (
          configs[tableId] || {
            type: '柱状图',
            title: '数据图表',
            chartType: 'bar',
          }
        );
      }

      /**
       * 渲染图表
       */
      function renderChart(container, tableId, data) {
        try {
          // 获取图表配置
          var chartConfig = getChartConfig(tableId);

          // 初始化ECharts实例
          var chart = echarts.getInstanceByDom(container);
          if (!chart) {
            chart = echarts.init(container);
          }

          // 根据表格类型生成图表选项
          var option = generateChartOption(tableId, data, chartConfig);

          // 设置图表选项
          chart.setOption(option);

          // 响应式调整
          window.addEventListener('resize', function () {
            chart.resize();
          });
        } catch (error) {
          console.error('图表渲染失败:', error);
          container.innerHTML = '<div style="text-align: center; padding: 50px; color: #999;">图表渲染失败</div>';
        }
      }

      /**
       * 生成图表选项
       */
      function generateChartOption(tableId, data, chartConfig) {
        switch (tableId) {
          case 'personnel-structure-age-table':
            return generateAgeDistributionChart(data);
          case 'personnel-structure-gender-table':
            return generateGenderDistributionChart(data);
          case 'personnel-structure-fulltime-edu':
          case 'personnel-structure-inservice-edu':
            return generateEducationChart(data, chartConfig.title);
          case 'personnel-structure-ethnicity-table':
            return generateEthnicityChart(data);

          case 'org-level-overall-table':
            return generateOverallAnalysisChart(data);

          case 'position-line-table':
            return generatePositionLineChart(data);

          case 'position-adjusted-table':
            return generatePositionAdjustedChart(data);

          case 'position-proposed-table':
            return generatePositionAdjustProposedChart(data);

          default:
            return generateDefaultBarChart(data, chartConfig);
        }
      }

      /**
       * 生成年龄分布图表
       */
      function generateAgeDistributionChart(data) {
        // 提取机构名称
        const organizations = data.map(function (item) {
          return item.dept_name || '未知机构';
        });

        // 年龄段
        const ageBrackets = ageDict.map(function (item) {
          return item.dictLabel;
        });

        const series = ageBrackets.map(function (bracket) {
          return {
            name: bracket,
            type: 'bar',
            stack: 'Total',
            emphasis: { focus: 'series' },
            data: data.map(function (item) {
              if (!item.dynamicColumn) {
                return 0;
              }
              return item.dynamicColumn[bracket] || 0;
            }),
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.value > 5 ? params.value : '';
              },
            },
          };
        });

        return {
          title: { text: '年龄分布统计', left: 'center' },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              let tooltipStr = `${params[0].name}<br/>`;
              let total = 0;
              params.forEach(param => {
                if (param.value > 0) {
                  tooltipStr += `${param.marker}${param.seriesName}: ${param.value} 人<br/>`;
                }
                total += param.value;
              });
              tooltipStr += `<strong>总计: ${total} 人</strong>`;
              return tooltipStr;
            },
          },
          legend: { data: ageBrackets, top: 'bottom', type: 'scroll' },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: { type: 'category', data: organizations },
          yAxis: { type: 'value', name: '人数 (人)' },
          series: series,
        };
      }

      /**
       * 生成性别分布图表
       */
      function generateGenderDistributionChart(data) {
        // 提取机构名称
        const organizations = data.map(function (item) {
          return item.dept_name || '未知机构';
        });

        /*<![CDATA[*/
        const genderDict = /*[[${@dict.getType('sys_user_sex')}]]*/ [];
        /*]]>*/

        // 性别
        const genderBrackets = genderDict.map(function (item) {
          return item.dictLabel;
        });

        // 生成系列数据
        var series = genderBrackets.map(function (bracket) {
          return {
            name: bracket,
            type: 'bar',
            stack: 'Total',
            emphasis: { focus: 'series' },
            data: data.map(function (item) {
              if (!item.dynamicColumn) {
                return 0;
              }
              return item.dynamicColumn[bracket] || 0;
            }),
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.value > 5 ? params.value : '';
              },
            },
          };
        });

        return {
          title: { text: '性别分布统计', left: 'center' },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              let tooltipStr = `${params[0].name}<br/>`;
              let total = 0;
              params.forEach(param => {
                if (param.value > 0) {
                  tooltipStr += `${param.marker}${param.seriesName}: ${param.value} 人<br/>`;
                }
                total += param.value;
              });
              tooltipStr += `<strong>总计: ${total} 人</strong>`;
              return tooltipStr;
            },
          },
          legend: { data: genderBrackets, top: 'bottom', type: 'scroll' },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: { type: 'category', data: organizations },
          yAxis: { type: 'value', name: '人数 (人)' },
          series: series,
        };
      }

      /**
       * 生成教育分布图表
       */
      function generateEducationChart(data, title) {
        const organizations = data.map(function (item) {
          return item.dept_name || '未知机构';
        });

        /*<![CDATA[*/
        const eduDict = /*[[${@dict.getType('dashborad_education_full_time')}]]*/ [];
        /*]]>*/

        const eduBrackets = eduDict.map(function (item) {
          return item.dictLabel;
        });

        // 生成系列数据
        const series = eduBrackets.map(function (bracket) {
          return {
            name: bracket,
            type: 'bar',
            stack: 'Total',
            emphasis: { focus: 'series' },
            data: data.map(function (item) {
              if (!item.dynamicColumn) {
                return 0;
              }
              return item.dynamicColumn[bracket] || 0;
            }),
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.value > 5 ? params.value : '';
              },
            },
          };
        });

        return {
          title: { text: '学历分布统计', left: 'center' },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              let tooltipStr = `${params[0].name}<br/>`;
              let total = 0;
              params.forEach(param => {
                if (param.value > 0) {
                  tooltipStr += `${param.marker}${param.seriesName}: ${param.value} 人<br/>`;
                }
                total += param.value;
              });
              tooltipStr += `<strong>总计: ${total} 人</strong>`;
              return tooltipStr;
            },
          },
          legend: { data: eduBrackets, top: 'bottom', type: 'scroll' },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: { type: 'category', data: organizations },
          yAxis: { type: 'value', name: '人数 (人)' },
          series: series,
        };
      }

      /**
       * 生成民族分布图表
       */
      function generateEthnicityChart(data) {
        if (!data || data.length === 0) {
          return {
            title: { text: '民族分布统计', left: 'center' },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
            legend: { data: [], top: 'bottom', type: 'scroll' },
            grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
            xAxis: { type: 'category', data: [] },
            yAxis: { type: 'value', name: '人数 (人)' },
            series: [],
          };
        }

        // 提取机构名称
        const organizations = data.map(function (item) {
          return item.dept_name || '未知机构';
        });

        // 民族
        let nationBrackets = Object.keys(data[0]?.dynamicColumn || {});

        // 生成系列数据
        const series = nationBrackets.map(function (bracket) {
          return {
            name: bracket,
            type: 'bar',
            stack: 'Total',
            emphasis: { focus: 'series' },
            data: data.map(function (item) {
              if (!item.dynamicColumn) {
                return 0;
              }
              return item.dynamicColumn[bracket] || 0;
            }),
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.value > 5 ? params.value : '';
              },
            },
          };
        });

        return {
          title: { text: '民族分布统计', left: 'center' },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              let tooltipStr = `${params[0].name}<br/>`;
              let total = 0;
              params.forEach(param => {
                if (param.value > 0) {
                  tooltipStr += `${param.marker}${param.seriesName}: ${param.value} 人<br/>`;
                }
                total += param.value;
              });
              tooltipStr += `<strong>总计: ${total} 人</strong>`;
              return tooltipStr;
            },
          },
          legend: { data: nationBrackets, top: 'bottom', type: 'scroll' },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: { type: 'category', data: organizations },
          yAxis: { type: 'value', name: '人数 (人)' },
          series: series,
        };
      }

      /**
       * 生成组织层面总体分析图表
       */
      function generateOverallAnalysisChart(data) {
        if (!data || data.length === 0) {
          return {
            title: { text: '总体分析', left: 'center' },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
            legend: { data: [], top: 'bottom', type: 'scroll' },
            grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
            xAxis: { type: 'category', data: [] },
            yAxis: { type: 'value', name: '人数 (人)' },
            series: [],
          };
        }

        // 提取机构名称
        const organizations = data.map(function (item) {
          return item.dept_name || '未知机构';
        });

        let brackets = Object.keys(data[0]?.dynamicColumn || {});

        const series = brackets.map(function (bracket) {
          return {
            name: bracket,
            type: 'bar',
            stack: 'Total',
            emphasis: { focus: 'series' },
            data: data.map(function (item) {
              if (!item.dynamicColumn) {
                return 0;
              }
              return item.dynamicColumn[bracket] || 0;
            }),
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.value > 5 ? params.value : '';
              },
            },
          };
        });

        return {
          title: { text: '总体分析', left: 'center' },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              let tooltipStr = `${params[0].name}<br/>`;
              let total = 0;
              params.forEach(param => {
                tooltipStr += `${param.marker}${param.seriesName}: ${param.value} 人<br/>`;
                total += param.value;
              });
              tooltipStr += `<strong>总计: ${total} 人</strong>`;
              return tooltipStr;
            },
          },
          legend: { data: brackets, top: 'bottom', type: 'scroll' },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: { type: 'category', data: organizations },
          yAxis: { type: 'value', name: '人数 (人)' },
          series: series,
        };
      }

      /**
       * 生成岗位条线分析图表
       */
      function generatePositionLineChart(data) {
        data = [
          {
            name: '董事长',
            data: [
              ['1998-01-01', '2020-12-31', '王明'],
              ['2021-01-01', '2023-12-31', '张三'],
              ['2024-01-01', '2025-12-31', '李四'],
            ],
          },
          {
            name: '总经理',
            data: [
              ['2000-06-01', '2018-12-31', '赵五'],
              ['2019-01-01', '2022-12-31', '钱六'],
              ['2023-01-01', '2025-12-31', '孙七'],
            ],
          },
          {
            name: '副总经理',
            data: [
              ['2002-03-01', '2019-12-31', '周八'],
              ['2020-01-01', '2024-06-30', '吴九'],
              ['2024-07-01', '2025-12-31', '郑十'],
            ],
          },
        ];

        var timelineData = [
          {
            name: '董事长',
            data: [
              ['1998-01-01', '2020-12-31', '王明'],
              ['2021-01-01', '2023-12-31', '张三'],
              ['2024-01-01', '2025-12-31', '李四'],
            ],
          },
          {
            name: '总经理',
            data: [
              ['2000-06-01', '2018-12-31', '赵五'],
              ['2019-01-01', '2022-12-31', '钱六'],
              ['2023-01-01', '2025-12-31', '孙七'],
            ],
          },
          {
            name: '副总经理',
            data: [
              ['2002-03-01', '2019-12-31', '周八'],
              ['2020-01-01', '2024-06-30', '吴九'],
              ['2024-07-01', '2025-12-31', '郑十'],
            ],
          },
        ];

        // 构建时间轴图表数据
        var categories = timelineData.map(function (item) {
          return item.name;
        });
        var series = [];
        var personColors = {};
        var colorPalette = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];
        var colorIndex = 0;

        timelineData.forEach(function (position, positionIndex) {
          position.data.forEach(function (period, periodIndex) {
            var startDate = new Date(period[0]);
            var endDate = new Date(period[1]);
            var personName = period[2];

            // 为每个人员分配唯一颜色
            if (!personColors[personName]) {
              personColors[personName] = colorPalette[colorIndex % colorPalette.length];
              colorIndex++;
            }

            series.push([positionIndex, startDate.getTime(), endDate.getTime(), endDate.getTime() - startDate.getTime(), personName, personColors[personName]]);
          });
        });

        const option = {
          title: {
            text: '岗位人员变动时间线',
            left: 'left',
          },
          tooltip: {
            formatter: function (params) {
              var start = new Date(params.value[1]);
              var end = new Date(params.value[2]);
              return (
                params.value[4] +
                '<br/>' +
                start.getFullYear() +
                '年' +
                (start.getMonth() + 1) +
                '月 - ' +
                end.getFullYear() +
                '年' +
                (end.getMonth() + 1) +
                '月'
              );
            },
          },
          grid: {
            x: 30,
            x2: 40,
            y2: 24,
            containLabel: true,
          },
          xAxis: {
            type: 'time',
            axisLabel: {
              formatter: function (value) {
                return new Date(value).getFullYear() + '年';
              },
            },
          },
          yAxis: {
            type: 'category',
            data: categories,
          },
          series: [
            {
              type: 'custom',
              renderItem: function (params, api) {
                var categoryIndex = api.value(0);
                var start = api.coord([api.value(1), categoryIndex]);
                var end = api.coord([api.value(2), categoryIndex]);
                var height = api.size([0, 1])[1] * 0.6;
                var personColor = api.value(5); // 使用预分配的颜色
                var personName = api.value(4);

                var rectShape = echarts.graphic.clipRectByRect(
                  {
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  }
                );

                var textX = (start[0] + end[0]) / 2;
                var textY = start[1];
                var textWidth = end[0] - start[0];

                var group = {
                  type: 'group',
                  children: [],
                };

                // 添加矩形
                if (rectShape) {
                  group.children.push({
                    type: 'rect',
                    shape: rectShape,
                    style: {
                      fill: personColor,
                      stroke: '#333',
                      lineWidth: 1,
                      opacity: 0.8,
                    },
                  });

                  // 如果矩形足够宽，添加人员姓名标签
                  if (textWidth > 60) {
                    group.children.push({
                      type: 'text',
                      style: {
                        x: textX,
                        y: textY,
                        text: personName.length > 4 ? personName.substring(0, 4) + '...' : personName,
                        textAlign: 'center',
                        textVerticalAlign: 'middle',
                        fontSize: 12,
                        fontWeight: 'bold',
                        fill: '#fff',
                        stroke: '#000',
                        lineWidth: 1,
                      },
                    });
                  }
                }

                return rectShape ? group : null;
              },
              encode: {
                x: [1, 2],
                y: 0,
              },
              data: series,
            },
          ],
        };

        return option;
      }

      /**
       * 生成岗位调整图表
       */
      function generatePositionAdjustedChart(data) {
        // 提取机构名称
        const organizations = _(data).map(item => {
          return item.dept_name || '未知机构';
        }).uniq().value();

        let brackets = Object.keys(data[0]?.dynamicColumn || {});

        const series = brackets.map(function (bracket) {
          return {
            name: bracket,
            type: 'bar',
            stack: 'Total',
            emphasis: { focus: 'series' },
            data: data.map(function (item) {
              if (!item.dynamicColumn) {
                return 0;
              }
              return item.dynamicColumn[bracket] || 0;
            }),
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.value > 5 ? params.value : '';
              },
            },
          };
        });

        return {
          title: { text: '岗位已调整统计', left: 'center' },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              let tooltipStr = `${params[0].name}<br/>`;
              let total = 0;
              params.forEach(param => {
                if (param.value > 0) {
                  tooltipStr += `${param.marker}${param.seriesName}: ${param.value} 人<br/>`;
                }
                total += param.value;
              });
              tooltipStr += `<strong>总计: ${total} 人</strong>`;
              return tooltipStr;
            },
          },
          legend: { data: brackets, top: 'bottom', type: 'scroll' },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: { type: 'category', data: organizations },
          yAxis: { type: 'value', name: '人数 (人)' },
          series: series,
        };
      }

      /**
       * 生成岗位拟调整图表
       */
       function generatePositionAdjustProposedChart(data) {
        // 提取机构名称
        const organizations = data.map(function (item) {
          return item.dept_name || '未知机构';
        });

        let brackets = Object.keys(data[0]?.dynamicColumn || {});

        const series = brackets.map(function (bracket) {
          return {
            name: bracket,
            type: 'bar',
            stack: 'Total',
            emphasis: { focus: 'series' },
            data: data.map(function (item) {
              if (!item.dynamicColumn) {
                return 0;
              }
              return item.dynamicColumn[bracket] || 0;
            }),
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.value > 5 ? params.value : '';
              },
            },
          };
        });

        return {
          title: { text: '岗位拟调整统计', left: 'center' },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              let tooltipStr = `${params[0].name}<br/>`;
              let total = 0;
              params.forEach(param => {
                if (param.value > 0) {
                  tooltipStr += `${param.marker}${param.seriesName}: ${param.value} 人<br/>`;
                }
                total += param.value;
              });
              tooltipStr += `<strong>总计: ${total} 人</strong>`;
              return tooltipStr;
            },
          },
          legend: { data: brackets, top: 'bottom', type: 'scroll' },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: { type: 'category', data: organizations },
          yAxis: { type: 'value', name: '人数 (人)' },
          series: series,
        };
      }

      /**
       * 生成默认柱状图
       */
      function generateDefaultBarChart(data, chartConfig) {
        var categories = data.map(function (item, index) {
          return item.org || item.orgName || item['机构名称'] || '项目' + (index + 1);
        });

        var values = data.map(function (item) {
          // 尝试获取数值字段
          var numericKeys = Object.keys(item).filter(function (key) {
            return typeof item[key] === 'number' && key !== 'total' && key !== '总人数';
          });

          if (numericKeys.length > 0) {
            return item[numericKeys[0]];
          }
          return item.total || item['总人数'] || 0;
        });

        return {
          title: { text: chartConfig.title, left: 'center' },
          tooltip: { trigger: 'axis' },
          grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
          xAxis: { type: 'category', data: categories },
          yAxis: { type: 'value', name: chartConfig.yAxis || '数值' },
          series: [
            {
              name: chartConfig.title,
              type: 'bar',
              data: values,
              itemStyle: {
                color: '#007bff',
              },
            },
          ],
        };
      }

      // 处理空状态显示
      function handleEmptyState(tableElement, message) {
        if (!tableElement) return;

        var tbody = tableElement.querySelector('tbody');
        if (!tbody) return;

        var colCount = tableElement.querySelectorAll('thead th').length || 1;
        var emptyMessage = message || '暂无数据';

        tbody.innerHTML =
          '<tr><td colspan="' +
          colCount +
          '" class="empty-data">' +
          '<i class="fa fa-table empty-data-icon"></i>' +
          '<div class="empty-data-message">' +
          emptyMessage +
          '</div>' +
          '</td></tr>';
      }

      // 在表格容器内显示错误状态
      function showTableError(tableId, errorMessage, showRetry) {
        var table = document.getElementById(tableId);
        if (!table) return;

        var tbody = table.querySelector('tbody');
        if (!tbody) return;

        var colCount = table.querySelectorAll('thead th').length || 1;
        var retryButton = showRetry !== false ? '<button class="retry-button" onclick="retryLoadData(\'' + tableId + '\')">重试</button>' : '';

        tbody.innerHTML =
          '<tr><td colspan="' +
          colCount +
          '" class="error-state">' +
          '<i class="fa fa-exclamation-triangle error-state-icon"></i>' +
          '<div class="error-state-message">' +
          (errorMessage || '加载失败') +
          '</div>' +
          retryButton +
          '</td></tr>';
      }

      // 简化的重试按钮处理
      function addRetryButton(tableId, customCallback) {
        var table = document.getElementById(tableId);
        if (!table) return;

        var errorCell = table.querySelector('.error-state');
        if (!errorCell) return;

        var existingButton = errorCell.querySelector('.retry-button');
        if (existingButton) return; // 避免重复添加

        var retryButton = document.createElement('button');
        retryButton.className = 'retry-button';
        retryButton.textContent = '重试';
        retryButton.onclick =
          customCallback ||
          function () {
            retryLoadData(tableId);
          };

        errorCell.appendChild(retryButton);
      }

      // 清除表格状态（空状态或错误状态）
      function clearTableState(tableId) {
        var table = document.getElementById(tableId);
        if (!table) return;

        var tbody = table.querySelector('tbody');
        if (tbody) {
          tbody.innerHTML = '';
        }
      }

      // 检查表格是否为空
      function isTableEmpty(tableId) {
        var table = document.getElementById(tableId);
        if (!table) return true;

        var tbody = table.querySelector('tbody');
        if (!tbody) return true;

        var rows = tbody.querySelectorAll('tr');
        return rows.length === 0 || (rows.length === 1 && rows[0].querySelector('.empty-data'));
      }

      // 简化的状态切换函数
      function setTableState(tableId, state, message, options) {
        options = options || {};

        switch (state) {
          case 'loading':
            showLoadingState(tableId, { message: message });
            break;
          case 'empty':
            hideLoadingState(tableId);
            handleEmptyState(document.getElementById(tableId), message);
            break;
          case 'error':
            hideLoadingState(tableId);
            showTableError(tableId, message, options.showRetry);
            break;
          case 'success':
            hideLoadingState(tableId);
            // 数据由外部更新
            break;
          default:
            clearTableState(tableId);
        }
      }

      // 错误处理函数
      function handleApiError(error, containerId, xhr, status) {
        var errorType = detectErrorType(error, xhr, status);
        var errorConfig = ErrorMessages[errorType];

        // 记录错误日志
        logError(error, {
          containerId: containerId,
          errorType: errorType,
          xhr: xhr,
          status: status,
          timestamp: new Date().toISOString(),
        });

        // 显示用户友好的错误消息
        displayErrorMessage(containerId, errorConfig.title, errorConfig.message, errorConfig.suggestion, errorConfig.icon, errorConfig.retryable);

        // 隐藏加载状态
        hideLoadingState(containerId.replace('-table', ''));

        return errorType;
      }

      // 错误消息显示函数
      function displayErrorMessage(containerId, title, message, suggestion, icon, retryable) {
        var tableWrapper = document.querySelector('#' + containerId).closest('.table-wrapper');
        if (!tableWrapper) {
          console.error('找不到表格容器:', containerId);
          return;
        }

        var table = tableWrapper.querySelector('table');
        if (!table) {
          console.error('找不到表格元素:', containerId);
          return;
        }

        var tbody = table.querySelector('tbody');
        if (!tbody) {
          console.error('找不到表格tbody:', containerId);
          return;
        }

        var colCount = table.querySelector('thead tr') ? table.querySelector('thead tr').children.length : 1;

        var errorHtml =
          '<tr><td colspan="' +
          colCount +
          '" class="error-state">' +
          '<i class="fa ' +
          (icon || 'fa-exclamation-triangle') +
          ' error-state-icon"></i>' +
          '<div class="error-state-message">' +
          (title || '错误') +
          '</div>' +
          '<div class="error-state-description">' +
          (message || '') +
          '</div>';

        if (suggestion) {
          errorHtml += '<div class="error-state-suggestion">' + suggestion + '</div>';
        }

        if (retryable !== false) {
          errorHtml += '<button class="retry-button" onclick="retryLoadData(\'' + containerId + '\')">重试</button>';
        }

        errorHtml += '</td></tr>';

        tbody.innerHTML = errorHtml;
      }

      // 错误日志记录函数
      function logError(error, context) {
        var logEntry = {
          timestamp: new Date().toISOString(),
          error: {
            message: error ? error.message : 'Unknown error',
            stack: error ? error.stack : null,
            name: error ? error.name : 'Error',
          },
          context: context || {},
          userAgent: navigator.userAgent,
          url: window.location.href,
        };

        // 控制台输出
        console.error('错误日志:', logEntry);

        // 存储到本地存储（可选）
        try {
          var errorLogs = JSON.parse(localStorage.getItem('errorLogs') || '[]');
          errorLogs.push(logEntry);

          // 只保留最近50条错误日志
          if (errorLogs.length > 50) {
            errorLogs = errorLogs.slice(-50);
          }

          localStorage.setItem('errorLogs', JSON.stringify(errorLogs));
        } catch (e) {
          console.warn('无法保存错误日志到本地存储:', e);
        }

        // 发送错误报告到服务器（可选）
        if (window.reportError && typeof window.reportError === 'function') {
          try {
            window.reportError(logEntry);
          } catch (e) {
            console.warn('无法发送错误报告:', e);
          }
        }
      }

      // 错误恢复函数
      function recoverFromError(containerId, errorType) {
        switch (errorType) {
          case ErrorTypes.NETWORK_ERROR:
            // 网络错误恢复：检查网络状态
            if (navigator.onLine) {
              setTimeout(function () {
                retryLoadData(containerId);
              }, 2000);
            } else {
              // 监听网络恢复
              window.addEventListener(
                'online',
                function () {
                  retryLoadData(containerId);
                },
                { once: true }
              );
            }
            break;

          case ErrorTypes.TIMEOUT_ERROR:
            // 超时错误恢复：延迟重试
            setTimeout(function () {
              retryLoadData(containerId);
            }, 5000);
            break;

          case ErrorTypes.SERVER_ERROR:
            // 服务器错误恢复：指数退避重试
            var retryCount = parseInt(sessionStorage.getItem('retryCount_' + containerId) || '0');
            if (retryCount < 3) {
              var delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
              setTimeout(function () {
                sessionStorage.setItem('retryCount_' + containerId, String(retryCount + 1));
                retryLoadData(containerId);
              }, delay);
            }
            break;

          default:
            // 其他错误类型不自动恢复
            break;
        }
      }

      // 清除错误重试计数
      function clearRetryCount(containerId) {
        sessionStorage.removeItem('retryCount_' + containerId);
      }

      // 初始化错误处理系统
      function initErrorHandlingSystem() {
        // 添加网络状态指示器
        var networkIndicator = document.createElement('div');
        networkIndicator.className = 'network-status';
        networkIndicator.id = 'network-status';
        document.body.appendChild(networkIndicator);

        // 监听网络状态变化
        window.addEventListener('online', function () {
          networkIndicator.textContent = '网络已连接';
          networkIndicator.className = 'network-status online';
          setTimeout(function () {
            networkIndicator.style.display = 'none';
          }, 3000);
        });

        window.addEventListener('offline', function () {
          networkIndicator.textContent = '网络连接已断开';
          networkIndicator.className = 'network-status offline';
        });

        // 初始网络状态检查
        if (!navigator.onLine) {
          networkIndicator.textContent = '网络连接已断开';
          networkIndicator.className = 'network-status offline';
        }

        // 全局错误捕获
        window.addEventListener('error', function (event) {
          logError(event.error, {
            type: 'global_error',
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
          });
        });

        // Promise错误捕获
        window.addEventListener('unhandledrejection', function (event) {
          logError(event.reason, {
            type: 'unhandled_promise_rejection',
          });
        });

        // 定期清理错误日志
        setInterval(function () {
          try {
            var errorLogs = JSON.parse(localStorage.getItem('errorLogs') || '[]');
            var oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

            var filteredLogs = errorLogs.filter(function (log) {
              return new Date(log.timestamp) > oneWeekAgo;
            });

            if (filteredLogs.length !== errorLogs.length) {
              localStorage.setItem('errorLogs', JSON.stringify(filteredLogs));
            }
          } catch (e) {
            console.warn('清理错误日志失败:', e);
          }
        }, 60 * 60 * 1000); // 每小时清理一次
      }

      // 获取错误统计信息
      function getErrorStatistics() {
        try {
          var errorLogs = JSON.parse(localStorage.getItem('errorLogs') || '[]');
          var stats = {
            total: errorLogs.length,
            byType: {},
            recent: 0,
          };

          var oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

          errorLogs.forEach(function (log) {
            // 按类型统计
            var errorType = log.context.errorType || 'unknown';
            stats.byType[errorType] = (stats.byType[errorType] || 0) + 1;

            // 最近一小时的错误
            if (new Date(log.timestamp) > oneHourAgo) {
              stats.recent++;
            }
          });

          return stats;
        } catch (e) {
          console.warn('获取错误统计失败:', e);
          return { total: 0, byType: {}, recent: 0 };
        }
      }

      function retryLoadData(containerId) {
        // 清除之前的错误状态
        clearRetryCount(containerId);

        // 根据表格ID确定重试的数据加载函数
        if (containerId.includes('personnel-structure-age')) {
          loadPersonnelStructureAnalysisData();
        } else if (containerId.includes('org-level-overall')) {
          loadOrgLevelAnalysisData();
        } else if (containerId.includes('position-line')) {
          loadPostLineAnalysisData();
        } else if (containerId.includes('adjustment')) {
          loadAdjustmentAnalysisData();
        }
      }

      // 错误处理系统

      // 错误类型枚举
      var ErrorTypes = {
        NETWORK_ERROR: 'network_error',
        TIMEOUT_ERROR: 'timeout_error',
        PERMISSION_ERROR: 'permission_error',
        NOT_FOUND_ERROR: 'not_found_error',
        SERVER_ERROR: 'server_error',
        DATA_FORMAT_ERROR: 'data_format_error',
        VALIDATION_ERROR: 'validation_error',
        UNKNOWN_ERROR: 'unknown_error',
      };

      // 错误消息配置
      var ErrorMessages = {
        [ErrorTypes.NETWORK_ERROR]: {
          title: '网络连接失败',
          message: '无法连接到服务器，请检查网络连接',
          suggestion: '请检查网络设置后重试',
          icon: 'fa-wifi',
          retryable: true,
        },
        [ErrorTypes.TIMEOUT_ERROR]: {
          title: '请求超时',
          message: '服务器响应超时',
          suggestion: '请稍后重试或联系系统管理员',
          icon: 'fa-clock-o',
          retryable: true,
        },
        [ErrorTypes.PERMISSION_ERROR]: {
          title: '访问权限不足',
          message: '您没有访问此数据的权限',
          suggestion: '请联系系统管理员获取相应权限',
          icon: 'fa-lock',
          retryable: false,
        },
        [ErrorTypes.NOT_FOUND_ERROR]: {
          title: '数据不存在',
          message: '请求的数据不存在或已被删除',
          suggestion: '请刷新页面或选择其他数据',
          icon: 'fa-search',
          retryable: false,
        },
        [ErrorTypes.SERVER_ERROR]: {
          title: '服务器内部错误',
          message: '服务器处理请求时发生错误',
          suggestion: '请稍后重试，如问题持续请联系技术支持',
          icon: 'fa-server',
          retryable: true,
        },
        [ErrorTypes.DATA_FORMAT_ERROR]: {
          title: '数据格式错误',
          message: '服务器返回的数据格式不正确',
          suggestion: '请联系系统管理员检查数据源',
          icon: 'fa-exclamation-triangle',
          retryable: false,
        },
        [ErrorTypes.VALIDATION_ERROR]: {
          title: '数据验证失败',
          message: '数据不符合预期格式',
          suggestion: '请检查输入数据或联系技术支持',
          icon: 'fa-check-circle-o',
          retryable: false,
        },
        [ErrorTypes.UNKNOWN_ERROR]: {
          title: '未知错误',
          message: '发生了未知错误',
          suggestion: '请刷新页面重试，如问题持续请联系技术支持',
          icon: 'fa-question-circle',
          retryable: true,
        },
      };

      // 错误类型检测函数
      function detectErrorType(error, xhr, status) {
        // 网络错误检测
        if (!navigator.onLine) {
          return ErrorTypes.NETWORK_ERROR;
        }

        // 超时错误
        if (status === 'timeout') {
          return ErrorTypes.TIMEOUT_ERROR;
        }

        // HTTP状态码错误
        if (xhr && xhr.status) {
          switch (xhr.status) {
            case 401:
            case 403:
              return ErrorTypes.PERMISSION_ERROR;
            case 404:
              return ErrorTypes.NOT_FOUND_ERROR;
            case 500:
            case 502:
            case 503:
            case 504:
              return ErrorTypes.SERVER_ERROR;
            default:
              if (xhr.status >= 400 && xhr.status < 500) {
                return ErrorTypes.VALIDATION_ERROR;
              } else if (xhr.status >= 500) {
                return ErrorTypes.SERVER_ERROR;
              }
          }
        }

        // 数据格式错误
        if (error && error.message && error.message.indexOf('JSON') !== -1) {
          return ErrorTypes.DATA_FORMAT_ERROR;
        }

        // 验证错误
        if (error && error.message && error.message.indexOf('验证') !== -1) {
          return ErrorTypes.VALIDATION_ERROR;
        }

        return ErrorTypes.UNKNOWN_ERROR;
      }

      // API响应验证函数
      function validateApiResponse(response) {
        try {
          // 基本响应检查
          if (!response) {
            throw new Error('API响应为空');
          }

          // 响应码检查
          if (typeof response.code === 'undefined') {
            throw new Error('API响应缺少code字段');
          }

          if (response.code !== 200) {
            throw new Error('API响应错误码: ' + response.code + ', 消息: ' + (response.msg || '未知错误'));
          }

          // 数据字段检查
          if (!response.data) {
            throw new Error('API响应缺少data字段');
          }

          var data = response.data;

          // 必需字段验证
          if (typeof data.total_count === 'undefined' || data.total_count === null) {
            throw new Error('API响应缺少total_count字段');
          }

          if (typeof data.total_count !== 'number' || data.total_count < 0) {
            throw new Error('total_count字段格式错误，应为非负数');
          }

          // 数组字段验证
          if (data.leading_group_allocation !== undefined && !Array.isArray(data.leading_group_allocation)) {
            throw new Error('leading_group_allocation字段应为数组');
          }

          if (data.edu !== undefined && !Array.isArray(data.edu)) {
            throw new Error('edu字段应为数组');
          }

          // 验证数组元素结构
          if (data.leading_group_allocation) {
            data.leading_group_allocation.forEach(function (item, index) {
              if (!item || typeof item !== 'object') {
                throw new Error('leading_group_allocation[' + index + ']应为对象');
              }
              if (!item.leading_group_level) {
                throw new Error('leading_group_allocation[' + index + ']缺少leading_group_level字段');
              }
            });
          }

          if (data.edu) {
            data.edu.forEach(function (item, index) {
              if (!item || typeof item !== 'object') {
                throw new Error('edu[' + index + ']应为对象');
              }
              if (!item.education_level) {
                throw new Error('edu[' + index + ']缺少education_level字段');
              }
              if (typeof item.count !== 'number' || item.count < 0) {
                throw new Error('edu[' + index + '].count应为非负数');
              }
            });
          }

          return true;
        } catch (error) {
          console.error('API响应验证失败:', error.message);
          return false;
        }
      }

      // 数据转换工具函数

      // 计算百分比
      function calculatePercentages(data) {
        if (!Array.isArray(data)) {
          console.error('calculatePercentages: 输入数据必须是数组');
          return data;
        }

        return data.map(function (item) {
          var total = item.total || 0;
          var result = Object.assign({}, item);

          // 为数值字段计算百分比
          Object.keys(item).forEach(function (key) {
            if (typeof item[key] === 'number' && key !== 'total' && total > 0) {
              var percentage = ((item[key] / total) * 100).toFixed(1);
              result[key + 'Percentage'] = percentage + '%';
            }
          });

          return result;
        });
      }

      // 格式化显示值
      function formatDisplayValues(value, type) {
        if (value === null || value === undefined) {
          return '-';
        }

        switch (type) {
          case 'number':
            return typeof value === 'number' ? value.toLocaleString() : value;

          case 'percentage':
            if (typeof value === 'number') {
              return value.toFixed(1) + '%';
            }
            return value.toString().endsWith('%') ? value : value + '%';

          case 'currency':
            return typeof value === 'number' ? '¥' + value.toLocaleString() : value;

          case 'date':
            if (value instanceof Date) {
              return value.toLocaleDateString('zh-CN');
            }
            return value;

          case 'text':
          default:
            return value.toString();
        }
      }

      // 数据分组函数
      function groupDataByCategory(data, groupField) {
        if (!Array.isArray(data) || !groupField) {
          return data;
        }

        var grouped = {};
        data.forEach(function (item) {
          var key = item[groupField] || '未分类';
          if (!grouped[key]) {
            grouped[key] = [];
          }
          grouped[key].push(item);
        });

        return grouped;
      }

      // 数据聚合函数
      function aggregateData(data, aggregateFields) {
        if (!Array.isArray(data) || !aggregateFields) {
          return data;
        }

        var result = {};

        aggregateFields.forEach(function (field) {
          var sum = data.reduce(function (total, item) {
            return total + (item[field] || 0);
          }, 0);
          result[field] = sum;
        });

        return result;
      }

      // 数据验证和清理函数
      function validateAndCleanData(data, schema) {
        if (!Array.isArray(data)) {
          return [];
        }

        return data.filter(function (item) {
          // 基本验证：确保item是对象
          if (typeof item !== 'object' || item === null) {
            return false;
          }

          // 如果提供了schema，进行字段验证
          if (schema) {
            for (var field in schema) {
              var fieldType = schema[field];
              var value = item[field];

              if (fieldType === 'required' && (value === null || value === undefined)) {
                return false;
              }

              if (fieldType === 'number' && typeof value !== 'number') {
                // 尝试转换为数字
                var numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                  item[field] = numValue;
                } else {
                  return false;
                }
              }
            }
          }

          return true;
        });
      }

      // 格式化单元格值
      function formatCellValue(value, column) {
        if (value === null || value === undefined) {
          return '-';
        }

        // 根据列类型格式化
        if (column.type === 'percentage' || (typeof value === 'string' && value.includes('%'))) {
          return value;
        }

        if (column.type === 'number' || typeof value === 'number') {
          return value.toLocaleString();
        }

        return String(value);
      }

      // 应用企业样式
      function applyEnterpriseStyles(tableElement) {
        if (!tableElement) return;

        // 确保表格有企业样式类
        if (!tableElement.classList.contains('enterprise-table')) {
          tableElement.classList.add('enterprise-table');
        }

        // 为第一列添加特殊样式（通常是机构名称或层级）
        var firstColumnCells = tableElement.querySelectorAll('td:first-child, th:first-child');
        firstColumnCells.forEach(function (cell) {
          cell.style.fontWeight = '500';
          if (cell.tagName === 'TD') {
            cell.style.backgroundColor = '#fafafa';
          }
        });

        // 为数值列添加右对齐
        var rows = tableElement.querySelectorAll('tbody tr');
        rows.forEach(function (row) {
          var cells = row.querySelectorAll('td');
          cells.forEach(function (cell, index) {
            var content = cell.textContent.trim();
            // 如果是数字或百分比，右对齐
            if (/^(?:\d+|\d{1,3}(?:,\d{3})*)(?:\.\d+)?%?$/.test(content) && index > 0) {
              cell.style.textAlign = 'right';
            }
          });
        });
      }

      // 清空表格数据
      function clearTableData(tableId) {
        try {
          var table = document.getElementById(tableId);
          if (!table) {
            throw new Error('找不到表格元素: ' + tableId);
          }

          var tbody = table.querySelector('tbody');
          if (!tbody) {
            throw new Error('表格缺少tbody元素');
          }

          // 获取列数
          var headers = table.querySelectorAll('thead th');
          var colCount = headers.length;

          // 显示空数据状态
          tbody.innerHTML = '';
          var emptyRow = document.createElement('tr');
          var emptyCell = document.createElement('td');
          emptyCell.colSpan = colCount;
          emptyCell.className = 'empty-data';
          emptyCell.innerHTML =
            '<i class="fa fa-table empty-data-icon"></i>' +
            '<div class="empty-data-message">暂无数据</div>' +
            '<div class="empty-data-description">数据已清空</div>';
          emptyRow.appendChild(emptyCell);
          tbody.appendChild(emptyRow);
        } catch (error) {
          console.error('清空表格数据失败:', error);
        }
      }

      // 获取表格数据
      function getTableData(tableId) {
        try {
          var table = document.getElementById(tableId);
          if (!table) {
            throw new Error('找不到表格元素: ' + tableId);
          }

          var headers = Array.from(table.querySelectorAll('thead th')).map(function (th) {
            return th.textContent.trim();
          });

          var rows = table.querySelectorAll('tbody tr');
          var data = [];

          rows.forEach(function (row) {
            var cells = row.querySelectorAll('td');
            if (cells.length === 1 && cells[0].classList.contains('empty-data')) {
              return; // 跳过空数据行
            }

            var rowData = {};
            cells.forEach(function (cell, index) {
              if (headers[index]) {
                rowData[headers[index]] = cell.textContent.trim();
              }
            });

            data.push(rowData);
          });

          return data;
        } catch (error) {
          console.error('获取表格数据失败:', error);
          return [];
        }
      }

      // 表格排序功能
      function sortTable(tableId, columnIndex, sortOrder) {
        try {
          var table = document.getElementById(tableId);
          if (!table) {
            throw new Error('找不到表格元素: ' + tableId);
          }

          var tbody = table.querySelector('tbody');
          var rows = Array.from(tbody.querySelectorAll('tr'));

          // 过滤掉空数据行
          var dataRows = rows.filter(function (row) {
            var firstCell = row.querySelector('td');
            return firstCell && !firstCell.classList.contains('empty-data');
          });

          if (dataRows.length === 0) return;

          // 排序
          dataRows.sort(function (a, b) {
            var aCell = a.querySelectorAll('td')[columnIndex];
            var bCell = b.querySelectorAll('td')[columnIndex];

            if (!aCell || !bCell) return 0;

            var aValue = aCell.textContent.trim();
            var bValue = bCell.textContent.trim();

            // 尝试数字比较
            var aNum = parseFloat(aValue.replace(/[,%]/g, ''));
            var bNum = parseFloat(bValue.replace(/[,%]/g, ''));

            if (!isNaN(aNum) && !isNaN(bNum)) {
              return sortOrder === 'desc' ? bNum - aNum : aNum - bNum;
            }

            // 字符串比较
            return sortOrder === 'desc' ? bValue.localeCompare(aValue) : aValue.localeCompare(bValue);
          });

          // 重新插入排序后的行
          tbody.innerHTML = '';
          dataRows.forEach(function (row) {
            tbody.appendChild(row);
          });
        } catch (error) {
          console.error('表格排序失败:', error);
        }
      }

      // 简单的数据缓存系统
      var dataCache = {
        storage: {},
        expireTime: 5 * 60 * 1000, // 5分钟过期

        // 生成缓存键
        getKey: function (type, deptId) {
          return type + '_' + (deptId || 'default');
        },

        // 设置缓存
        set: function (type, deptId, data) {
          var key = this.getKey(type, deptId);
          this.storage[key] = {
            data: data,
            timestamp: Date.now(),
          };
        },

        // 获取缓存
        get: function (type, deptId) {
          var key = this.getKey(type, deptId);
          var cached = this.storage[key];

          if (!cached) return null;

          // 检查是否过期
          if (Date.now() - cached.timestamp > this.expireTime) {
            delete this.storage[key];
            return null;
          }

          return cached.data;
        },

        // 清除特定缓存
        clear: function (type, deptId) {
          if (type && deptId) {
            var key = this.getKey(type, deptId);
            delete this.storage[key];
          } else if (type) {
            // 清除某类型的所有缓存
            Object.keys(this.storage).forEach(function (key) {
              if (key.startsWith(type + '_')) {
                delete dataCache.storage[key];
              }
            });
          } else {
            // 清除所有缓存
            this.storage = {};
          }
        },

        // 获取缓存统计
        getStats: function () {
          var keys = Object.keys(this.storage);
          var expired = 0;
          var valid = 0;

          keys.forEach(function (key) {
            var cached = dataCache.storage[key];
            if (Date.now() - cached.timestamp > dataCache.expireTime) {
              expired++;
            } else {
              valid++;
            }
          });

          return {
            total: keys.length,
            valid: valid,
            expired: expired,
          };
        },
      };

      // 表格渲染性能优化
      var renderOptimizer = {
        batchSize: 50, // 批量渲染行数

        // 优化的表格更新
        updateTableOptimized: function (tableId, data) {
          if (!data || data.length === 0) {
            handleEmptyState(document.getElementById(tableId), '暂无数据');
            return;
          }

          var table = document.getElementById(tableId);
          if (!table) return;

          var tbody = table.querySelector('tbody');
          if (!tbody) return;

          // 小数据集直接渲染
          if (data.length <= this.batchSize) {
            this.renderAllRows(tbody, data);
            return;
          }

          // 大数据集分批渲染
          this.renderProgressively(tbody, data);
        },

        // 直接渲染所有行
        renderAllRows: function (tbody, data) {
          var fragment = document.createDocumentFragment();

          data.forEach(function (rowData) {
            var row = document.createElement('tr');
            Object.keys(rowData).forEach(function (key) {
              var cell = document.createElement('td');
              cell.textContent = formatCellValue(rowData[key], { key: key });
              row.appendChild(cell);
            });
            fragment.appendChild(row);
          });

          tbody.innerHTML = '';
          tbody.appendChild(fragment);
        },

        // 分批渲染
        renderProgressively: function (tbody, data) {
          tbody.innerHTML = '';
          var index = 0;
          var self = this;

          function renderBatch() {
            var fragment = document.createDocumentFragment();
            var endIndex = Math.min(index + self.batchSize, data.length);

            for (var i = index; i < endIndex; i++) {
              var rowData = data[i];
              var row = document.createElement('tr');
              Object.keys(rowData).forEach(function (key) {
                var cell = document.createElement('td');
                cell.textContent = formatCellValue(rowData[key], { key: key });
                row.appendChild(cell);
              });
              fragment.appendChild(row);
            }

            tbody.appendChild(fragment);
            index = endIndex;

            // 继续渲染下一批
            if (index < data.length) {
              requestAnimationFrame(renderBatch);
            }
          }

          renderBatch();
        },
      };

      // 表格数据管理
      var tableData = {};

      // 性能优化的表格更新函数
      function updateTable(tableId, data) {
        try {
          // 检查数据
          if (!data || !Array.isArray(data) || data.length === 0) {
            handleEmptyState(document.getElementById(tableId), '暂无数据');
            return;
          }

          // 使用性能优化的渲染
          renderOptimizer.updateTableOptimized(tableId, data);

          // 存储数据到缓存
          tableData[tableId] = data;

          // 应用企业样式
          const table = document.getElementById(tableId);
          if (table) {
            applyEnterpriseStyles(table);
          }

          // 刷新图表分析
          const tableElement = document.getElementById(tableId);
          if (!tableElement) {
            return;
          }

          const tableContainer = tableElement.closest('.table-container');
          if (!tableContainer) {
            return;
          }

          const button = tableContainer.querySelector('.chart-analysis-btn');
          if (!button) {
            return;
          }

          const isChartView = tableContainer.classList.contains('chart-view');
          if (isChartView) {
            switchToChartView(tableContainer, button, tableId, data);
          }
        } catch (error) {
          console.error('更新表格失败:', error);
          showTableError(tableId, '表格更新失败', true);
        }
      }

      function queryDeptTree() {
        var url = ctx + 'system/user/deptTreeData';
        var options = {
          url: url,
          expandLevel: 1,
          view: {
            selectedMulti: true, // 启用多选模式
            nameIsHTML: true,
          },
          check: {
            enable: true, // 启用checkbox
            nocheckInherit: true, // 子节点不自动继承父节点选中状态
            chkboxType: { Y: '', N: '' }, // 父子节点独立选择
          },
          onCheck: zOnCheck, // 使用checkbox事件替代click事件
        };
        $.tree.init(options);

        // 多选模式下的checkbox事件处理
        function zOnCheck(event, treeId, treeNode) {
          // 获取所有选中的节点
          var checkedNodes = $._tree.getCheckedNodes(true);
          var selectedDeptIds = [];
          var selectedDeptNames = [];

          checkedNodes.forEach(function (node) {
            selectedDeptIds.push(node.id);
            selectedDeptNames.push(node.name);
          });

          // 更新隐藏字段（保持兼容性）
          $('#deptId').val(selectedDeptIds.join(','));

          // 更新选中状态提示
          updateDeptSelectionInfo(selectedDeptIds.length, selectedDeptNames);

          // 触发数据加载
          if (selectedDeptIds.length > 0) {
            refreshTabData();
          } else {
            // 没有选中任何部门时，清空数据或显示提示
            clearAllTablesData();
          }

          // 兼容性处理
          if (typeof handlerConfirmRemove === 'function') {
            handlerConfirmRemove();
          }
        }

        // 更新部门选择状态提示
        function updateDeptSelectionInfo(count, names) {
          var infoElement = document.getElementById('deptSelectionInfo');
          var countElement = document.getElementById('selectedCount');

          if (count > 0) {
            countElement.textContent = count;
            infoElement.style.display = 'block';
            infoElement.title = '已选择: ' + names.join(', ');
          } else {
            infoElement.style.display = 'none';
          }
        }
      }

      // 初始化人员结构分析Tab
      function initPersonnelAnalysisTab() {
        loadPersonnelStructureAnalysisData();
      }

      // 加载人员结构分析数据
      function loadPersonnelStructureAnalysisData() {
        // 显示加载状态
        showLoadingState('personnel-structure-age', {
          message: '正在加载年龄分布数据...',
          timeout: 5000,
        });
        showLoadingState('personnel-structure-gender', {
          message: '正在加载性别分布数据...',
          timeout: 5000,
        });
        showLoadingState('fulltime-education', {
          message: '正在加载全日制教育数据...',
          timeout: 5000,
        });
        showLoadingState('inservice-education', {
          message: '正在加载在职教育数据...',
          timeout: 5000,
        });
        showLoadingState('personnel-structure-ethnicity', {
          message: '正在加载民族分布数据...',
          timeout: 5000,
        });

        let data = [{ name: 'deptIds', value: getCurrentDeptIds() }];

        const requestConfig = {
          url: ctx + 'library/statistic/personnel/structure',
          type: 'POST',
          timeout: 10000,
          dataType: 'json',
          cache: false,
          data: data,
          success: function (response) {
            if (response && response.code === 0 && response.data) {
              const data = response.data;

              renderPersonnelStructureAgeTable(data.age || []);
              renderPersonnelStructureGenderTable(data.gender || []);

              let eduData = response.data.edu || [];
              let fulltimeEducation = eduData.filter(function (item) {
                return item.edu_category === '全日制教育';
              });
              renderPersonnelStructureEduTable('personnel-structure-fulltime-edu', fulltimeEducation || []);

              let inserviceEducation = eduData.filter(function (item) {
                return item.edu_category === '在职教育';
              });
              renderPersonnelStructureEduTable('personnel-structure-inservice-edu', inserviceEducation || []);

              renderPersonnelStructureNationTable(data.nation || []);
            }
          },
        };
        $.ajax(requestConfig).always(function () {
          hideLoadingState('personnel-structure-age');
          hideLoadingState('personnel-structure-gender');
          hideLoadingState('fulltime-education');
          hideLoadingState('inservice-education');
          hideLoadingState('personnel-structure-ethnicity');
        });
      }

      $(function () {
        var panehHidden = false;
        if ($(this).width() < 769) {
          panehHidden = true;
        }
        var libraryType = $('#libraryType').val();
        if ($('#personnelType').length) {
          $('#personnelType').val(libraryType);
        }

        $('body').layout({ initClosed: panehHidden, west__size: 255 });

        // 回到顶部绑定
        if ($.fn.toTop !== undefined) {
          var opt = {
            win: $('.ui-layout-center'),
            doc: $('.ui-layout-center'),
          };
          $('#scroll-up').toTop(opt);
        }

        queryDeptTree();

        // 初始化部门选择变化监听器
        initDepartmentSelectionListener();

        // 页面加载时加载默认数据（简化版）
        loadInitialDeptData();

        // 树操作按钮
        $('#btnExpand').click(function () {
          $._tree.expandAll(true);
          $(this).hide();
          $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function () {
          $._tree.expandAll(false);
          $(this).hide();
          $('#btnExpand').show();
        });

        $('#btnRefresh').click(function () {
          queryDeptTree();
        });

        // Tab切换事件
        $('.top-nav-bar .nav-item').on('click', function () {
          var targetPaneId = $(this).data('target');

          // 移除所有Tab的active类
          $('.top-nav-bar .nav-item').removeClass('active');
          // 为当前点击的Tab添加active类
          $(this).addClass('active');

          // 隐藏所有Tab内容
          $('.tab-content-wrapper .tab-pane').removeClass('active');

          // 显示当前Tab对应的内容
          var targetPane = $(targetPaneId);
          if (targetPane.length) {
            targetPane.addClass('active');
          }

          // 加载对应Tab的数据
          refreshTabData(targetPaneId);
        });

        // 默认加载第一个Tab的数据
        initPersonnelAnalysisTab();

        // 初始化错误处理系统
        initErrorHandlingSystem();

        // 初始化加载状态管理系统
        initLoadingStateManagement();
      });

      // 简化的部门选择触发器
      function initDepartmentSelectionListener() {
        // 统一的部门变化处理函数（带缓存清理）
        function handleDeptChange(deptId) {
          if (!deptId) return;

          // 获取部门信息并显示指示器
          var deptInfo = getCurrentDeptInfo();
          showDeptChangeIndicator(deptInfo.name);

          refreshTabData();
        }

        // 监听部门树节点点击
        $(document).on('click', '.dept-tree-node', function () {
          var deptId = $(this).data('dept-id') || $(this).attr('data-dept-id');
          handleDeptChange(deptId);
        });

        // 监听部门下拉框变化
        $(document).on('change', '#deptId, [name="deptId"]', function () {
          handleDeptChange($(this).val());
        });

        // 监听自定义部门选择事件
        $(document).on('deptChanged', function (event, deptId) {
          handleDeptChange(deptId);
        });
      }

      // 获取当前选中的部门信息
      function getCurrentDeptInfo() {
        var deptId = getCurrentDeptIds();
        var deptName = '';

        // 尝试获取部门名称
        var selectedNode = $('.dept-tree-node[data-dept-id="' + deptId + '"]');
        if (selectedNode.length) {
          deptName = selectedNode.text().trim();
        } else {
          var selectedOption = $('#deptId option:selected');
          if (selectedOption.length) {
            deptName = selectedOption.text().trim();
          }
        }

        return {
          id: deptId,
          name: deptName || '未知部门',
        };
      }

      // 显示部门切换指示器
      function showDeptChangeIndicator(deptName) {
        var indicator = document.getElementById('dept-change-indicator');
        if (!indicator) {
          indicator = document.createElement('div');
          indicator.id = 'dept-change-indicator';
          indicator.style.cssText =
            'position: fixed; top: 50px; right: 20px; background: #1ab394; color: white; padding: 8px 12px; border-radius: 4px; font-size: 12px; z-index: 1000; display: none;';
          document.body.appendChild(indicator);
        }

        indicator.textContent = '正在加载 ' + (deptName || '部门') + ' 数据...';
        indicator.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(function () {
          indicator.style.display = 'none';
        }, 3000);
      }

      // 简化的页面初始化时的部门数据加载
      function loadInitialDeptData() {
        var deptInfo = getCurrentDeptInfo();
        if (deptInfo.id) {
          loadOrgLevelAnalysisData();
        }
      }

      // 刷新Tab数据
      function refreshTabData(tabId) {
        if (!tabId) {
          tabId = $('.top-nav-bar .nav-item.active').data('target');
        }

        switch (tabId) {
          case '#tab-personnel-analysis':
            loadPersonnelStructureAnalysisData();
            break;
          case '#tab-organization-analysis':
            loadOrgLevelAnalysisData();
            break;
          case '#tab-position-line':
            loadPostLineAnalysisData();
            break;
          case '#tab-adjustment-analysis':
            loadAdjustmentAnalysisData();
            break;
        }
      }

      // 组织分析数据加载
      function loadOrgLevelAnalysisData() {
        const deptIds = getCurrentDeptIds();
        if (!deptIds || deptIds.length === 0) {
          clearAllTablesData();
          return;
        }

        // 生成缓存键（多个部门ID排序后连接）
        var cacheKey = deptIds.slice().sort().join(',');

        // 检查缓存
        var cachedData = dataCache.get('organization', cacheKey);
        if (cachedData) {
          updateOrgLevelTables(cachedData);
          setTableState('org-level-overall-table', 'success');
          return;
        }

        // 缓存未命中，从API加载
        setTableState('org-level-overall-table', 'loading', '正在获取组织数据...');

        // 显示加载状态
        showLoadingState('org-level-overall', {
          message: '正在加载总体情况数据...',
          timeout: 5000,
        });
        // 显示加载状态
        showLoadingState('org-level-gender', {
          message: '正在加载性别分析数据...',
          timeout: 5000,
        });
        showLoadingState('org-level-allocation', {
          message: '正在加载配备分析数据...',
          timeout: 5000,
        });
        showLoadingState('org-level-omission', {
          message: '正在加载缺位分析数据...',
          timeout: 5000,
        });
        showLoadingState('org-level-edu', {
          message: '正在加载学历分析数据...',
          timeout: 5000,
        });

        let data = [];
        data.push({ name: 'deptIds', value: deptIds });

        var apiUrl = ctx + 'library/statistic/orgs/levels';
        var requestConfig = {
          url: apiUrl,
          type: 'POST',
          timeout: 10000,
          dataType: 'json',
          cache: false,
          data: data,
          success: function (response) {
            if (response && response.code === 0 && response.data) {
              // 缓存API响应数据
              dataCache.set('organization', cacheKey, response.data);

              updateOrgLevelTables(response.data);
              setTableState('org-level-overall-table', 'success');
            } else {
              setTableState('org-level-overall-table', 'error', '数据格式错误', { showRetry: true });
            }
          },
          error: function (xhr, status, error) {
            setTableState('org-level-overall-table', 'error', '网络请求失败', { showRetry: true });
          },
        };

        $.ajax(requestConfig).always(function () {
          hideLoadingState('org-level-overall');
          hideLoadingState('org-level-gender');
          hideLoadingState('org-level-allocation');
          hideLoadingState('org-level-omission');
          hideLoadingState('org-level-edu');
        });
      }

      // 清空所有表格数据
      function clearAllTablesData() {
        // 清空组织分析相关表格
        var organizationTables = [
          'org-level-overall-table',
          'org-level-gender-table',
          'org-level-allocation-table',
          'org-level-omission-table',
          'org-level-edu-table',
        ];

        organizationTables.forEach(function (tableId) {
          var table = document.getElementById(tableId);
          if (table) {
            handleEmptyState(table, '请选择组织机构');
          }
        });

        // 清空人员分析相关表格
        var personnelTables = [
          'personnel-structure-age-table',
          'personnel-structure-gender-table',
          'personnel-structure-fulltime-edu',
          'personnel-structure-inservice-edu',
          'personnel-structure-ethnicity-table',
        ];

        personnelTables.forEach(function (tableId) {
          var table = document.getElementById(tableId);
          if (table) {
            handleEmptyState(table, '请选择组织机构');
          }
        });
      }

      function updateOrgLevelTables(data) {
        try {
          renderOrgLevelOverallTable(data.overall);
          renderOrgLevelGenderTable(data.overall);
          renderOrgLevelAllocationTable(data.leading_group_allocation);
          renderOrgLevelOmissionTable(data.leading_group_post_allocation);
          renderOrgLevelEduTable(data.edu);
        } catch (error) {
          console.error(error);
          setTableState('org-level-overall-table', 'error', '数据处理失败', { showRetry: true });
        }
      }

      function getCurrentDeptIds() {
        const deptIds = $('#deptId').val().split(',');
        return deptIds;
      }

      function renderPersonnelStructureAgeTable(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const tableId = 'personnel-structure-age-table';
        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        tableBody.innerHTML = '';

        const seenColumns = new Set();

        data.forEach(item => {
          if (item.name && !seenColumns.has(item.name)) {
            seenColumns.add(item.name);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const key = item.dept_id;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              total_count: item.total_count || 0,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.name)) {
            const value = item.value || 0;
            acc[key].dynamicColumn[item.name] += value;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        const prefixHeaders = ['机构名称', '总人数'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const fragment = document.createDocumentFragment();
        dataForTable.forEach(item => {
          const row = document.createElement('tr');

          const td = document.createElement('td');
          td.textContent = item.dept_name;
          row.appendChild(td);

          if (enableDataLink) {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            td.classList.add('link-text');
            td.addEventListener('click', function() {
              openPersonList(item.dept_id, item.dept_name);
            });
            row.appendChild(td);
          } else {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            row.appendChild(td);
          }

          dynamicColumns.forEach(colName => {
            const td = document.createElement('td');
            td.textContent = item.dynamicColumn[colName];

            if (enableDataLink) {
              const params = {};
              const dataValue = ageValueDict[colName];
              if (dataValue) {
                const ageRanges = dataValue.split('-');
                if (ageRanges.length > 1) {
                  let ageStart = parseInt(ageRanges[0]);
                  if (isNaN(ageStart)) {
                    ageStart = 0;
                  }
                  let ageEnd = parseInt(ageRanges[1]);
                  if (isNaN(ageEnd)) {
                    ageEnd = 200;
                  } else {
                    ageEnd = ageEnd - 1;
                  }
                  params.ageStart = ageStart;
                  params.ageEnd = ageEnd;
                }
              }
              td.classList.add('link-text');
              td.addEventListener('click', function() {
                openPersonList(item.dept_id, item.dept_name, params);
              });
            }

            row.appendChild(td);
          });

          fragment.appendChild(row);
        });

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderPersonnelStructureGenderTable(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const tableId = 'personnel-structure-gender-table';
        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        tableBody.innerHTML = '';

        const seenColumns = new Set();

        data.forEach(item => {
          if (item.name && !seenColumns.has(item.name)) {
            seenColumns.add(item.name);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const key = item.dept_id;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              total_count: item.total_count || 0,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.name)) {
            const value = item.value || 0;
            acc[key].dynamicColumn[item.name] += value;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        const prefixHeaders = ['机构名称', '总人数'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const fragment = document.createDocumentFragment();
        dataForTable.forEach(item => {
          const row = document.createElement('tr');

          const td = document.createElement('td');
          td.textContent = item.dept_name;
          row.appendChild(td);

          if (enableDataLink) {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            td.classList.add('link-text');
            td.addEventListener('click', function() {
              openPersonList(item.dept_id, item.dept_name);
            });
            row.appendChild(td);
          } else {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            row.appendChild(td);
          }

          dynamicColumns.forEach(colName => {
            const td = document.createElement('td');
            td.textContent = item.dynamicColumn[colName];

            if (enableDataLink) {
              let params = { sex: colName };
              td.classList.add('link-text');
              td.addEventListener('click', function() {
                openPersonList(item.dept_id, item.dept_name, params);
              });
            }

            row.appendChild(td);
          });

          fragment.appendChild(row);
        });

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderPersonnelStructureEduTable(tableId, data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        tableBody.innerHTML = '';

        const seenColumns = new Set();

        data.forEach(item => {
          if (item.name && !seenColumns.has(item.name)) {
            seenColumns.add(item.name);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const key = item.dept_id;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              edu_category: item.edu_category,
              total_count: item.total_count || 0,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.name)) {
            const value = item.value || 0;
            acc[key].dynamicColumn[item.name] += value;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        const prefixHeaders = ['机构名称', '总人数'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const fragment = document.createDocumentFragment();
        dataForTable.forEach(item => {
          const row = document.createElement('tr');

          const td = document.createElement('td');
          td.textContent = item.dept_name;
          row.appendChild(td);
       
          if (enableDataLink) {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            td.classList.add('link-text');
            td.addEventListener('click', function() {
              openPersonList(item.dept_id, item.dept_name);
            });
            row.appendChild(td);
          } else {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            row.appendChild(td);
          }

          dynamicColumns.forEach(colName => {
            const td = document.createElement('td');
            td.textContent = item.dynamicColumn[colName];

            if (enableDataLink) {
              let params = { eduCategory: item.edu_category, eduName: colName };
              td.classList.add('link-text');
              td.addEventListener('click', function() {
                openPersonList(item.dept_id, item.dept_name, params);
              });
            }

            row.appendChild(td);
          });

          fragment.appendChild(row);
        });

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderPersonnelStructureNationTable(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const tableId = 'personnel-structure-ethnicity-table';
        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        tableBody.innerHTML = '';

        const seenColumns = new Set();

        data.forEach(item => {
          if (item.name && !seenColumns.has(item.name)) {
            seenColumns.add(item.name);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const key = item.dept_id;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              total_count: item.total_count || 0,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.name)) {
            const value = item.value || 0;
            acc[key].dynamicColumn[item.name] += value;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        const prefixHeaders = ['机构名称', '总人数'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const fragment = document.createDocumentFragment();
        dataForTable.forEach(item => {
          const row = document.createElement('tr');
     
          const td = document.createElement('td');
          td.textContent = item.dept_name;
          row.appendChild(td);

          if (enableDataLink) {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            td.classList.add('link-text');
            td.addEventListener('click', function() {
              openPersonList(item.dept_id, item.dept_name);
            });
            row.appendChild(td);
          } else {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            row.appendChild(td);
          }

          dynamicColumns.forEach(colName => {
            const td = document.createElement('td');
            td.textContent = item.dynamicColumn[colName];

            if (enableDataLink) {
              let params = { nation: colName };
              td.classList.add('link-text');
              td.addEventListener('click', function() {
                openPersonList(item.dept_id, item.dept_name, params);
              });
            }

            row.appendChild(td);
          });

          fragment.appendChild(row);
        });

        if (!fragment.hasChildNodes()) {
        return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderOrgLevelOverallTable(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const tableId = 'org-level-overall-table';
        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        tableBody.innerHTML = '';

        const seenColumns = new Set();

        data.forEach(item => {
          if (item.name && !seenColumns.has(item.name)) {
            seenColumns.add(item.name);
          }
        });

        seenColumns.add('其他人员');
        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const key = item.dept_id;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              total_count: item.total_count || 0,
              allocated_count: 0,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.name)) {
            const value = item.allocated_count || 0;
            acc[key].dynamicColumn[item.name] += value;
            acc[key].allocated_count += value;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);

        const prefixHeaders = ['机构名称', '总人数'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length + suffixHeaders.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const fragment = document.createDocumentFragment();
        dataForTable.forEach(item => {
          item.other_count = Math.max(0, item.total_count - item.allocated_count);

          item.dynamicColumn['其他人员'] = item.other_count;

          const row = document.createElement('tr');

          const td = document.createElement('td');
          td.textContent = item.dept_name;
          row.appendChild(td);

          if (enableDataLink) {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            td.classList.add('link-text');
            td.addEventListener('click', function() {
              openPersonList(item.dept_id, item.dept_name);
            });
            row.appendChild(td);
          } else {
            const td = document.createElement('td');
            td.textContent = item.total_count;
            row.appendChild(td);
          }

          dynamicColumns.forEach(colName => {
            const td = document.createElement('td');
            td.textContent = item.dynamicColumn[colName];
            if (enableDataLink) {
              let params = { leadingGroupLevel: colName, isEmployed: 'Y' };
              td.classList.add('link-text');
              td.addEventListener('click', function() {
                openPersonList(item.dept_id, item.dept_name, params);
              });
            } 
            row.appendChild(td);
          });

          fragment.appendChild(row);
        });

        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderOrgLevelGenderTable(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const tableId = 'org-level-gender-table';
        const table = document.getElementById(tableId);
        const tableBody = table.querySelector('tbody');

        const deptsWithNamedEntries = new Set(data.filter(item => item.name).map(item => item.dept_id));
        const filteredData = data.filter(item => !(deptsWithNamedEntries.has(item.dept_id) && !item.name));
        const groupedByDept = filteredData.reduce((acc, item) => {
          const key = item.dept_name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        const fragment = document.createDocumentFragment();

        for (const deptName in groupedByDept) {
          const itemsInGroup = groupedByDept[deptName];
          const rowspanCount = itemsInGroup.length;

          itemsInGroup.forEach((item, index) => {
            const row = document.createElement('tr');

            const allocatedCount = item.allocated_count || 0;
            const maleCount = item.male_count || 0;
            const femaleCount = item.female_count || 0;
            const maleRate = allocatedCount > 0 ? ((maleCount / allocatedCount) * 100).toFixed(2) : '0.00';
            const femaleRate = allocatedCount > 0 ? ((femaleCount / allocatedCount) * 100).toFixed(2) : '0.00';

            // 只在第一行创建合并单元格
            if (index === 0) {
              const th = document.createElement('td');
              th.rowSpan = rowspanCount;
              th.textContent = deptName;
              row.appendChild(th);
            }

            // 创建并填充其他单元格
            const cellData = [item.name || '-', allocatedCount, maleCount, femaleCount, `${maleRate}%`, `${femaleRate}%`];
            cellData.forEach((text, index) => {
              const td = document.createElement('td');
              td.textContent = text;

              if (enableDataLink) {
                let params = { leadingGroupLevel: item.name, isEmployed: 'Y' };
                switch (index) {
                case 1:
                    td.classList.add('link-text');
                    td.addEventListener('click', function() {
                      openPersonList(item.dept_id, item.dept_name, params);
                    });
                  break;

                case 2: 
                  params.sex = '男';
                  td.classList.add('link-text');
                  td.addEventListener('click', function() {
                    openPersonList(item.dept_id, item.dept_name, params);
                  });
                  break;

                case 3:
                  params.sex = '女';
                  td.classList.add('link-text');
                  td.addEventListener('click', function() {
                    openPersonList(item.dept_id, item.dept_name, params);
                  });
                  break;
                }
              }
              
              row.appendChild(td);
            });

            fragment.appendChild(row);
          });
        }

        tableData[tableId] = data;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderOrgLevelAllocationTable(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const tableBody = document.getElementById('org-level-allocation-table')?.querySelector('tbody');

        const deptsWithNamedEntries = new Set(data.filter(item => item.name).map(item => item.dept_id));
        const filteredData = data.filter(item => !(deptsWithNamedEntries.has(item.dept_id) && !item.name));
        const groupedByDept = filteredData.reduce((acc, item) => {
          const key = item.dept_name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        const fragment = document.createDocumentFragment();

        for (const deptName in groupedByDept) {
          const itemsInGroup = groupedByDept[deptName];
          const rowspanCount = itemsInGroup.length;

          itemsInGroup.forEach((item, index) => {
            const row = document.createElement('tr');

            const count = item.count || 0;
            const allocatedCount = item.allocated_count || 0;
            const allocatedRate = count > 0 ? ((allocatedCount / count) * 100).toFixed(2) : '0.00';
            const balanceCount = count - allocatedCount;

            let overAllocatedCount = 0;
            let omissionCount = 0;

            if (balanceCount > 0) {
              omissionCount = balanceCount;
            } else {
              overAllocatedCount = -balanceCount;
            }

            // 只在第一行创建合并单元格
            if (index === 0) {
              const th = document.createElement('td');
              th.rowSpan = rowspanCount;
              th.textContent = deptName;
              row.appendChild(th);
            }

            // 创建并填充其他单元格
            const cellData = [item.name || '-', count, allocatedCount, `${allocatedRate}%`, overAllocatedCount, omissionCount];

            cellData.forEach((text, index) => {
              const td = document.createElement('td');
              td.textContent = text;
              if (index === 2 && enableDataLink) {
                let params = { leadingGroupLevel: item.name, isEmployed: 'Y' };
                td.classList.add('link-text');
                td.addEventListener('click', function() {
                  openPersonList(item.dept_id, item.dept_name, params);
                });
              }

              row.appendChild(td);
            });

            fragment.appendChild(row);
          });
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderOrgLevelOmissionTable(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
          return;
        }

        const tableBody = document.getElementById('org-level-omission-table')?.querySelector('tbody');

        const deptsWithNamedEntries = new Set(data.filter(item => item.name).map(item => item.dept_id));
        const filteredData = data.filter(item => !(deptsWithNamedEntries.has(item.dept_id) && !item.name));
        const groupedByDept = filteredData.reduce((acc, item) => {
          const key = item.dept_name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        const fragment = document.createDocumentFragment();

        for (const deptName in groupedByDept) {
          const itemsInGroup = groupedByDept[deptName];
          const rowspanCount = itemsInGroup.length;

          itemsInGroup.forEach((item, index) => {
            const row = document.createElement('tr');

            const postName = item.position_name || '-';
            const count = item.count || 0;
            const allocatedCount = item.allocated_count || 0;
            const balanceCount = count - allocatedCount;
            const omissionRate = count > 0 ? ((balanceCount / count) * 100).toFixed(2) : '0.00';

            let overAllocatedCount = 0;
            let omissionCount = 0;

            if (balanceCount > 0) {
              omissionCount = balanceCount;
            } else {
              overAllocatedCount = -balanceCount;
            }

            // 只在第一行创建合并单元格
            if (index === 0) {
              const th = document.createElement('td');
              th.rowSpan = rowspanCount;
              th.textContent = deptName;
              row.appendChild(th);
            }

            // 创建并填充其他单元格
            const cellData = [item.name || '-', postName, count, allocatedCount, omissionCount, `${omissionRate}%`, overAllocatedCount];

            cellData.forEach((text, index) => {
              const td = document.createElement('td');
              td.textContent = text;
              if (index === 3 && enableDataLink) {
                let params = { 
                  leadingGroupLevel: item.name, 
                  positionName: item.position_name,
                  isEmployed: 'Y'
                };
                td.classList.add('link-text');
                td.addEventListener('click', function() {
                  openPersonList(item.dept_id, item.dept_name, params);
                });
              }
              row.appendChild(td);
            });

            fragment.appendChild(row);
          });
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderOrgLevelEduTable(data) {
        const table = document.getElementById('org-level-edu-table');
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        if (!data || !Array.isArray(data) || data.length === 0) {
          tableHeadRow.innerHTML = '<th>信息</th>';
          tableBody.innerHTML = '<tr><td class="empty-data">暂无数据</td></tr>';
          return;
        }

        const seenColumns = new Set();

        data.forEach(item => {
          if (!seenColumns.has(item.name)) {
            seenColumns.add(item.name);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const level = item.leading_group_level || '';
          const key = `${item.dept_id}-${level}-${item.edu_category}`;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              leading_group_level: level,
              edu_category: item.edu_category,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.name)) {
            acc[key].dynamicColumn[item.name] += item.value || 0;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        const prefixHeaders = ['机构名称', '层次', '教育类型'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const deptsWithNamedEntries = new Set(dataForTable.filter(item => item.leading_group_level).map(item => item.dept_id));
        const filteredData = dataForTable.filter(item => !(deptsWithNamedEntries.has(item.dept_id) && !item.leading_group_level));
        const groupedByDept = filteredData.reduce((acc, item) => {
          const key = item.dept_name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        const fragment = document.createDocumentFragment();

        for (const deptName in groupedByDept) {
          const itemsInGroup = groupedByDept[deptName];
          const rowspanCount = itemsInGroup.length;

          itemsInGroup.forEach((item, index) => {
            const row = document.createElement('tr');

            const leadingGroupLevel = item.leading_group_level || '-';
            const eduCategory = item.edu_category || '-';

            // 只在第一行创建合并单元格
            if (index === 0) {
              const th = document.createElement('td');
              th.rowSpan = rowspanCount;
              th.textContent = deptName;
              row.appendChild(th);
            }

            // 创建并填充其他单元格
            const cellData = [leadingGroupLevel, eduCategory];

            dynamicColumns.forEach(colName => {
              cellData.push(item.dynamicColumn[colName]);
            });

            cellData.forEach((text, index) => {
              const td = document.createElement('td');
              td.textContent = text;
              if (index >= 2 && enableDataLink) {
                let params = { 
                  leadingGroupLevel: item.leading_group_level, 
                  eduCategory: item.edu_category,
                  eduName: dynamicColumns[index - 2],
                  isEmployed: 'Y'
                };

                td.classList.add('link-text');
                td.addEventListener('click', function() {
                  openPersonList(item.dept_id, item.dept_name, params);
                });
              }
              row.appendChild(td);
            });

            fragment.appendChild(row);
          });
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderPostLineTable(data) {
        const tableId = 'position-line-table';
        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        if (!data || !Array.isArray(data) || data.length === 0) {
          tableHeadRow.innerHTML = '<th>信息</th>';
          tableBody.innerHTML = '<tr><td class="empty-data">暂无数据</td></tr>';
          return;
        }

        const seenColumns = new Set();

        data.forEach(item => {
          if (!seenColumns.has(item.year)) {
            seenColumns.add(item.year);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const postTypeName = item.post_type_name || '';
          const key = `${item.dept_id}-${postTypeName}-${item.post_name}`;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              post_name: item.post_name,
              post_type_name: item.post_type_name,
              person_id: item.person_id,
              name: item.name,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: [] }), {}),
            };
          } 
          
          if (!item.person_id) {
            return acc;
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.year)) {
            let persons = acc[key].dynamicColumn[item.year] || [];
            persons.push({ person_id: item.person_id, name: item.name || '' })
            acc[key].dynamicColumn[item.year] = _.uniqBy(persons, 'person_id');
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        const prefixHeaders = ['机构名称', '职务类型', '职务名称'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const deptsWithNamedEntries = new Set(dataForTable.filter(item => item.leading_group_level).map(item => item.dept_id));
        const filteredData = dataForTable.filter(item => !(deptsWithNamedEntries.has(item.dept_id) && !item.leading_group_level));
        const groupedByDept = filteredData.reduce((acc, item) => {
          const key = item.dept_name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        const fragment = document.createDocumentFragment();

        for (const deptName in groupedByDept) {
          const itemsInGroup = groupedByDept[deptName];
          const rowspanCount = itemsInGroup.length;

          itemsInGroup.forEach((item, index) => {
            const row = document.createElement('tr');

            const postTypeName = item.post_type_name || '-';
            const postName = item.post_name || '-';

            // 只在第一行创建合并单元格
            if (index === 0) {
              const th = document.createElement('td');
              th.rowSpan = rowspanCount;
              th.textContent = deptName;
              row.appendChild(th);
            }

            // 创建并填充其他单元格
            const cellData = [postTypeName, postName];

            dynamicColumns.forEach(colName => {
              cellData.push(item.dynamicColumn[colName]);
            });

            cellData.forEach((item, index) => {
              const td = document.createElement('td');
              if (index < 2) {
                td.textContent = item;
              } else {
                let persons = item || [];
                if (enableDataLink) {
                  const linkFragment = document.createDocumentFragment();
                  persons.forEach((person, personIndex) => {
                    const link = document.createElement('a');
                    link.href = 'javascript:void(0);';
                    link.textContent = person.name;
                    link.classList.add('link-text');
                    link.addEventListener('click', function() {
                      showPersonDetail(`${person.name} 的资料`, person.person_id);
                    });
                    linkFragment.appendChild(link);

                    if (personIndex < persons.length - 1) {
                      const comma = document.createElement('span');
                      comma.textContent = ',';
                      linkFragment.appendChild(comma);
                    }
                  });
                  td.appendChild(linkFragment);
                } else {
                  let personNames = persons.map(person => person.name).join(',');
                  td.textContent = personNames;
                }
              }
              row.appendChild(td);
            });

            fragment.appendChild(row);
          });
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderPositionAdjustedTable(data) {
        const tableId = 'position-adjusted-table';
        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        if (!data || !Array.isArray(data) || data.length === 0) {
          tableHeadRow.innerHTML = '<th>信息</th>';
          tableBody.innerHTML = '<tr><td class="empty-data">暂无数据</td></tr>';
          return;
        }

        const seenColumns = new Set();

        data.forEach(item => {
          if (!seenColumns.has(item.year)) {
            seenColumns.add(item.year);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const adjustType = item.adjust_type || '';
          const key = `${item.dept_id}-${adjustType}`;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              adjust_type: item.adjust_type,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.year)) {
            acc[key].dynamicColumn[item.year] += item.adjust_count || 0;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        const prefixHeaders = ['机构名称', '调整类型'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const deptsWithNamedEntries = new Set(dataForTable.filter(item => item.adjust_type).map(item => item.dept_id));
        const filteredData = dataForTable.filter(item => !(deptsWithNamedEntries.has(item.dept_id) && !item.adjust_type));
        const groupedByDept = filteredData.reduce((acc, item) => {
          const key = item.dept_name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        const fragment = document.createDocumentFragment();

        for (const deptName in groupedByDept) {
          const itemsInGroup = groupedByDept[deptName];
          const rowspanCount = itemsInGroup.length;

          itemsInGroup.forEach((item, index) => {
            const row = document.createElement('tr');

            const adjustType = item.adjust_type || '-';

            // 只在第一行创建合并单元格
            if (index === 0) {
              const th = document.createElement('td');
              th.rowSpan = rowspanCount;
              th.textContent = deptName;
              row.appendChild(th);
            }

            // 创建并填充其他单元格
            const cellData = [adjustType];

            dynamicColumns.forEach(colName => {
              cellData.push(item.dynamicColumn[colName]);
            });

            cellData.forEach((text, index) => {
              const td = document.createElement('td');
              td.textContent = text;

              if (index >= 1 && enableDataLink) {
                const year = dynamicColumns[index - 1];
                const adjustBeginTime = moment(`${year}-01-01`).format('YYYY-MM-DD');
                const adjustEndTime = moment(adjustBeginTime).add(1, 'year').format('YYYY-MM-DD');
                const params = { 
                  positionAdjustTagList: item.adjust_type,
                  adjustBeginTime,
                  adjustEndTime
                };
                
                td.classList.add('link-text');
                td.addEventListener('click', function() {
                  openPersonList(item.dept_id, item.dept_name, params);
                });
              }

              row.appendChild(td);
            });

            fragment.appendChild(row);
          });
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      function renderPositionAdjustProposedTable(data) {
        const tableId = 'position-proposed-table';
        const table = document.getElementById(tableId);
        const tableHeadRow = table.querySelector('thead tr');
        const tableBody = table.querySelector('tbody');

        if (!data || !Array.isArray(data) || data.length === 0) {
          tableHeadRow.innerHTML = '<th>信息</th>';
          tableBody.innerHTML = '<tr><td class="empty-data">暂无数据</td></tr>';
          return;
        }

        const seenColumns = new Set();

        data.forEach(item => {
          if (!seenColumns.has(item.year)) {
            seenColumns.add(item.year);
          }
        });

        const dynamicColumns = [...seenColumns];

        const processedData = data.reduce((acc, item) => {
          const adjustType = item.adjust_type || '';
          const key = `${item.dept_id}-${adjustType}`;

          if (!acc[key]) {
            acc[key] = {
              dept_id: item.dept_id,
              dept_name: item.dept_name,
              adjust_type: item.adjust_type,
              dynamicColumn: dynamicColumns.reduce((degAcc, col) => ({ ...degAcc, [col]: 0 }), {}),
            };
          }

          if (acc[key].dynamicColumn.hasOwnProperty(item.year)) {
            acc[key].dynamicColumn[item.year] += item.adjust_count || 0;
          }

          return acc;
        }, {});

        const dataForTable = Object.values(processedData);
        tableData[tableId] = dataForTable;

        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
          const button = tableContainer.querySelector('.chart-analysis-btn');
          const isChartView = tableContainer.classList.contains('chart-view');
          if (button && isChartView) {
            switchToChartView(tableContainer, button, tableId, dataForTable);
          }
        }

        const prefixHeaders = ['机构名称', '调整类型'];

        tableHeadRow.innerHTML = '';
        [...prefixHeaders, ...dynamicColumns].forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          tableHeadRow.appendChild(th);
        });

        tableBody.innerHTML = '';
        if (dataForTable.length === 0) {
          const cellCount = prefixHeaders.length + dynamicColumns.length;
          tableBody.innerHTML = `<tr><td colspan="${cellCount}" class="empty-data">无有效数据</td></tr>`;
          return;
        }

        const deptsWithNamedEntries = new Set(dataForTable.filter(item => item.adjust_type).map(item => item.dept_id));
        const filteredData = dataForTable.filter(item => !(deptsWithNamedEntries.has(item.dept_id) && !item.adjust_type));
        const groupedByDept = filteredData.reduce((acc, item) => {
          const key = item.dept_name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        const fragment = document.createDocumentFragment();

        for (const deptName in groupedByDept) {
          const itemsInGroup = groupedByDept[deptName];
          const rowspanCount = itemsInGroup.length;

          itemsInGroup.forEach((item, index) => {
            const row = document.createElement('tr');

            const adjustType = item.adjust_type || '-';

            // 只在第一行创建合并单元格
            if (index === 0) {
              const th = document.createElement('td');
              th.rowSpan = rowspanCount;
              th.textContent = deptName;
              row.appendChild(th);
            }

            // 创建并填充其他单元格
            const cellData = [adjustType];

            dynamicColumns.forEach(colName => {
              cellData.push(item.dynamicColumn[colName]);
            });

            cellData.forEach((text, index) => {
              const td = document.createElement('td');
              td.textContent = text;

              if (index >= 1 && enableDataLink) {
                const year = dynamicColumns[index - 1];
                const meetingBeginTime = moment(`${year}-01-01`).format('YYYY-MM-DD');
                const meetingEndTime = moment(meetingBeginTime).add(1, 'year').format('YYYY-MM-DD');
                const params = { 
                  deptId: item.dept_id,
                  adjustTypeList: item.adjust_type,
                  meetingBeginTime: meetingBeginTime,
                  meetingEndTime: meetingEndTime
                };
                
                td.classList.add('link-text');
                td.addEventListener('click', function() {
                  showPositionAdjustProposedDetail(params);
                });
              }

              row.appendChild(td);
            });

            fragment.appendChild(row);
          });
        }

        if (!fragment.hasChildNodes()) {
          return;
        }

        tableBody.innerHTML = '';
        tableBody.appendChild(fragment);
      }

      // 岗位条线分析数据
      function loadPostLineAnalysisData() {
        showLoadingState('position-line', {
          message: '正在加载岗位数据...',
          timeout: 5000,
        });

        let data = [{ name: 'deptIds', value: getCurrentDeptIds() }];

        let postIds = $('#positions').val();
        let year = $('#positionYear').val();
        if (postIds) {
          data.push({ name: 'postIds', value: postIds });
        }
        if (year) {
          data.push({ name: 'year', value: year });
        }

        const requestConfig = {
          url: ctx + 'library/statistic/orgs/levels/post_line',
          type: 'POST',
          timeout: 10000,
          dataType: 'json',
          cache: false,
          data: data,
          success: function (response) {
            if (response && response.code === 0 && response.data) {
              renderPostLineTable(response.data?.post_line || []);
            }
          },
        };
        $.ajax(requestConfig).always(function () {
          hideLoadingState('position-line');
        });
      }

      // 加载干部调整情况分析数据
      function loadAdjustmentAnalysisData() {
        loadPositionAdjustedAnalysisData();
        loadPositionProposedAnalysisData();
      }

      // 干部已调整情况分析数据
      function loadPositionAdjustedAnalysisData() {
        showLoadingState('position-adjusted', {
          message: '正在加载已调整情况分析数据...',
          timeout: 5000,
        });

        let data = [{ name: 'deptIds', value: getCurrentDeptIds() }];

        let adjustTypes = $('#positionAdjustedTypes').val();
        if (adjustTypes) {
          data.push({ name: 'adjustTypes', value: adjustTypes });
        }

        let year = $('#positionAdjustedYear').val();
        if (year) {
          data.push({ name: 'year', value: year });
        }

        var apiUrl = ctx + 'library/statistic/position/adjusts/adjusted';
        var requestConfig = {
          url: apiUrl,
          type: 'POST',
          timeout: 10000,
          dataType: 'json',
          cache: false,
          data: data,
          success: function (response) {
            if (response && response.code === 0 && response.data) {
              renderPositionAdjustedTable(response.data || []);
            } 
          },
        };

        $.ajax(requestConfig).always(function () {
          hideLoadingState('position-adjusted');
        });
      }

      // 干部拟调整情况分析数据
      function loadPositionProposedAnalysisData() {
        showLoadingState('position-proposed', {
          message: '正在加载需调整情况分析数据...',
          timeout: 5000,
        });

        let data = [{ name: 'deptIds', value: getCurrentDeptIds() }];

        let adjustTypes = $('#positionProposedTypes').val();
        if (adjustTypes) {
          data.push({ name: 'adjustTypes', value: adjustTypes });
        }

        let year = $('#positionProposedYear').val();
        if (year) {
          data.push({ name: 'year', value: year });
        }

        var apiUrl = ctx + 'library/statistic/position/adjusts/proposed';
        var requestConfig = {
          url: apiUrl,
          type: 'POST',
          timeout: 10000,
          dataType: 'json',
          cache: false,
          data: data,
          success: function (response) {
            if (response && response.code === 0 && response.data) {
              renderPositionAdjustProposedTable(response.data || []);
            } 
          },
        };

        $.ajax(requestConfig).always(function () {
          hideLoadingState('position-proposed');
        });
      }

      function openPersonList(deptId, deptName, params) {
        if (!params) {
          params = {};
        }

        params.type = 1;
        params.deptId = deptId;

        let url = `/library/person?${$.param(params)}`;

        $.modal.openTab(`人员列表 - ${deptName}`, url);
      }
      function showPersonDetail(title, id) {
        $.modal.openTab(title, ctx + 'library/person/edit/' + id);
      }    
      function showPositionAdjustProposedDetail(params) {
        if (!params) {
          params = {};
        }
        let url = `/library/position_adjust/position_adjust_proposed?${$.param(params)}`;
        $.modal.openTab('岗位拟调整列表', url);
      }
    </script>
  </body>
</html>
