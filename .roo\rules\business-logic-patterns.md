---
description: 业务逻辑模式
globs: 
alwaysApply: false
---
# MetaOrg 业务逻辑模式

## 业务模块结构
基于人员管理的核心业务，系统包含以下主要业务模块：

### 人员档案管理 (`library/person`)
- 人员基本信息维护
- 组织关系管理
- 人员照片和附件管理
- 参考文件: [MetaOrg/metaorg-library/src/main/java/com/metaorg/library/service/IBizPersonService.java](mdc:MetaOrg/metaorg-library/src/main/java/com/metaorg/library/service/IBizPersonService.java)

### 考核管理 (`library/check`)
- 年度考核记录
- 考核结果统计
- 考核导入导出功能
- 参考页面: [MetaOrg/metaorg-admin/src/main/resources/templates/library/check/check.html](mdc:MetaOrg/metaorg-admin/src/main/resources/templates/library/check/check.html)

### 政治情况管理 (`library/politics`)
- 政治面貌变更记录
- 党团关系管理
- 参考服务: [MetaOrg/metaorg-library/src/main/java/com/metaorg/library/service/IBizPersonPoliticsService.java](mdc:MetaOrg/metaorg-library/service/IBizPersonPoliticsService.java)

### 消息通知管理 (`library/message`)
- 模板化消息通知
- 用户订阅管理
- 多渠道通知发送
- 参考服务: [MetaOrg/metaorg-library/src/main/java/com/metaorg/library/service/IBizMessageNotifyTemplateService.java](mdc:MetaOrg/metaorg-library/service/IBizMessageNotifyTemplateService.java)

## 通用业务模式

### 数据访问层模式
```java
// Mapper 接口命名: I{业务}Mapper
// Service 接口命名: I{业务}Service  
// Service 实现命名: {业务}ServiceImpl
```

### 控制器模式
- 使用 RESTful API 设计
- 统一返回格式 AjaxResult
- 权限控制使用 `@RequiresPermissions`
- 数据验证使用 `@Valid`

### 页面模式
- 列表页面支持搜索、排序、分页
- 表单页面使用模态框
- 批量操作支持（删除、导入、导出）
- 树形结构支持（部门、组织）

### 数据导入导出模式
- Excel 导入使用统一模板
- 导出支持多种格式
- 导入前数据校验
- 异步处理大数据量操作

## 权限控制模式
基于 Shiro 的权限控制：
- 功能权限: `{module}:{function}:{operation}`
- 数据权限: 基于部门层级控制
- 示例: `library:check:edit` - 考核编辑权限

## 版本管理模式
版本发布记录在 `release/` 目录下：
- 版本号格式: R{YYYYMMDD}
- 每个版本包含更新说明文件
- 数据库变更脚本单独维护
