"""
数据存储和处理服务
"""

import json
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from loguru import logger

from nfra_crawler.models.database import (
    BizSyncRecord, 
    BizSyncPunishItem, 
    BizSyncPunishDetail,
    PunishDetailData
)
from nfra_crawler.utils.database import dao_factory


class DataProcessingService:
    """数据处理服务类"""
    
    def __init__(self):
        self.biz_person_dao = dao_factory.biz_person_dao
        self.sync_record_dao = dao_factory.sync_record_dao
        self.punish_item_dao = dao_factory.punish_item_dao
        self.punish_detail_dao = dao_factory.punish_detail_dao
    
    async def process_punish_items(self, items: List[BizSyncPunishItem]) -> List[BizSyncPunishItem]:
        """处理处罚公示项，实现去重和批量插入"""
        logger.info(f"开始处理 {len(items)} 条处罚公示项")
        
        # 去重处理
        unique_items = self._deduplicate_items(items)
        logger.info(f"去重后剩余 {len(unique_items)} 条记录")
        
        if not unique_items:
            logger.info("没有新的处罚公示项需要保存")
            return []
        
        try:
            # 批量插入
            inserted_count = self.punish_item_dao.batch_create_punish_items(unique_items)
            logger.info(f"成功插入 {inserted_count} 条处罚公示项")
            return unique_items
            
        except Exception as e:
            logger.error(f"批量插入处罚公示项失败: {e}")
            # 尝试逐条插入
            return await self._insert_items_individually(unique_items)
    
    async def process_punish_details(self, 
                                   punish_item: BizSyncPunishItem,
                                   detail_data_list: List[PunishDetailData]) -> List[BizSyncPunishDetail]:
        """处理处罚详情数据"""
        logger.info(f"开始处理处罚详情数据，共 {len(detail_data_list)} 条")
        
        punish_details = []
        
        # 获取处罚详情中当事人类型为"个人"的当事人姓名列表
        person_detail_data_list = [detail_data for detail_data in detail_data_list if detail_data.party_type == "个人"]
        person_name_list = [detail_data.party_name for detail_data in person_detail_data_list]
        
        # 根据当事人姓名列表查询人员信息
        person_name_dict = {}
        if person_name_list:
            person_info_list = self.biz_person_dao.get_persons_by_names(person_name_list)
            person_name_dict = {person_info['name']: person_info for person_info in person_info_list}
        
        for i, detail_data in enumerate(detail_data_list):
            try:
                # 创建处罚详情对象
                punish_detail = BizSyncPunishDetail(
                    punish_item_id=punish_item.id,
                    request_id=punish_item.request_id,
                    order_num=int(detail_data.serial_no) if detail_data.serial_no.isdigit() else i + 1,
                    party_name=detail_data.party_name,
                    party_position=detail_data.party_position,
                    punish_doc_no=detail_data.punish_doc_no,
                    violation=detail_data.violation,
                    punish_basis=detail_data.punish_basis,
                    punish_content=detail_data.punish_content,
                    punish_date=detail_data.punish_date,
                    decision_authority=detail_data.decision_authority,
                    party_type=detail_data.party_type,
                    punish_json_content=detail_data.model_dump_json(),
                    source_id=detail_data.source_id,
                    source_url=detail_data.source_url
                )
                
                # 判断当事人类型为"个人"且姓名在人员表中存在，则绑定人员信息
                if person_name_dict and detail_data.party_type == "个人" and detail_data.party_name in person_name_dict:
                    punish_detail.bind_person_id = person_name_dict[detail_data.party_name]['id']
                    punish_detail.bind_status = "pending"
                else:
                    punish_detail.bind_status = "none"

                # 调试日志：检查 source_id 和 source_url 是否正确设置
                logger.debug(f"创建处罚详情对象: {detail_data.party_name}, source_id: {detail_data.source_id}, source_url: {detail_data.source_url}")
                
                punish_details.append(punish_detail)
                
            except Exception as e:
                logger.warning(f"创建处罚详情对象失败: {e}, 数据: {detail_data}")
                continue
        
        if not punish_details:
            logger.warning("没有有效的处罚详情数据")
            return []
        
        try:
            # 批量插入处罚详情
            inserted_count = self.punish_detail_dao.batch_create_punish_details(punish_details)
            logger.info(f"成功插入 {inserted_count} 条处罚详情")
            return punish_details
            
        except Exception as e:
            logger.error(f"批量插入处罚详情失败: {e}")
            # 尝试逐条插入
            return await self._insert_details_individually(punish_details)
    
    async def update_sync_record_progress(self, 
                                        sync_record: BizSyncRecord,
                                        status: str,
                                        remark: str = None) -> bool:
        """更新同步记录进度"""
        try:
            completed_time = datetime.now() if status in ["succeeded", "failed"] else None
            elapsed = None
            
            if completed_time and sync_record.request_time:
                elapsed = int((completed_time - sync_record.request_time).total_seconds() * 1000)
            
            updated_count = self.sync_record_dao.update_sync_record_status(
                sync_record.id, status, completed_time, elapsed, remark
            )
            
            if updated_count > 0:
                logger.info(f"同步记录状态更新成功: {sync_record.id} -> {status}")
                return True
            else:
                logger.warning(f"同步记录状态更新失败: {sync_record.id}")
                return False
                
        except Exception as e:
            logger.error(f"更新同步记录状态时发生错误: {e}")
            return False
    
    def _deduplicate_items(self, items: List[BizSyncPunishItem]) -> List[BizSyncPunishItem]:
        """去重处理"""
        unique_items = []
        seen_items = set()
        
        for item in items:
            # 使用标题和source_id作为去重标识
            duplicate_key = (item.title, item.source_id)
            
            if duplicate_key in seen_items:
                logger.debug(f"发现重复项，跳过: {item.title}")
                continue
            
            # 检查数据库中是否已存在
            if item.title and item.source_id:
                if self.punish_item_dao.check_item_exists(item.title, item.source_id):
                    logger.debug(f"数据库中已存在该项，跳过: {item.title}")
                    continue
            
            seen_items.add(duplicate_key)
            unique_items.append(item)
        
        return unique_items
    
    async def _insert_items_individually(self, items: List[BizSyncPunishItem]) -> List[BizSyncPunishItem]:
        """逐条插入处罚公示项"""
        logger.info("尝试逐条插入处罚公示项")
        successful_items = []
        
        for item in items:
            try:
                item_id = self.punish_item_dao.create_punish_item(item)
                if item_id:
                    successful_items.append(item)
                    logger.debug(f"成功插入处罚公示项: {item.title}")
                else:
                    logger.warning(f"插入处罚公示项失败: {item.title}")
                    
            except Exception as e:
                logger.error(f"插入处罚公示项时发生错误: {e}, 项目: {item.title}")
                continue
        
        logger.info(f"逐条插入完成，成功 {len(successful_items)} 条")
        return successful_items
    
    async def _insert_details_individually(self, details: List[BizSyncPunishDetail]) -> List[BizSyncPunishDetail]:
        """逐条插入处罚详情"""
        logger.info("尝试逐条插入处罚详情")
        successful_details = []
        
        for detail in details:
            try:
                detail_id = self.punish_detail_dao.create_punish_detail(detail)
                if detail_id:
                    successful_details.append(detail)
                    logger.debug(f"成功插入处罚详情: {detail.party_name}")
                else:
                    logger.warning(f"插入处罚详情失败: {detail.party_name}")
                    
            except Exception as e:
                logger.error(f"插入处罚详情时发生错误: {e}, 详情: {detail.party_name}")
                continue
        
        logger.info(f"逐条插入完成，成功 {len(successful_details)} 条")
        return successful_details
    
    def get_latest_published_date(self) -> Optional[date]:
        """获取biz_sync_punish_item表中最新的published_date"""
        try:
            query = """
            SELECT published_date FROM biz_sync_punish_item
            WHERE published_date IS NOT NULL
            ORDER BY published_date DESC
            LIMIT 1
            """
            result = dao_factory.db_manager.execute_query(query)
            if result and result[0]['published_date']:
                # 将datetime转换为date
                published_datetime = result[0]['published_date']
                return published_datetime.date() if isinstance(published_datetime, datetime) else published_datetime
            return None

        except Exception as e:
            logger.error(f"查询最新published_date时发生错误: {e}")
            return None

    def get_processing_statistics(self, request_id: str) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            # 获取同步记录
            sync_record = self.sync_record_dao.get_sync_record_by_request_id(request_id)
            if not sync_record:
                return {"error": "未找到同步记录"}

            # 统计处罚公示项数量
            item_count_query = """
            SELECT COUNT(*) as count FROM biz_sync_punish_item
            WHERE request_id = %s
            """
            item_result = dao_factory.db_manager.execute_query(item_count_query, (request_id,))
            item_count = item_result[0]['count'] if item_result else 0

            # 统计处罚详情数量
            detail_count_query = """
            SELECT COUNT(*) as count FROM biz_sync_punish_detail
            WHERE request_id = %s
            """
            detail_result = dao_factory.db_manager.execute_query(detail_count_query, (request_id,))
            detail_count = detail_result[0]['count'] if detail_result else 0

            # 统计当事人类型分布
            party_type_query = """
            SELECT party_type, COUNT(*) as count
            FROM biz_sync_punish_detail
            WHERE request_id = %s
            GROUP BY party_type
            """
            party_type_result = dao_factory.db_manager.execute_query(party_type_query, (request_id,))
            party_type_stats = {row['party_type']: row['count'] for row in party_type_result}

            return {
                "sync_record": sync_record,
                "punish_item_count": item_count,
                "punish_detail_count": detail_count,
                "party_type_distribution": party_type_stats
            }

        except Exception as e:
            logger.error(f"获取处理统计信息时发生错误: {e}")
            return {"error": str(e)}


# 全局数据处理服务实例
data_service = DataProcessingService()
