package com.metaorg.web.controller.library;

import java.util.List;

// import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.core.text.Convert;
// import com.metaorg.library.domain.BizPerson;
// import com.metaorg.library.service.IBizPersonService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonResume;
import com.metaorg.library.service.IBizPersonResumeService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;

/**
 * 工作简历Controller
 * 
 * <AUTHOR>
 * @date 2023-06-04
 */
@Controller
@RequestMapping("/library/resume")
public class BizPersonResumeController extends BaseController
{
    private String prefix = "library/resume";

    @Autowired
    private IBizPersonResumeService bizPersonResumeService;

    // @Autowired
    // private IBizPersonService bizPersonService;

    @RequiresPermissions("library:resume:view")
    @GetMapping()
    public String resume()
    {
        return prefix + "/resume";
    }

    /**
     * 查询工作简历列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonResume bizPersonResume)
    {
        startPage();
//        com.github.pagehelper.PageHelper.orderBy("start_date, case when end_date='' then NULL else end_date end asc");
        List<BizPersonResume> list = bizPersonResumeService.selectBizPersonResumeListOrderBy(bizPersonResume, "start_date, end_date asc");
        return getDataTable(list);
    }

    /**
     * 导出工作简历列表
     */
    @RequiresPermissions("library:resume:export")
    @Log(title = "工作简历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonResume bizPersonResume)
    {
        List<BizPersonResume> list = bizPersonResumeService.selectBizPersonResumeListOrderBy(bizPersonResume, "start_date, end_date asc");
        ExcelUtil<BizPersonResume> util = new ExcelUtil<BizPersonResume>(BizPersonResume.class);
        return util.exportExcel(list, "工作简历数据");
    }

    /**
     * 新增工作简历
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        return prefix + "/add";
    }

    /**
     * 新增保存工作简历
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "工作简历", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "工作简历", tableName = PersonTable.BIZ_PERSON_RESUME, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonResume bizPersonResume)
    {
        AjaxResult result = toAjax(bizPersonResumeService.insertBizPersonResume(bizPersonResume));
        //UpdateBizPersonJobUnit(bizPersonResume.getPersonId());
        bizPersonResumeService.updateBizPersonJobUnit(bizPersonResume.getPersonId());
        return result;
    }

    /**
     * 修改工作简历
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{resumeId}")
    public String edit(@PathVariable("resumeId") String resumeId, ModelMap mmap)
    {
        BizPersonResume bizPersonResume = bizPersonResumeService.selectBizPersonResumeByResumeId(resumeId);
        mmap.put("bizPersonResume", bizPersonResume);
        return prefix + "/edit";
    }

    /**
     * 修改保存工作简历
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "工作简历", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "工作简历", tableName = PersonTable.BIZ_PERSON_RESUME, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonResume bizPersonResume)
    {
        BizPersonResume chkBizPersonResume = bizPersonResumeService.selectBizPersonResumeByResumeId(bizPersonResume.getResumeId());
        AjaxResult result = toAjax(bizPersonResumeService.updateBizPersonResume(bizPersonResume));
        if(chkBizPersonResume != null)
        {
            //UpdateBizPersonJobUnit(chkBizPersonResume.getPersonId());
            bizPersonResumeService.updateBizPersonJobUnit(chkBizPersonResume.getPersonId());
        }
        return result;
    }

    /**
     * 删除工作简历
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "工作简历", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "工作简历", tableName = PersonTable.BIZ_PERSON_RESUME, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        BizPersonResume chkBizPersonResume = new BizPersonResume();
        chkBizPersonResume.getParams().put("resumeIds", Convert.toStrArray(ids));
        chkBizPersonResume.getParams().put("limit", 1);
        List<BizPersonResume> list = bizPersonResumeService.selectBizPersonResumeList(chkBizPersonResume);
        AjaxResult result = toAjax(bizPersonResumeService.deleteBizPersonResumeByResumeIds(ids));
        if(!list.isEmpty()) {
            //UpdateBizPersonJobUnit(list.get(0).getPersonId());
            bizPersonResumeService.updateBizPersonJobUnit(list.get(0).getPersonId());
        }
        return result;
        //return toAjax(bizPersonResumeService.deleteBizPersonResumeByResumeIds(ids));
    }

    /**
     * 根据简历更新人员职务信息
     */
    // private void UpdateBizPersonJobUnit(String personId)
    // {
    //     if(StringUtils.isEmpty(personId)) return;
    //     //按时间排序获取简历列表，先按开始时间倒序排序，再按结束时间倒序排序
    //     BizPersonResume bizPersonResume = new BizPersonResume();
    //     bizPersonResume.setPersonId(personId);
    //     bizPersonResume.getParams().put("limit", 1);
    //     List<BizPersonResume> list = bizPersonResumeService.selectBizPersonResumeListOrderBy(bizPersonResume, "start_date desc, end_date desc");
    //     if(!list.isEmpty()) {
    //         //更新人员信息职务
    //         BizPerson bizPerson = new BizPerson();
    //         //最多255个字符
    //         bizPerson.setJobUnit(StringUtils.substring(list.get(0).getDescribes(),0,255));
    //         bizPerson.setId(personId);
    //         bizPersonService.updateBizPerson(bizPerson);
    //     }
    // }
}
