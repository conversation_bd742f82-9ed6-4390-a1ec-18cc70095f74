package com.metaorg.web.controller.system;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.metaorg.common.constant.ShiroConstants;
import com.metaorg.common.core.domain.ResponseBodyApi;
import com.metaorg.common.utils.IpUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.enums.UserStatus;
import com.metaorg.common.utils.ServletUtils;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.framework.jwt.utils.JwtUtils;
import com.metaorg.framework.shiro.service.SysPasswordService;
import com.metaorg.framework.web.service.ConfigService;

import com.metaorg.system.service.ISysUserService;
import com.metaorg.common.utils.security.RsaUtils;
import com.metaorg.web.service.OAuth2Service;
import com.metaorg.framework.oauth2.auth.OAuth2Token;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import com.metaorg.system.service.ISysConfigService;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Controller
public class SysLoginController extends BaseController
{
    /**
     * 是否开启记住我功能
     */
    @Value("${shiro.rememberMe.enabled: false}")
    private boolean rememberMe;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private ConfigService configService;

    @Autowired
    @Qualifier("getEhCacheManager")
    private CacheManager ehCacheManager;
    
    @Autowired
    @Qualifier("getRedisCacheManager")
    private CacheManager redisCacheManager;
    
    @Value("${spring.redis.enabled}")
    private boolean redisEnabled = false;
    
    /**
     * 根据配置动态获取缓存管理器
     * @return 缓存管理器
     */
    private CacheManager getCacheManager() {
        return redisEnabled ? redisCacheManager : ehCacheManager;
    }
    
    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private OAuth2Service oAuth2Service;

    @GetMapping("/login")
    public String login(HttpServletRequest request, HttpServletResponse response, ModelMap mmap)
    {
        // 如果是Ajax请求，返回Json字符串。
        if (ServletUtils.isAjaxRequest(request))
        {
            return ServletUtils.renderString(response, "{\"code\":\"1\",\"msg\":\"未登录或登录超时。请重新登录\"}");
        }
        // 是否开启记住我
        mmap.put("isRemembered", rememberMe);
        // 是否开启用户注册
        mmap.put("isAllowRegister", Convert.toBool(configService.getKey("sys.account.registerUser"), false));
        mmap.put("copyRight", configService.getKey("sys.common.copyright"));
        return "login";
    }

    @PostMapping("/login")
    @ResponseBody
    public AjaxResult ajaxLogin(String username, String password, Boolean rememberMe)
    {
        UsernamePasswordToken token = new UsernamePasswordToken(username, password, rememberMe);
        Subject subject = SecurityUtils.getSubject();
        try
        {
            subject.login(token);
            return success();
        }
        catch (AuthenticationException e)
        {
            String msg = "用户或密码错误";
            if (StringUtils.isNotEmpty(e.getMessage()))
            {
                msg = e.getMessage();
            }
            return error(msg);
        }
    }

//    @CrossOrigin(origins = {"*"})
    @PostMapping("/jwt/login")
    @ResponseBody
    public ResponseBodyApi<Map<String, String>> jwtLogin(HttpServletRequest request, @RequestParam(required = false) String username, @RequestParam(required = false) String password, @RequestParam(required = false) String validateCode, @RequestBody(required = false) Map<String, Object> paramMap) throws Exception {
        if (StringUtils.isEmpty(username) && StringUtils.isEmpty(password)) {
            if(paramMap.containsKey("username")) {
                username = paramMap.get("username").toString();
            }
            if(paramMap.containsKey("password")) {
                password = paramMap.get("password").toString();
            }
        }
        if(StringUtils.isEmpty(validateCode)){
            validateCode =  paramMap.get("validateCode").toString();
        }
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            return new ResponseBodyApi<>(false, "账号和密码不能为空");
        }
        if(StringUtils.isEmpty(validateCode)){
            return new ResponseBodyApi<>(false, "验证码不能为空");
        }

        String UserIp = IpUtils.getIpAddr(request);
        String appValidateCodeCacheKey = "appValidateCode_" + UserIp;
        Cache<String, String> appValidateCodeCache = getCacheManager().getCache(ShiroConstants.APP_CURRENT_VALIDATECODE);
        String serverValidateCode = appValidateCodeCache.get(appValidateCodeCacheKey);
        appValidateCodeCache.remove(appValidateCodeCacheKey);
        if (!validateCode.equalsIgnoreCase(serverValidateCode)){
            return new ResponseBodyApi<>(false, "验证码错误");
        }

        SysUser user = userService.selectUserByLoginName(username);
        if (user == null)
        {
            return new ResponseBodyApi<>(false, "用户不存在/密码错误!");
        }

        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            return new ResponseBodyApi<>(false, "对不起，您的账号已被删除!");
        }

        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            return new ResponseBodyApi<>(false, "用户已封禁，请联系管理员!");
        }
        String plainPwd = RsaUtils.decryptByPrivateKey(password);

        try{
            passwordService.validateAppClient(user, plainPwd, UserIp);
        }catch (Exception e){
            if(StringUtils.isNotEmpty(e.getMessage())){
                return new ResponseBodyApi<>(false, e.getMessage());
            }else{
                return new ResponseBodyApi<>(false, "用户不存在/密码错误!");
            }
        }

        String token = JwtUtils.createToken(username, user.getPassword());
        return new ResponseBodyApi<>(new HashMap<String, String>(){{
            put("token", token);
        }});
    }

    @PostMapping("/jwt/login_sso")
    @ResponseBody
    public ResponseBodyApi<Map<String, String>> jwtLoginSSO(HttpServletRequest request, @RequestParam(required = false) String accessToken, @RequestParam(required = false) String orgCode, @RequestParam(required = false) String userName, @RequestBody(required = false) Map<String, Object> paramMap) throws Exception {
        String finalAccessToken = accessToken;
        String finalOrgCode = orgCode;
        String finalUserName = userName;
        
        if(StringUtils.isEmpty(finalAccessToken)){
            if(paramMap.containsKey("accessToken")) {
                finalAccessToken = paramMap.get("accessToken").toString();
            }
        }
        if(StringUtils.isEmpty(finalOrgCode)){
            if(paramMap.containsKey("orgCode")) {
                finalOrgCode = paramMap.get("orgCode").toString();
            }
        }
        if(StringUtils.isEmpty(finalUserName)){
            if(paramMap.containsKey("userName")) {
                finalUserName = paramMap.get("userName").toString();
            }
        }
        if (StringUtils.isEmpty(finalAccessToken)) {
            return new ResponseBodyApi<>(false, "token不能为空");
        }
        if (StringUtils.isEmpty(finalOrgCode)) {
            return new ResponseBodyApi<>(false, "机构编码不能为空");
        }
        try{
            String realLoginName = passwordService.validateAppClientByMbsAuth(finalAccessToken, finalOrgCode);
            if(StringUtils.isEmpty(realLoginName)) {
                return new ResponseBodyApi<>(false, "服务端token校验失败，请重试");
            }else{
                SysUser user = userService.selectUserByUnionLoginName(realLoginName);
                if(user == null) {
                    user = userService.selectUserByLoginName(realLoginName);
                }
                if(user == null) {
                    return new ResponseBodyApi<>(false, "用户信息未配置!");
                }
                String token = JwtUtils.createToken(user.getLoginName(), user.getPassword());
                return new ResponseBodyApi<>(new HashMap<String, String>(){{
                    put("token", token);
                }});
            }
        }catch (Exception e){
            if(StringUtils.isNotEmpty(e.getMessage())){
                return new ResponseBodyApi<>(false, e.getMessage());
            }else{
                return new ResponseBodyApi<>(false, "服务端用户认证失败!");
            }
        }
    }   

    @PostMapping("/jwt/login_ajax")
    @ResponseBody
    public AjaxResult jwtLoginAjax(@RequestParam(required = false) String username, @RequestParam(required = false) String password, @RequestBody(required = false) Map<String, Object> paramMap)
    {
        if (StringUtils.isEmpty(username) && StringUtils.isEmpty(password)) {
            if(paramMap.containsKey("username")) {
                username = paramMap.get("username").toString();
            }
            if(paramMap.containsKey("password")) {
                password = paramMap.get("password").toString();
            }
        }
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            return AjaxResult.error("账号和密码不能为空!");
        }

        SysUser user = userService.selectUserByLoginName(username);
        if (user == null)
        {
            return AjaxResult.error("用户不存在/密码错误!");
        }

        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            return AjaxResult.error("对不起，您的账号已被删除!");
        }

        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            return AjaxResult.error("用户已封禁，请联系管理员!");
        }

        if (!passwordService.matches(user, password))
        {
            return AjaxResult.error("用户不存在/密码错误!");
        }

        String token = JwtUtils.createToken(username, user.getPassword());
        return AjaxResult.success("登录成功,请妥善保管您的token信息").put("token", token);
    }

    @GetMapping("/unauth")
    public String unauth()
    {
        return "error/unauth";
    }
    
    /**
     * OAuth2单点登录入口
     */
    @GetMapping("/sso/entry")
    public String ssoEntry(HttpServletRequest request, HttpServletResponse response, ModelMap mmap) {
        String code = request.getParameter("code");
        String state = request.getParameter("state");

        String baseUrl = ServletUriComponentsBuilder.fromCurrentContextPath()
            .build()
            .toUriString();

        String idpOauthSettings = sysConfigService.selectConfigByKey("idp.oauth.settings");
        if (StringUtils.isEmpty(idpOauthSettings)) {
            idpOauthSettings = "";
        }
        String[] arr = idpOauthSettings.split(",");
        String idpBaseUrl = arr.length > 0 ? arr[0] : "";
        String idpOauthClientId = arr.length > 1 ? arr[1] : "";

        String authorizeUrl = idpBaseUrl + "/idp/oauth2/authorize";

        if (StringUtils.isEmpty(code)) {
            String stateParam = UUID.randomUUID().toString();
            // 将state存储到session中，用于后续验证
            request.getSession().setAttribute("oauth_state", stateParam);
            String authUrl = String.format("%s?client_id=%s&redirect_uri=%s&response_type=code&state=%s",
                  authorizeUrl, idpOauthClientId, baseUrl + "/sso/callback", stateParam);
            try {
              response.sendRedirect(authUrl);
              return null;
            } catch (IOException e) {
              mmap.put("error", "认证服务器连接失败，请稍后重试");
              return "sso/index";
            }
        }

//        // 验证state参数
//        String sessionState = (String) request.getSession().getAttribute("oauth_state");
//        if (StringUtils.isEmpty(sessionState) || !sessionState.equals(state)) {
//          mmap.put("error", "认证状态验证失败，请重新登录");
//          return "sso/index";
//        }

        request.getSession().removeAttribute("oauth_state");

        String accessToken = "";
        try{
          accessToken = oAuth2Service.getTokenByCode(code);
          if (StringUtils.isEmpty(accessToken)) {
              mmap.put("error", "获取访问令牌失败");
              return "sso/index";
          }
        }catch (Exception e) {
          mmap.put("error", "认证服务器连接失败，请稍后重试");
        }

        String loginName = "";
        try{
          loginName = oAuth2Service.getUserInfo(accessToken);
          if (StringUtils.isEmpty(loginName)) {
              mmap.put("error", "统一平台获取用户信息失败");
              return "sso/index";
          }
        }catch (Exception e) {
          mmap.put("error", "统一平台获取用户信息失败");
          return "sso/index";
        }

        try {
          SysUser user = null;
          user = user = userService.selectUserByUnionLoginName(loginName);
          if (user == null) {
              user = userService.selectUserByLoginName(loginName);
          }
          if (user == null) {
              mmap.put("error", "暂时无权限访问");
              return "sso/index";
          }

          // 创建OAuth2认证token并登录
          OAuth2Token oauth2Token = new OAuth2Token(user.getLoginName(), accessToken, "统一平台");
          // oauth2Token.setUserInfo(userInfo);
          Subject subject = SecurityUtils.getSubject();
          try
          {
              subject.login(oauth2Token);
              response.sendRedirect("/index");
              return null;
          }
          catch (AuthenticationException e)
          {
              mmap.put("error", "认证过程中发生错误：" + e.getMessage());
              return "sso/index";
          }
        } catch (Exception e) {
          mmap.put("error", "认证过程中发生错误：" + e.getMessage());
          return "sso/index";
        }
    }
    
    /**
     * OAuth2回调处理（与entry相同，提供额外的回调端点）
     */
    @GetMapping("/sso/callback")
    public String ssoCallback(HttpServletRequest request, HttpServletResponse response, ModelMap mmap) {
        return ssoEntry(request, response, mmap);
    }
    
    /**
     * 获取OAuth2认证状态（AJAX接口）
     */
    @PostMapping("/sso/status")
    @ResponseBody
    public AjaxResult getSsoStatus(HttpServletRequest request) {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            SysUser user = (SysUser) subject.getPrincipal();
            return AjaxResult.success("已登录").put("user", user.getLoginName());
        } else {
            return AjaxResult.error("未登录");
        }
    }

    /**
     * OAuth2功能测试页面
     */
    @GetMapping("/sso/test")
    public String ssoTest() {
        return "sso/test";
    }
    
    /**
     * OAuth2Token测试接口
     */
    @PostMapping("/oauth2/test")
    @ResponseBody
    public AjaxResult testOAuth2Token(@RequestParam String username, 
                                     @RequestParam(required = false) String accessToken,
                                     @RequestParam(required = false) String password,
                                     @RequestParam(required = false) String source) {
        try {
            OAuth2Token oauth2Token;
            
            if (accessToken != null && !accessToken.trim().isEmpty()) {
                // 使用OAuth2访问令牌认证
                oauth2Token = new OAuth2Token(username, accessToken, source != null ? source : "测试");
            } else {
                // 使用用户名密码认证（兼容模式）
                oauth2Token = new OAuth2Token(username, password != null ? password : "");
                oauth2Token.setSource(source != null ? source : "密码认证");
            }
            
            Subject subject = SecurityUtils.getSubject();
            subject.login(oauth2Token);
            
            return AjaxResult.success("OAuth2Token认证成功")
                    .put("tokenType", "OAuth2Token")
                    .put("source", oauth2Token.getSource())
                    .put("isOAuth2Auth", oauth2Token.isOAuth2Auth());
                    
        } catch (Exception e) {
            return AjaxResult.error("OAuth2Token认证失败: " + e.getMessage());
        }
    }
}
