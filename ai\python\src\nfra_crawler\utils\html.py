from bs4 import BeautifulSoup

class HtmlUtils:
    """HTML工具类"""

    def remove_style_attributes(self, html):
        soup = BeautifulSoup(html, 'html.parser')
        # 移除所有style属性
        for tag in soup.find_all(True):
            if 'style' in tag.attrs:
                del tag.attrs['style']
        return soup

    def remove_attributes(self, html, attributes):
        soup = BeautifulSoup(html, 'html.parser')
        for tag in soup.find_all(True):
            for attribute in attributes:
                if attribute in tag.attrs:
                    del tag.attrs[attribute]
        return soup

    def remove_all_attributes(self, html):
        soup = BeautifulSoup(html, 'html.parser')
        for tag in soup.find_all(True):
            for attribute in tag.attrs:
                del tag.attrs[attribute]
        return soup

html_utils = HtmlUtils()