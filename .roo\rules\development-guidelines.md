---
description: 开发指导原则
globs: 
alwaysApply: false
---
# MetaOrg 开发指导原则

## 编码规范

### Java 编码规范
- 使用明确的变量命名，避免使用缩写
- 所有类和方法必须有完整的 JavaDoc 注释
- 异常处理必须完善，使用日志记录错误信息
- 服务层方法必须处理边界条件和空值检查
- 实体类使用 `@Entity` 注解，遵循 JPA 规范

### 前端编码规范
- HTML 模板使用 Thymeleaf 语法
- JavaScript 代码必须包含在 `<![CDATA[...]]>` 中
- 使用 Bootstrap 样式框架保持UI一致性
- 表格使用 bootstrap-table 插件
- 弹框使用 layer 插件

## 安全要求
- 所有控制器方法必须添加 Shiro 权限检查注解
- 用户输入必须进行 XSS 过滤
- SQL 查询使用 MyBatis 参数化防止注入
- 敏感操作记录操作日志

## 性能要求
- 列表查询必须支持分页
- 大数据量操作使用批处理
- 文件上传限制大小和类型
- 查询优化使用索引

## 测试要求
- 新功能必须编写单元测试
- 业务逻辑测试覆盖率不低于 80%
- 集成测试验证完整业务流程
- 性能测试验证系统负载能力

## 文档要求
- API 接口必须有完整文档
- 数据库表结构变更需要记录
- 部署配置需要详细说明
- 版本更新记录详细功能变化
