<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <th:block th:include="include :: header('干部调整情况统计列表')" />
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: ztree-css"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .select-table .table td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
        #selectedPeopleModal .ui-layout-content{
            min-height:300px;
        }
        #selectedPeopleModal .fixed-table-container{
            max-height:380px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa fa-sitemap"></i> 组织机构
                </div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i
                            class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i
                            class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i
                            class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <input name="treeId" type="hidden" id="treeId" th:value="${deptId}">
                <div id="tree" class="ztree"></div>
            </div>
        </div>
    </div>
    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" id="deptId" name="deptId" th:value="${deptId}">
                        <input type="hidden" id="parentId" name="parentId">
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>关键字：</label>
                                    <input type="text" name="params[keyword]" placeholder="请输入关键字，支持模糊查询" data-toggle="tooltip" data-placement="bottom" title="支持按机构名称模糊查询"/>
                                </li>
                                <li class="select-time">
                                    <label>调整日期：</label>
                                    <input type="text" class="time-input" id="statisticBeginTime" data-type="date"
                                           placeholder="开始日期" name="params[statisticBeginTime]" title="开始日期"/>
                                    <span>-</span>
                                    <input type="text" class="time-input" id="statisticEndTime" data-type="date"
                                           placeholder="结束日期" name="params[statisticEndTime]" title="结束日期"/>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="searchFormReset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="btn-group-sm" id="check-toolbar" role="group">
                    <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="library:position_adjust_statistic:remove">
                        <i class="fa fa-remove"></i> 删除
                    </a>
                    <a class="btn btn-info" onclick="$.table.importExcel('importTpl',400,250)" shiro:hasPermission="library:position_adjust_statistic:import">
                        <i class="fa fa-upload"></i> 导入
                    </a>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table" data-resizable="true" data-use-row-attr-func="true"></table>
                </div>
            </div>
        </div>
    </div>
    <!-- 导入区域 -->
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file"/>
                <div class="mt10 pt5">
                    <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
                </div>
                <font color="red" class="pull-left mt10">
                    提示：<br/>
                    1. 仅允许导入"xls"或"xlsx"格式文件！<br/>
                    2. 数据导入中，请耐心等待勿关闭本页面！
                </font>
            </div>
        </form>
    </script>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js"/>
    <th:block th:include="include :: ztree-js"/>
    <th:block th:include="include :: bootstrap-table-reorder-rows-js" />
    <th:block th:include="include :: bootstrap-table-resizable-js" />
    <th:block th:include="include :: bootstrap-table-fixed-columns-js" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:src="@{/js/moment.min.js}"></script>
    <script th:inline="javascript">
        /*<![CDATA[*/
        const removeFlag = /*[[${@permission.hasPermi('library:position_adjust_statistic:remove')}]]*/ false;
        const prefix = ctx + "library/position_adjust";
        const sysYesNo = /*[[${@dict.getType('sys_yes_no')}]]*/ [];
        const checkCategory = /*[[${@dict.getType('biz_person_check_category')}]]*/ [];
        const checkResult = /*[[${@dict.getType('biz_person_check_result')}]]*/ [];
       
        const statisticBeginTime = moment().subtract(1, 'year').format('YYYY-MM-DD');
        const statisticEndTime = moment().format('YYYY-MM-DD');
        
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
            $("#statisticBeginTime").val(statisticBeginTime);
            $("#statisticEndTime").val(statisticEndTime);
            let panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 255 });
            // 回到顶部绑定
            if ($.fn.toTop !== undefined) {
                const opt = {
                    win:$('.ui-layout-center'),
                    doc:$('.ui-layout-center')
                };
                $('#scroll-up').toTop(opt);
            }

            $(document).on('keypress', function(e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    $.table.search();
                }
            });
        });

        $(function() {
            const options = {
                toolbar: "check-toolbar",
                url: prefix + "/statistic/list",
                importUrl: prefix + "/statistic/import",
                importTemplateUrl: prefix + "/statistic/template",
                removeUrl: prefix + "/statistic/remove",
                modalName: "干部调整情况统计",
                showToggle: false,
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    {
                        checkbox: true
                    },
                    {
                        field: 'id',
                        title: 'ID',
                        visible: false
                    },
                    {
                        field: 'deptId',
                        title: '部门ID',
                        visible: false
                    },
                    {
                        field: 'deptName',
                        title: '机构名称'
                    },
                    {
                        field: 'statisticDate',
                        title: '开始日期',
                        formatter: function(value, row, index) {
                            return moment(value).format('YYYY-MM-DD');
                        }
                    },
                    {
                        field: 'adjustDetails',
                        title: '调整详情',
                        formatter: function(value, row, index) {
                            if (!value|| value.length === 0) {
                                return '-';
                            }
                            const adjustList = value.map(item => {
                                return `${item.name}: ${item.value}人`;
                            });
                            return adjustList.join(', ');
                        }
                    },
                    {
                        field: 'adjustDetails',
                        title: '调整总数量',
                        formatter: function(value, row, index) {
                            if (!value|| value.length === 0) {
                                return '-';
                            }
                            let adjustCount = 0;
                            value.forEach(item => {
                                adjustCount += item.value;
                            });
                            return adjustCount;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
            queryDeptTree();
        });
        
        // 组织树数据绑定
        function queryDeptTree() {
            const url = ctx + "system/user/deptTreeData";
            const options = {
                url: url,
                expandLevel: 1,
                onClick: zOnClick
            };
            $.tree.init(options);

            function zOnClick(event, treeId, treeNode) {
                $("#deptId").val(treeNode.id);
                $("#parentId").val(treeNode.pId);
                $.table.search();
            }
        }
        
        // 重置筛选条件
        function searchFormReset() {
            $("#formId")[0].reset();
            $("#statisticBeginTime").val(statisticBeginTime);
            $("#statisticEndTime").val(statisticEndTime);
            $(".selectpicker").val("");
            $(".selectpicker").selectpicker('refresh');
            $.table.search();
        }

        $('#btnExpand').click(function() {
            $._tree.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function() {
            $._tree.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        $('#btnRefresh').click(function() {
            queryDeptTree();
        });
        /*]]>*/
    </script>
</body>
</html>