package com.metaorg.web.test;

import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.web.controller.tool.POIUtil;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class Excel {
    public static void main(String[] args) throws IOException {
//        File file = new File("./config/gbmc_template.xls").getAbsoluteFile();
//
//        FileInputStream fileInputStream = null;
//        FileOutputStream fileOutputStream = null;
//
//        try {
//            fileInputStream = new FileInputStream(file);
//            //通过流的方式读取文件
//            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
//            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);//.getSheet("Sheet1");
//            POIUtil.copyRows(hssfWorkbook, 0, 0, 3, 3, 4);
//            POIUtil.removeRow(hssfSheet, 3);
//
//            //通过sheet的名字来获取数据
////        HSSFSheet hssfSheet1 = xssfWorkbook.getSheetAt(0);
////        String[] biaoti = new String[]{"姓名", "职业", "年龄"};
////
////        Row row = hssfSheet.createRow(4);
////        for (int i = 0; i < biaoti.length; i++) {
////            row.createCell(i).setCellValue(biaoti[i]);
////        }
////        row = hssfSheet.createRow(5);
////        for (int i = 0; i < biaoti.length; i++) {
////            row.createCell(i).setCellValue(biaoti[i]);
////        }
////        row = hssfSheet.createRow(6);
////        for (int i = 0; i < biaoti.length; i++) {
////            row.createCell(i).setCellValue(biaoti[i]);
////        }
//            //添加上文件的路径，组成文件的绝对路径
//            File saveFile = new File("./outputs/gbmc_output.xls").getAbsoluteFile();
//            fileOutputStream = new FileOutputStream(saveFile);
//            hssfWorkbook.write(fileOutputStream);
//            //通过流的方式写入到文件中中
////            fileOutputStream.close();
//        } catch (IOException e) {
//
//        }finally {
//            FileUtils.closeFile(fileInputStream);
//            FileUtils.closeFile(fileOutputStream);
//        }
    }
}
