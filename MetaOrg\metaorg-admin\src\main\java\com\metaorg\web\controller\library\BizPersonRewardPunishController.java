package com.metaorg.web.controller.library;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.github.pagehelper.util.StringUtil;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.exception.ServiceException;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.library.domain.*;
import com.metaorg.library.service.*;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.utils.DataScopeUtils;
import com.metaorg.web.controller.tool.POIUtil;
import org.apache.commons.collections4.ListUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import com.metaorg.library.service.RabbitService;

/**
 * 奖惩情况Controller
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/reward_punish")
public class BizPersonRewardPunishController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(BizPersonRewardPunishController.class);

    private String prefix = "library/reward_punish";

    @Autowired
    private RabbitService rabbitService;

    @Autowired
    private IBizPersonRewardPunishService bizPersonRewardPunishService;

    @Autowired
    private IBizPersonService bizPersonService;

    @Autowired
    private IBizPersonEducationService educationService;

    @Autowired
    private IBizPersonPoliticsService politicsService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IBizPersonResumeService resumeService;

    @Autowired
    private  IBizPersonPositionService positionService;

    @RequiresPermissions("library:reward_punish:view")
    @GetMapping()
    public String reward_punish() {
        return prefix + "/reward_punish";
    }

    @RequiresPermissions("library:punish:view")
    @GetMapping("punish")
    public String punish(ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        return prefix + "/punish";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizPersonRewardPunish bizPersonRewardPunish, Map<String, Object> paramMap) {
        long deptId = -1;
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonRewardPunish.getParams().put("deptId", deptId);
        }
        String rewardPunishCategory = "";
        if (paramMap.get("rewardPunishCategory") != null && paramMap.get("rewardPunishCategory") != "") {
            rewardPunishCategory = paramMap.get("rewardPunishCategory").toString();
            bizPersonRewardPunish.getParams().put("rewardPunishCategory", rewardPunishCategory);
        }

        if (paramMap.get("rewardPunishCategoryList") != null && paramMap.get(("rewardPunishCategoryList")) != "") {
            bizPersonRewardPunish.getParams().put("rewardPunishCategoryList", Convert.toStrArray(paramMap.get("rewardPunishCategoryList").toString()));
        }
        
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonRewardPunish, getSysUser(), "d", "", "", deptId);
        }
    }

    /**
     * 查询奖惩情况列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonRewardPunish bizPersonRewardPunish, @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizPersonRewardPunish, paramMap);
        startPage();
        List<BizPersonRewardPunish> list = bizPersonRewardPunishService.selectBizPersonRewardPunishList(bizPersonRewardPunish);
        return getDataTable(list);
    }

    /**
     * 查询惩处情况列表
     */
    @RequiresPermissions("library:punish:list")
    @PostMapping("/punish_list")
    @ResponseBody
    public TableDataInfo punishList(BizPersonRewardPunish bizPersonRewardPunish, @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizPersonRewardPunish, paramMap);
        startPage();
        List<BizPersonRewardPunish> list = bizPersonRewardPunishService.selectBizPersonRewardPunishList(bizPersonRewardPunish);
        return getDataTable(list);
    }

    /**
     * 新增惩处情况
     */
    @RequiresPermissions("library:punish:add")
    @PostMapping("/punish_add")
    @ResponseBody
    public AjaxResult punishAdd(BizPersonRewardPunish bizPersonRewardPunish, @RequestParam Map<String, Object> paramMap) {
        // 根据姓名和身份证查找人员标识
        BizPerson person = bizPersonRewardPunish.getPerson();
        if (person == null || StringUtil.isEmpty(person.getName()) || StringUtil.isEmpty(person.getCitizenId())) {
            return error("人员姓名和身份证号码不能为空！");
        }

        // 根据身份证获取人员信息
        BizPerson bizPersonSearch = new BizPerson();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonSearch, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }

        HashSet<String> citizenIdSet = new HashSet<>();
        citizenIdSet.add(person.getCitizenId());
        bizPersonSearch.getParams().put("citizenIds", citizenIdSet.toArray());

        // 数据范围需要加上现任机构的
        List<BizPerson> personList = bizPersonService.selectBizPersonListByCitizenIds(bizPersonSearch);
        if (personList.isEmpty()) {
            return error("找不到身份证号码为【" + person.getCitizenId() + "】的人员信息！");
        }else if(!personList.get(0).getName().equals(person.getName())){
            return error("找不到【" + person.getName() + "、" + person.getCitizenId() + "】的人员信息！");
        }

        String personId = personList.get(0).getId();

        if(StringUtils.isNotEmpty(bizPersonRewardPunish.getRewardPunishDocNo())){
            bizPersonRewardPunish.setRewardPunishDocNo(bizPersonRewardPunish.getRewardPunishDocNo().trim());
        }
        // 查询是否有重复的奖惩文件号
        BizPersonRewardPunish searchPersonRewardPunish = new BizPersonRewardPunish();
        String rewardPunishDocNo = bizPersonRewardPunish.getRewardPunishDocNo();
        searchPersonRewardPunish.setPersonId(personId);
        searchPersonRewardPunish.setRewardPunishDocNo(rewardPunishDocNo);
        List<BizPersonRewardPunish> searchResult = bizPersonRewardPunishService.selectBizPersonRewardPunishList(searchPersonRewardPunish);
        if (!searchResult.isEmpty()) {
            return error("处罚处分文件号【" + rewardPunishDocNo + "】已存在，请勿重复添加！");
        }

        Date currentDate = DateUtils.getNowDate();
        bizPersonRewardPunish.setCreateTime(currentDate);
        bizPersonRewardPunish.setUpdateTime(currentDate);
        bizPersonRewardPunish.setPersonId(personId);

        bizPersonRewardPunish.setCreateById(getUserId().toString());
        bizPersonRewardPunish.setCreateBy(getLoginName());
        bizPersonRewardPunish.setUpdateById(getUserId().toString());
        bizPersonRewardPunish.setUpdateBy(getLoginName());

        bizPersonRewardPunish.setOrderNum(bizPersonRewardPunishService.getMaxOrderNum(personId));

        String rewardPunishId = bizPersonRewardPunishService.insertBizPersonRewardPunishReturnID(bizPersonRewardPunish);
        return toAjax(StringUtils.isEmpty(rewardPunishId) ? 0 : 1);
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

/**
     * 下载惩罚模板
     */
    @GetMapping("/importTemplate/punish")
    @ResponseBody
    public AjaxResult importTemplatePunish()
    {
        List<BizPersonPunishTplVM> list = new ArrayList<>();
        BizPersonPunishTplVM demo1 = new BizPersonPunishTplVM();
        BizPersonPunishTplVM demo2 = new BizPersonPunishTplVM();

//        String punishCategory = "";
//        List<SysDictData> catDicts = DictUtils.getDictCache("biz_person_punish_category");
//        if(catDicts != null && catDicts.size() > 0) {
//            punishCategory =  catDicts.get(0).getDictValue();
//        }

        demo1.setName("谢小娜");
        demo1.setCitizenId("110101199003078830");
//        demo1.setPunishCategory(punishCategory);
        demo1.setRewardPunishDay("2025-01-01");
        demo1.setRewardPunishEndDay("2025-05-01");
        demo1.setRewardPunishReason("未在贷审会中提示风险、发表正确意见");
        demo1.setRewardPunishPosition("董事长");
        demo1.setRewardPunishOrg("信用社联合社");
        demo1.setRewardPunishName("警告处分");
        demo1.setRewardPunishDocNo("信用社(2025)175号");
        
        demo2.setName("谢小锋");
        demo2.setCitizenId("422244199002025337");
//        demo2.setPunishCategory(punishCategory);
        demo2.setRewardPunishDay("2025-01-05");
        demo2.setRewardPunishEndDay("2025-05-05");
        demo2.setRewardPunishReason("未在贷审会中提示风险、发表正确意见");
        demo2.setRewardPunishPosition("行长");
        demo2.setRewardPunishOrg("监督管理总局");
        demo2.setRewardPunishName("警告处分，罚款1000");
        demo2.setRewardPunishDocNo("监管局(2025)176号");
        list.add(demo1);
        list.add(demo2);
        ExcelUtil<BizPersonPunishTplVM> util = new ExcelUtil<BizPersonPunishTplVM>(BizPersonPunishTplVM.class);
        return util.exportExcel(list, "惩处导入模板");
    }

    /**
     * 导出惩处人员列表
     */
    @RequiresPermissions("library:punish:export")
    @Log(title = "奖惩情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export/punish")
    @ResponseBody
    public AjaxResult exportPunish(BizPersonRewardPunish bizPersonRewardPunish, @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizPersonRewardPunish, paramMap);
        bizPersonRewardPunish.setRewardPunishCategory("惩处");
        bizPersonRewardPunish.getParams().put("orderBy", "a.reward_punish_day desc,a.id desc");

        List<BizPersonRewardPunish> list = bizPersonRewardPunishService.selectBizPersonRewardPunishList(bizPersonRewardPunish);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "处分花名册_" + System.currentTimeMillis() + ".xls";

        try {
            List<BizPerson> personList = new ArrayList<>();
            for(BizPersonRewardPunish pp : list){
                personList.add(pp.getPerson());
            }
            positionService.fillPositionForPersons(personList);

            File file = new File("./config/template/chufen_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            //通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            for (int i = 0; i < list.size(); i++) {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                row.getCell(0).setCellValue(i + 1);  //序号
                row.getCell(1).setCellValue(list.get(i).getPerson().getDept().getDeptName());  //行社名称
                row.getCell(2).setCellValue(list.get(i).getPerson().getJobUnit());   //职务
                row.getCell(3).setCellValue(list.get(i).getPerson().getName());   //姓名
                row.getCell(4).setCellValue(list.get(i).getPerson().getSex());   //性别
                row.getCell(5).setCellValue(list.get(i).getPerson().getNation());   ///民族
                //出生年月
                String bitthDay = list.get(i).getPerson().getBirthday();
                row.getCell(6).setCellValue(StringUtils.isEmpty(bitthDay) ? "" : bitthDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(7).setCellValue(list.get(i).getPerson().getAge().replace("岁", ""));   //年龄
                row.getCell(8).setCellValue(list.get(i).getPerson().getNativePlace());   //籍贯

                BizPersonEducation education = new BizPersonEducation();  //学历对象
                education.setPersonId(list.get(i).getPersonId());
                List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);
                BizPersonEducation highestEducation = null; //最高学历
                String ZuiGaoXueLi = "";  //最高学历
                // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum)); //正序
                if (educations != null) {
                    for (BizPersonEducation item : educations) {
                        //根据是否最高学历标识获取最高学历对象
                        if (item.getHighestDegreeFlag().equalsIgnoreCase("Y")) {
                            highestEducation = item;
                        }
                    }
                    //如果没有标识最高学历获取最后一个
                    if (highestEducation == null && educations.size() > 0) {
                        highestEducation = educations.get(educations.size() - 1);
                    }
                    if (highestEducation != null) {
                        ZuiGaoXueLi = highestEducation.getEduName();
                    }
                }
                row.getCell(9).setCellValue(ZuiGaoXueLi);   //最高学历
                //通过政治情况获取入党时间
                BizPersonPolitics politics = new BizPersonPolitics();
                politics.setPersonId(list.get(i).getPersonId());
                List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);
                BizPersonPolitics bizPersonPolitics = null;
                if (politicsList != null) {
                    bizPersonPolitics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
                        @Override
                        public boolean test(BizPersonPolitics bizPersonPolitics) {
                            return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
                        }
                    }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
                        @Override
                        public BizPersonPolitics get() {
                            return new BizPersonPolitics();
                        }
                    });
                }
                //入党时间
                if (bizPersonPolitics != null && bizPersonPolitics.getJoinPartyDay() != null) {
                    row.getCell(10).setCellValue(bizPersonPolitics.getJoinPartyDay().replace("-", ".").replace("Invalid date", ""));
                } else {
                    row.getCell(10).setCellValue("");
                }
                //参加工作时间
                row.getCell(11).setCellValue(list.get(i).getPerson().getJobDay() == null ? "" : list.get(i).getPerson().getJobDay().replace("-", ".").replace("Invalid date", ""));

                //根据最新职职务级职务获取
                if(list.get(i).getPerson() != null && list.get(i).getPerson().getPositions() !=null
                        && list.get(i).getPerson().getPositions().size() > 0){
                    BizPersonPosition personPosition = positionService.mergePositionsIntoSingle(list.get(i).getPerson().getPositions());
                    row.getCell(12).setCellValue(personPosition.getPositionDay().replace("-", "."));
                    row.getCell(13).setCellValue(personPosition.getSameRankDay().replace("-", "."));
                } else {
                    //从简历中获取最新任职时间
                    BizPersonResume resume = new BizPersonResume();
                    resume.setPersonId(list.get(i).getPerson().getId());
                    List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume, "start_date desc");
                    BizPersonResume lastResume = null;
                    if (resumes != null) {
                        lastResume = resumes.stream().findFirst().orElseGet(new Supplier<BizPersonResume>() {
                            @Override
                            public BizPersonResume get() {
                                return new BizPersonResume();
                            }
                        });
                    }
                    if (lastResume != null && lastResume.getStartDate() != null) {
                        row.getCell(12).setCellValue(lastResume.getStartDate().replace("-", ".").replace("Invalid date", ""));
                    } else {
                        row.getCell(12).setCellValue("");
                    }
                    row.getCell(13).setCellValue("");
                }

                //处分时间
                String rewardPunishDay = list.get(i).getRewardPunishDay();
                row.getCell(14).setCellValue(StringUtils.isEmpty(rewardPunishDay) ? "" : rewardPunishDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(15).setCellValue(list.get(i).getPerson().getQualificationJob());  //专业技术职务
                if (highestEducation != null) {
                    //毕业日期
                    String BiYeShiJian = highestEducation.getGraduationDay().replace("-", ".").replace("Invalid date", "");
                    row.getCell(16).setCellValue(BiYeShiJian);  //毕业时间
                    row.getCell(17).setCellValue(highestEducation.getEduSchool());  //毕业院校
                    row.getCell(18).setCellValue(highestEducation.getMajorName());  //专业
                    row.getCell(19).setCellValue(highestEducation.getDegreeCode());  //学位
                } else {
                    row.getCell(16).setCellValue("");  //毕业时间
                    row.getCell(17).setCellValue("");  //毕业院校
                    row.getCell(18).setCellValue("");  //专业
                    row.getCell(19).setCellValue("");  //学位
                }
                row.getCell(20).setCellValue(list.get(i).getPerson().getCitizenId());  //身份证号码
                row.getCell(21).setCellValue(list.get(i).getRewardPunishOrg());  //处分机构
//                row.getCell(22).setCellValue(list.get(i).getPunishCategory());  //处分类别
                String rewardPunishEndDay = list.get(i).getRewardPunishEndDay();
                row.getCell(22).setCellValue(StringUtils.isEmpty(rewardPunishEndDay) ? "" : rewardPunishEndDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(23).setCellValue(list.get(i).getRewardPunishDetail());  //处分详情
                row.getCell(24).setCellValue(list.get(i).getRewardPunishReason());  //处分事由
                row.getCell(25).setCellValue(list.get(i).getRewardPunishNote());  //处分备注
            }

            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导出惩处两次以上人员列表
     */
    @RequiresPermissions("library:punish:export")
    @Log(title = "奖惩情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export/punish_more_times")
    @ResponseBody
    public AjaxResult exportPunishMoreTimes(BizPersonRewardPunish bizPersonRewardPunish, @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizPersonRewardPunish, paramMap);
        bizPersonRewardPunish.setRewardPunishCategory("惩处");
        bizPersonRewardPunish.getParams().put("punishTimes", 2);
        bizPersonRewardPunish.getParams().put("orderBy", "a.person_id asc,a.reward_punish_day asc");
        List<BizPersonRewardPunish> list = bizPersonRewardPunishService.selectBizPersonRewardPunishList(bizPersonRewardPunish);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "处罚累计两次以上_" + System.currentTimeMillis() + ".xls";

        try {
            File file = new File("./config/template/chufentwice_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            //通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);

            int mergedStart = 3;
            int mergedEnd = 3;
            int listSize = list.size();
            String curPersonId = "";
            int orderNumber = 1;
            if (listSize > 0) curPersonId = list.get(0).getPersonId();
            for (int i = 0; i < listSize; i++) {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                String personId = list.get(i).getPersonId();
                if (!curPersonId.equals(personId) || i == listSize - 1) {
                    if (i == listSize - 1) mergedEnd++;
                    if (mergedStart != mergedEnd) {
                        CellRangeAddress region = new CellRangeAddress(mergedStart, mergedEnd, 0, 0);
                        CellRangeAddress region1 = new CellRangeAddress(mergedStart, mergedEnd, 1, 1);
                        CellRangeAddress region2 = new CellRangeAddress(mergedStart, mergedEnd, 2, 2);
                        CellRangeAddress region3 = new CellRangeAddress(mergedStart, mergedEnd, 3, 3);
                        hssfSheet.addMergedRegion(region);
                        hssfSheet.addMergedRegion(region1);
                        hssfSheet.addMergedRegion(region2);
                        hssfSheet.addMergedRegion(region3);
                    }
                    curPersonId = personId;
                    mergedStart = 3 + i;
                    orderNumber++;
                }

                row.getCell(0).setCellValue(orderNumber);  //序号
                row.getCell(1).setCellValue(list.get(i).getPerson().getName());   //姓名
                row.getCell(2).setCellValue(list.get(i).getPerson().getDept().getDeptName());  //行社名称
                row.getCell(3).setCellValue(list.get(i).getPerson().getJobUnit());   //职务
                row.getCell(4).setCellValue(list.get(i).getRewardPunishOrg());  //处分机构
                //处分时间
                String rewardPunishDay = list.get(i).getRewardPunishDay();
                row.getCell(5).setCellValue(StringUtils.isEmpty(rewardPunishDay) ? "" : rewardPunishDay.replace("-", ".").replace("Invalid date", ""));
                String rewardPunishEndDay = list.get(i).getRewardPunishEndDay();
                row.getCell(6).setCellValue(StringUtils.isEmpty(rewardPunishEndDay) ? "" : rewardPunishEndDay.replace("-", ".").replace("Invalid date", ""));
//                row.getCell(5).setCellValue(list.get(i).getPunishCategory());  //处分类别
                row.getCell(7).setCellValue(list.get(i).getRewardPunishReason());  //处分事由
                row.getCell(8).setCellValue(list.get(i).getRewardPunishNote());  //处分备注
                mergedEnd = 3 + i;
            }

            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 新增奖惩情况
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap) {
        mmap.put("personId", personId);
        mmap.put("selCategory", "");
        mmap.put("maxOrderNum", bizPersonRewardPunishService.getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增惩处情况
     */
    @GetMapping("/add_punish")
    public String add_punish(ModelMap mmap) {
        mmap.put("selCategory", "");
        return prefix + "/add_punish";
    }

    /**
     * 新增保存奖惩情况
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "奖惩情况", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "奖惩情况", tableName = PersonTable.BIZ_PERSON_REWARD_PUNISH, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonRewardPunish bizPersonRewardPunish) {
        // 查询是否有重复的奖惩文件号
        if(StringUtils.isNotEmpty(bizPersonRewardPunish.getRewardPunishDocNo())){
            bizPersonRewardPunish.setRewardPunishDocNo(bizPersonRewardPunish.getRewardPunishDocNo().trim());
        }
        String rewardPunishDocNo = bizPersonRewardPunish.getRewardPunishDocNo();
        if (!StringUtils.isEmpty(rewardPunishDocNo)) {
            BizPersonRewardPunish searchPersonRewardPunish = new BizPersonRewardPunish();
            searchPersonRewardPunish.setPersonId(bizPersonRewardPunish.getPersonId());
            searchPersonRewardPunish.setRewardPunishDocNo(rewardPunishDocNo);
            List<BizPersonRewardPunish> searchResult = bizPersonRewardPunishService.selectBizPersonRewardPunishList(searchPersonRewardPunish);
            if (!searchResult.isEmpty()) {
                return error("文件号【" + rewardPunishDocNo + "】已存在，请勿重复添加！");
            }
        }

        Date currentDate = DateUtils.getNowDate();
        bizPersonRewardPunish.setCreateTime(currentDate);
        bizPersonRewardPunish.setUpdateTime(currentDate);

        bizPersonRewardPunish.setCreateById(getUserId().toString());
        bizPersonRewardPunish.setCreateBy(getLoginName());
        bizPersonRewardPunish.setUpdateById(getUserId().toString());
        bizPersonRewardPunish.setUpdateBy(getLoginName());

        String rewardPunishId = bizPersonRewardPunishService.insertBizPersonRewardPunishReturnID(bizPersonRewardPunish);
        AjaxResult result = toAjax(StringUtils.isEmpty(rewardPunishId) ? 0 : 1);
        if (result.isSuccess()) {
            BizPerson bizPerson = bizPersonService.selectBizPersonById(bizPersonRewardPunish.getPersonId());
            if (bizPerson != null) {
                result.put("data", bizPerson.getRewardPunishContent());
            }
        }
        return result;
    }

    /**
     * 修改奖惩情况
     */
    @RequiresPermissions(value = {"library:person:edit", "library:punish:edit"}, logical = Logical.OR)
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPersonRewardPunish bizPersonRewardPunish = bizPersonRewardPunishService.selectBizPersonRewardPunishById(id);
        mmap.put("bizPersonRewardPunish", bizPersonRewardPunish);
        return prefix + "/edit";
    }

    /**
     * 修改保存奖惩情况
     */
    @RequiresPermissions(value = {"library:person:edit", "library:punish:edit"}, logical = Logical.OR)
    @Log(title = "奖惩情况", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "奖惩情况", tableName = PersonTable.BIZ_PERSON_REWARD_PUNISH, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonRewardPunish bizPersonRewardPunish) {
        String personId = bizPersonRewardPunish.getPersonId();
        if (StringUtils.isEmpty(personId)) {
            return error("找不到身份证号码为的人员信息！");
        }

        if(StringUtils.isNotEmpty(bizPersonRewardPunish.getRewardPunishDocNo())){
            bizPersonRewardPunish.setRewardPunishDocNo(bizPersonRewardPunish.getRewardPunishDocNo().trim());
        }
        // 查询是否有重复的奖惩文件号
        String rewardPunishDocNo = bizPersonRewardPunish.getRewardPunishDocNo();
        if (!StringUtils.isEmpty(rewardPunishDocNo)) {
            BizPersonRewardPunish searchPersonRewardPunish = new BizPersonRewardPunish();

            List<String> excludeIds = new ArrayList<>();
            excludeIds.add(bizPersonRewardPunish.getId());
            searchPersonRewardPunish.setPersonId(personId);
            searchPersonRewardPunish.getParams().put("excludeIds", excludeIds);
            searchPersonRewardPunish.setRewardPunishDocNo(rewardPunishDocNo);

            List<BizPersonRewardPunish> searchResult = bizPersonRewardPunishService
                    .selectBizPersonRewardPunishList(searchPersonRewardPunish);
            if (!searchResult.isEmpty()) {
                return error("文件号【" + rewardPunishDocNo + "】已存在，请勿重复添加！");
            }
        }

        bizPersonRewardPunish.setUpdateTime(DateUtils.getNowDate());

        bizPersonRewardPunish.setUpdateById(getUserId().toString());
        bizPersonRewardPunish.setUpdateBy(getLoginName());

        AjaxResult result = toAjax(bizPersonRewardPunishService.updateBizPersonRewardPunish(bizPersonRewardPunish));
        if (result.isSuccess()) {
            BizPerson bizPerson = bizPersonService.selectBizPersonById(bizPersonRewardPunish.getPersonId());
            if (bizPerson != null) {
                result.put("data", bizPerson.getRewardPunishContent());
            }
        }
        return result;
    }

    /**
     * 导入惩处数据
     */
    @Log(title = "奖惩情况", businessType = BusinessType.IMPORT)
    @RequiresPermissions("library:punish:add")
    @PersonOperationLog(module = "奖惩情况", tableName = PersonTable.BIZ_PERSON_REWARD_PUNISH, operationType = PersonOperationType.IMPORT)
    @PostMapping("/importData/punish")
    @ResponseBody
    public AjaxResult importDataPunish(MultipartFile file) throws Exception
    {
        ExcelUtil<BizPersonPunishTplVM> util = new ExcelUtil<>(BizPersonPunishTplVM.class);
        List<BizPersonPunishTplVM> punishList = util.importExcel(file.getInputStream());
        String message = importPunish(punishList);
        return AjaxResult.success(message);
    }

    /**
     * 删除奖惩情况
     */
    @RequiresPermissions(value = {"library:person:edit", "library:punish:remove"}, logical = Logical.OR)
    @Log(title = "奖惩情况", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "奖惩情况", tableName = PersonTable.BIZ_PERSON_REWARD_PUNISH, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        String[] deleteIds = Convert.toStrArray(ids);
        if (deleteIds.length == 0) {
            return error("请选择要删除的奖惩情况！");
        }

        BizPersonRewardPunish bizPersonRewardPunish = bizPersonRewardPunishService.selectBizPersonRewardPunishById(deleteIds[0]);
        if (bizPersonRewardPunish == null) {
            return error("找不到奖惩情况！");
        }

        AjaxResult result = toAjax(bizPersonRewardPunishService.deleteBizPersonRewardPunishByIds(ids));
        if (result.isSuccess()) {
            BizPerson bizPerson = bizPersonService.selectBizPersonById(bizPersonRewardPunish.getPersonId());
            if (bizPerson != null) {
                result.put("data", bizPerson.getRewardPunishContent());
            }
        }

        return result;
    }

    /**
     * 排序奖惩情况
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "奖惩情况", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++) {
            BizPersonRewardPunish bizPersonRewardPunish = bizPersonRewardPunishService.selectBizPersonRewardPunishById(idsArray[i]);
            bizPersonRewardPunish.setOrderNum(Convert.toLong(i + 1));
            bizPersonRewardPunishService.updateBizPersonRewardPunish(bizPersonRewardPunish);
        }
        return success();
    }

    // 重新生成人员任免表奖惩情况
    private String generateNewRewardPunishContent(String oldPersonRbText, List<BizPersonRewardPunish> list, List<BizPersonRewardPunish> delList){
        if (oldPersonRbText == null) oldPersonRbText = "";
        String newPersonRbText = oldPersonRbText;

        if (delList == null) delList = new ArrayList<>();  //更新前奖惩信息
        if (list == null) list = new ArrayList<>();
        List<BizPersonRewardPunish> allDelList = ListUtils.union(list, delList);

        // 按照rewardPunishToString()方法返回的字符串长度进行降序排序
        allDelList.sort((params1, params2) -> {
            Integer len1 = params1.rewardPunishToString().length();
            Integer len2 = params2.rewardPunishToString().length();
            return -len1.compareTo(len2);
        });

        // 删除所有需要删除的奖惩情况（后面重新生成）
        for (BizPersonRewardPunish item : allDelList) {
            String rbString = item.rewardPunishToString();
            if (StringUtils.isNotEmpty(rbString)) {
                newPersonRbText = newPersonRbText.replace(rbString, "");
            }
        }

        // 根据rewardPunishToString去重
        List<BizPersonRewardPunish> uniqueList = list.stream()
                .collect(Collectors.toMap(BizPersonRewardPunish::rewardPunishToString, p -> p, (p1, p2) -> p1))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 按照日期正序排序
        uniqueList.sort(Comparator.comparing(BizPersonRewardPunish::getRewardPunishDay));
        for (BizPersonRewardPunish item : uniqueList) {
            String rbString = item.rewardPunishToString();
            if (StringUtils.isNotEmpty(rbString)) {
                newPersonRbText = newPersonRbText + rbString;
            }
        }
        // 去掉开头为“无”的字符串（一般是在初始化时导入的）
        if (!(newPersonRbText == "无" || newPersonRbText == "（无）" || newPersonRbText == "(无)")) {
            newPersonRbText = newPersonRbText.replaceFirst("^(无|（无）|\\(无\\))", "");
        }

        //最多552个字符
        if (newPersonRbText.length() > 552 && newPersonRbText.length() > oldPersonRbText.length()) {
            newPersonRbText = oldPersonRbText;
        }

        return newPersonRbText;
    }

    private String importPunish(List<BizPersonPunishTplVM> punishList){
        if (StringUtils.isNull(punishList) || punishList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 导入数据检查
        Boolean isValid = checkImportData(punishList);

        // 获取奖惩最大序号
        HashSet<String> personIdSet = new HashSet<>();  // 人员ID
        HashMap<String, Long> maxOrderNumMap = new HashMap<>();  // 奖惩最大序号
        if(isValid){
            for (BizPersonPunishTplVM pvm : punishList) {
                personIdSet.add(pvm.getPerson().getId());
            }
            // 根据人员ID获取每一个人的最大奖惩情况序号
            List<Map<String, Object>> postMaxOrderNumList = bizPersonRewardPunishService.selectBizPersonRewardPunishMaxOrderNums(personIdSet.toArray(new String[0]));
            for (Map<String, Object> maxOrderNum : postMaxOrderNumList) {
                maxOrderNumMap.put(maxOrderNum.get("person_id").toString(), Long.valueOf(maxOrderNum.get("order_num").toString()));
            }
        }

        // 成功入库的记录，逐个更新太慢，后面批量更新
        List<BizPersonRewardPunish> successRewardPunishs = new ArrayList<>();

        Map<String, Integer> successPersonMap = new HashMap<>();

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        for (BizPersonPunishTplVM pvm : punishList) {
            if(!isValid){ //数据格式不正确，全部不入库
                if(StringUtils.isNotEmpty(pvm.getErrMsg())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
            }else{
                try {
//                    pvm.setPunishCategory(DictUtils.getDictLabel("biz_person_punish_category", pvm.getPunishCategory()));

                    BizPersonRewardPunish rewardPunish = BizPersonRewardPunish.fromBizPersonPunishTplVM(pvm);

                    rewardPunish.setCreateById(createUserId);
                    rewardPunish.setCreateBy(createUserName);
                    rewardPunish.setUpdateById(createUserId);
                    rewardPunish.setUpdateBy(createUserName);
                    rewardPunish.setCreateTime(currentDate);
                    rewardPunish.setUpdateTime(currentDate);

                    rewardPunish.setPersonId(pvm.getPerson().getId());  // 人员ID
                    // 奖惩情况序号
                    if(!maxOrderNumMap.containsKey(rewardPunish.getPersonId())){
                        maxOrderNumMap.put(rewardPunish.getPersonId(), 1L);
                    }
                    rewardPunish.setOrderNum(maxOrderNumMap.get(rewardPunish.getPersonId()));
                    maxOrderNumMap.put(rewardPunish.getPersonId(), maxOrderNumMap.get(rewardPunish.getPersonId()) + 1);

                    int result = bizPersonRewardPunishService.insertBizPersonRewardPunish(rewardPunish);
                    if(result > 0){
                        successRewardPunishs.add(rewardPunish);
                        successNum++;
                        successPersonMap.put(rewardPunish.getPersonId(), successPersonMap.getOrDefault(rewardPunish.getPersonId(), 0) + 1);
                    }else{
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、姓名：" + pvm.getName()  + "导入失败：";
                        failureMsg.append(msg + "数据入库失败");
                    }
                }catch (Exception e) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、姓名：" + pvm.getName()  + "导入失败：";
                    String exMsg = e.getMessage();
                    if(StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException){
                        exMsg = ((InvocationTargetException)e).getTargetException().getMessage();
                    }
                    if(StringUtils.isEmpty(exMsg)){
                        exMsg = "数据入库失败";
                    }
                    failureMsg.append(msg + exMsg);
                }
            }
        }

        // 生成人员任免表奖惩情况
        if(successRewardPunishs.size() > 0){
            importDataBizPersonUpdate(successRewardPunishs);
        }

        if (failureNum > 0)
        {
            if(!isValid){
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            }else{
                failureMsg.insert(0, "总共：" + punishList.size() + "，成功：" + successNum + "，失败：" + failureNum + "，错误如下：");
            }
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条。");
        }

        successPersonMap.forEach((personId, count) -> {
            String operationModule = "奖惩情况";
            String operationType = PersonOperationType.IMPORT.getValue();
            String tableName = PersonTable.BIZ_PERSON_REWARD_PUNISH.getValue();
            String remark = createUserName + "导入了" + count + "条" + "\"" + operationModule + "\"信息。";
            rabbitService.sendPersonOperationLog(personId, operationType, operationModule, tableName, null, remark, createUserId, createUserName, null);
        });

        return successMsg.toString();
    }

    private Boolean checkImportData(List<BizPersonPunishTplVM> punishList) {
        Boolean isOk = true;

        //根据身份证获取人员信息
        HashMap<String, BizPerson> personMap = new HashMap<>();
        BizPerson bizPersonSearch = new BizPerson();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonSearch, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }
        HashSet<String> citizenIdSet = new HashSet<>();
        HashSet<String> personIdSet = new HashSet<>();  // 人员ID
        for (BizPersonPunishTplVM pvm : punishList) {
            if (StringUtils.isNotEmpty(pvm.getCitizenId())) {
                citizenIdSet.add(pvm.getCitizenId());
            }
        }
        if (citizenIdSet.size() > 0) {
            bizPersonSearch.getParams().put("citizenIds", citizenIdSet.toArray());
            List<BizPerson> personList = bizPersonService.selectBizPersonListByCitizenIds(bizPersonSearch);
            for (BizPerson person : personList) {
                personMap.put(person.getCitizenId(), person);
                personIdSet.add(person.getId());
            }
        }

        // 根据人员ID获取已存在的奖惩信息，用于判断人员ID、奖惩文号判重
        HashSet<String> docNoSet = new HashSet<>();  // 系统中已存在的奖惩文号
        if(personIdSet.size() > 0){
            BizPersonRewardPunish rpWhere = new BizPersonRewardPunish();
            List<BizPersonRewardPunish> existList = bizPersonRewardPunishService.selectBizPersonRewardPunishListByPersonIds(personIdSet.toArray(new String[0]), rpWhere);
            for(BizPersonRewardPunish item : existList){
                if(StringUtils.isNotEmpty(item.getRewardPunishDocNo())){
                    String docNoKey = item.getPersonId() + "_" + item.getRewardPunishDocNo();
                    docNoSet.add(docNoKey);
                }
            }   
        }
        HashSet<String> docNoSetUp = new HashSet<>();  // 上传文件中的奖惩文号

        for (BizPersonPunishTplVM pvm : punishList) {
            String preMsg = "姓名：" + pvm.getName() + " ";
            try {

//                if (StringUtils.isEmpty(pvm.getPunishCategory())){
//                    isOk = false;
//                    pvm.setErrMsg(preMsg + "惩处类别为空或系统不存在");
//                    continue;
//                }
                // 去除左右空格
                if(StringUtils.isNotEmpty(pvm.getRewardPunishDocNo())){
                    pvm.setRewardPunishDocNo(pvm.getRewardPunishDocNo().trim());
                }

                // 判空
                ArrayList<String> emptyMsgList = new ArrayList<>();
                if (StringUtils.isEmpty(pvm.getName())) emptyMsgList.add("姓名");
                if (StringUtils.isEmpty(pvm.getCitizenId())) emptyMsgList.add("身份证");
                if (StringUtils.isEmpty(pvm.getRewardPunishDay())) emptyMsgList.add("惩处时间");
//                if (StringUtils.isEmpty(pvm.getRewardPunishEndDay())) emptyMsgList.add("影响结束时间");
                if (StringUtils.isEmpty(pvm.getRewardPunishReason())) emptyMsgList.add("惩处事由");
                if (StringUtils.isEmpty(pvm.getRewardPunishPosition())) emptyMsgList.add("惩处时所任职务");
                if (StringUtils.isEmpty(pvm.getRewardPunishOrg())) emptyMsgList.add("惩处机构名称");
                if (StringUtils.isEmpty(pvm.getRewardPunishName())) emptyMsgList.add("给予的惩处");
                if (StringUtils.isEmpty(pvm.getRewardPunishDocNo())) emptyMsgList.add("惩处文号");
                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    pvm.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }

                // 惩处时间格式校验
                Date rewardPunishDay = DateUtils.dateTime("yyyy-MM-dd", pvm.getRewardPunishDay());
                if(rewardPunishDay == null){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "惩处时间格式不正确");
                    continue;
                }
                pvm.setRewardPunishDay(DateUtils.parseDateToStr("yyyy-MM-dd", rewardPunishDay));

                // 影响结束时间格式校验
                if(StringUtils.isNotEmpty(pvm.getRewardPunishEndDay())){
                    Date rewardPunishEndDay = DateUtils.dateTime("yyyy-MM-dd", pvm.getRewardPunishEndDay());
                    if(rewardPunishEndDay == null){
                        isOk = false;
                        pvm.setErrMsg(preMsg + "影响结束时间格式不正确");
                        continue;
                    }
                    pvm.setRewardPunishEndDay(DateUtils.parseDateToStr("yyyy-MM-dd", rewardPunishEndDay));
                }

                //检查人员信息
                if(!personMap.containsKey(pvm.getCitizenId())){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "人员信息不存在，检查姓名、身份证");
                    continue;
                }else{
                    if(!personMap.get(pvm.getCitizenId()).getName().equals(pvm.getName())){
                        isOk = false;
                        pvm.setErrMsg(preMsg + "人员信息不存在，检查姓名、身份证");
                        continue;
                    }
                }
                pvm.setPerson(personMap.get(pvm.getCitizenId()));

                // 根据奖惩文号判重
                String rewardPunishDocNo = pvm.getRewardPunishDocNo();
                String docNoKey = personMap.get(pvm.getCitizenId()).getId() + "_" + rewardPunishDocNo;
                // 奖惩文号判重(上传的数据内检查)
                if(docNoSetUp.contains(docNoKey)){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "惩处文号【" + rewardPunishDocNo + "】重复");
                    continue;
                }else{
                    docNoSetUp.add(docNoKey);
                }
                // 奖惩文号判重(数据库内检查)
                if(docNoSet.contains(docNoKey)){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "惩处文号【" + rewardPunishDocNo + "】重复");
                    continue;
                }
            }catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if(StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException){
                    exMsg = ((InvocationTargetException)e).getTargetException().getMessage();
                }
                if(StringUtils.isEmpty(exMsg)){
                    exMsg = "数据校验失败";
                }
                pvm.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }

    // 导入数据人员任免表奖惩情况更新处理
    private void importDataBizPersonUpdate(List<BizPersonRewardPunish> list){
        HashSet<String> personIdSet = new HashSet<>();  // 人员ID
        // 将奖惩对象按人员分类进行存储
        Map<String, List<BizPersonRewardPunish>> rewardPunishMap = new HashMap<>();
        Map<String, BizPerson> personMap = new HashMap<>();
        List<BizPerson> personList = new ArrayList<>();

        try {
            if (list == null || list.size() == 0) {
                return;
            }
            // 获取人员ID集合
            for(BizPersonRewardPunish p : list){
                personIdSet.add(p.getPersonId());
            }

            BizPersonRewardPunish rpWhere = new BizPersonRewardPunish();
            rpWhere.setShowInAppoint("Y");
            List<BizPersonRewardPunish> allList = bizPersonRewardPunishService.selectBizPersonRewardPunishListByPersonIds(personIdSet.toArray(new String[0]), rpWhere);
            if(allList == null || allList.size() == 0){
                return;
            }

            for(BizPersonRewardPunish p : allList){
                String personId = p.getPersonId();
                if(!rewardPunishMap.containsKey(personId)) {
                    rewardPunishMap.put(personId, new ArrayList<>());
                }
                rewardPunishMap.get(personId).add(p);

                BizPerson person = p.getPerson();
                if(person != null && StringUtils.isNotEmpty(person.getId())) {
                    personMap.put(personId, p.getPerson());
                }
            }

            for (Map.Entry<String, List<BizPersonRewardPunish>> entry : rewardPunishMap.entrySet()) {
                BizPerson person = personMap.get(entry.getKey());

                if(person != null) {
                    String oldPersonRbText = person.getRewardPunishContent();
                    if(oldPersonRbText == null) oldPersonRbText = "";
                    // 获取最新的奖惩情况
                    String newPersonRbText = generateNewRewardPunishContent(oldPersonRbText, entry.getValue(), null);
                    if(!oldPersonRbText.equals(newPersonRbText)){
                        person.setRewardPunishContent(newPersonRbText);
                        personList.add(person);
                    }
                }
            }

            if(personList.size() > 0){
                bizPersonService.updateRewardPunishContentForeach(personList);
            }
        }catch (Exception e){
            return;
        }
    }
}
