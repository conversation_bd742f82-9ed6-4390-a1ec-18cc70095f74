package com.metaorg.web.controller.api;

import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.ResponseBodyApi;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.utils.JWTUtil;
import com.metaorg.common.utils.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/api")
public class SysController  extends BaseController {

    @GetMapping("/list")
    @ResponseBody
    public ResponseBodyApi<String> list()
    {
        return new ResponseBodyApi<>("list success");
//        return AjaxResult.success("list success");
    }

    @PostMapping("/login")
    @ResponseBody
    public ResponseBodyApi<Map<String, String>> login(@RequestBody Map<String, Object> paramMap, HttpServletResponse response)
    {
        UsernamePasswordToken token = new UsernamePasswordToken(paramMap.get("username").toString(), paramMap.get("password").toString(), false);
        Subject subject = SecurityUtils.getSubject();
        try
        {
            subject.login(token);
            SysUser user = (SysUser) subject.getPrincipal();
            String tokenString = JWTUtil.createToken(user.getLoginName());
            response.setHeader("Authorization", "Bearer " + tokenString);

            return new ResponseBodyApi<>(new HashMap<String, String>(){{
                put("token", tokenString);
            }});
//            return success( new HashMap<String, String>(){{
//                put("token", tokenString);
//            }});
        }
        catch (AuthenticationException e)
        {
            String msg = "用户或密码错误";
            if (StringUtils.isNotEmpty(e.getMessage()))
            {
                msg = e.getMessage();
            }
            return new ResponseBodyApi<>(false, msg);
//            return error(msg);
        }
    }

    @GetMapping("/test")
    @ResponseBody
    public AjaxResult test(@RequestBody Map<String, Object> paramMap, HttpServletResponse response)
    {
        // Object user = SecurityUtils.getSubject().getPrincipal();
        // Object sysUser = getSysUser();
        return success();
    }
}
