package com.metaorg.web.controller.library;

import java.util.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.domain.BizPersonCheckTplVM;
import com.metaorg.library.service.IBizPersonService;
import com.metaorg.common.exception.ServiceException;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.file.FileUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonCheck;
import com.metaorg.library.service.IBizPersonCheckService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import com.metaorg.utils.DataScopeUtils;
import org.springframework.web.bind.annotation.RequestParam;
import java.lang.reflect.InvocationTargetException;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.utils.DictUtils;
import com.metaorg.common.core.domain.entity.SysDictData;
import com.metaorg.library.service.RabbitService;
import org.apache.shiro.authz.annotation.Logical;

/**
 * 考核情况Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/check")
public class BizPersonCheckController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(BizPersonCheckController.class);

    @Autowired
    private RabbitService rabbitService;

    private String prefix = "library/check";

    @Autowired
    private IBizPersonCheckService bizPersonCheckService;

    @Autowired
    private IBizPersonService bizPersonService;

    @RequiresPermissions("library:check:view")
    @GetMapping()
    public String check() {
        return prefix + "/check";
    }

    /**
     * 查询考核情况列表
     */
    @RequiresPermissions(value = { "library:check:list", "library:person:view" }, logical = Logical.OR)
    @PostMapping({ "/list/{personId}", "/list" })
    @ResponseBody
    public TableDataInfo list(BizPersonCheck bizPersonCheck, @RequestParam Map<String, Object> paramMap) {
        long deptId = -1;
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonCheck.getParams().put("deptId", deptId);
        }

        if (paramMap.get("checkCategoryList") != null && paramMap.get(("checkCategoryList")) != "") {
            bizPersonCheck.getParams().put("checkCategoryList",
                    Convert.toStrArray(paramMap.get("checkCategoryList").toString()));
        }

        if (paramMap.get("checkResultList") != null && paramMap.get(("checkResultList")) != "") {
            bizPersonCheck.getParams().put("checkResultList",
                    Convert.toStrArray(paramMap.get("checkResultList").toString()));
        }

        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonCheck, getSysUser(), "vpd", "", "", deptId);
        }

        startPage();
        List<BizPersonCheck> list = bizPersonCheckService.selectBizPersonCheckList(bizPersonCheck);
        return getDataTable(list);
    }

    /**
     * 导出考核情况列表
     */
    @RequiresPermissions(value = { "library:check:export" })
    @Log(title = "考核情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonCheck bizPersonCheck) {
        List<BizPersonCheck> list = bizPersonCheckService.selectBizPersonCheckList(bizPersonCheck);
        ExcelUtil<BizPersonCheck> util = new ExcelUtil<BizPersonCheck>(BizPersonCheck.class);
        return util.exportExcel(list, "考核情况数据");
    }

    /**
     * 导入模板下载
     */
    @SuppressWarnings("resource")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = System.currentTimeMillis() + "_人员考核情况导入模板" + ".xls";
        try {
            File file = new File("./config/template/ryydkh_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            // 通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("导出Excel模板异常{}", e.getMessage());
            throw new UtilException("导出Excel模板失败，请联系网站管理员！");
        } finally {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导入考核情况数据
     */
    @RequiresPermissions("library:check:import")
    @Log(title = "考核情况", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BizPersonCheckTplVM> util = new ExcelUtil<BizPersonCheckTplVM>(BizPersonCheckTplVM.class);
        List<BizPersonCheckTplVM> checkList = util.importExcel(file.getInputStream());
        String userName = getUserName();
        String message = importCheck(checkList, updateSupport, userName);
        return success(message);
    }

    public String getAbsoluteFile(String filename) {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

    /**
     * 新增考核情况
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap) {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存考核情况
     */
    @RequiresPermissions(value = { "library:person:edit" })
    @Log(title = "考核情况", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "考核情况", tableName = PersonTable.BIZ_PERSON_CHECK, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonCheck bizPersonCheck) {
        String createUserId = getUserId().toString();
        String createUserName = getUserName();

        bizPersonCheck.setCreateById(createUserId);
        bizPersonCheck.setCreateBy(createUserName);
        bizPersonCheck.setUpdateById(createUserId);
        bizPersonCheck.setUpdateBy(createUserName);

        Date currentDate = DateUtils.getNowDate();
        bizPersonCheck.setCreateTime(currentDate);
        bizPersonCheck.setUpdateTime(currentDate);

        String checkId = bizPersonCheckService.insertBizPersonCheckReturnID(bizPersonCheck);
        AjaxResult result = toAjax(StringUtils.isEmpty(checkId) ? 0 : 1);
        BizPersonCheck ckItem = bizPersonCheckService.selectBizPersonCheckById(checkId);
        if (ckItem != null) {
            String lastCkText = UpdateBizPersonCheckContent(ckItem.getPersonId(), null);
            result.put("data", lastCkText);
        }
        return result;
    }

    /**
     * 修改考核情况
     */
    @RequiresPermissions(value = { "library:check:edit", "library:person:edit" }, logical = Logical.OR)
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPersonCheck bizPersonCheck = bizPersonCheckService.selectBizPersonCheckById(id);
        mmap.put("bizPersonCheck", bizPersonCheck);
        return prefix + "/edit";
    }

    /**
     * 修改保存考核情况
     */
    @RequiresPermissions(value = { "library:check:edit", "library:person:edit" }, logical = Logical.OR)
    @Log(title = "考核情况", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "考核情况", tableName = PersonTable.BIZ_PERSON_CHECK, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonCheck bizPersonCheck) {
        String updateUserId = getUserId().toString();
        String updateUserName = getUserName();

        bizPersonCheck.setUpdateById(updateUserId);
        bizPersonCheck.setUpdateBy(updateUserName);
        bizPersonCheck.setUpdateTime(DateUtils.getNowDate());

        BizPersonCheck exitItem = bizPersonCheckService.selectBizPersonCheckById(bizPersonCheck.getId());
        AjaxResult result = toAjax(bizPersonCheckService.updateBizPersonCheck(bizPersonCheck));
        if (result.isSuccess()) {
            List<BizPersonCheck> delList = new ArrayList<BizPersonCheck>();
            delList.add(exitItem);
            String lastText = UpdateBizPersonCheckContent(exitItem.getPersonId(), delList);
            result.put("data", lastText);
        }
        return result;
    }

    /**
     * 删除考核情况
     */
    @RequiresPermissions(value = { "library:check:remove", "library:person:edit" }, logical = Logical.OR)
    @Log(title = "考核情况", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "考核情况", tableName = PersonTable.BIZ_PERSON_CHECK, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        BizPersonCheck chkBizPersonCheck = new BizPersonCheck();
        chkBizPersonCheck.getParams().put("checkIds", Convert.toStrArray(ids));
        List<BizPersonCheck> list = bizPersonCheckService.selectBizPersonCheckList(chkBizPersonCheck);
        AjaxResult result = toAjax(bizPersonCheckService.deleteBizPersonCheckByIds(ids));
        if (!list.isEmpty()) {
            String lastText = UpdateBizPersonCheckContent(list.get(0).getPersonId(), list);
            result.put("data", lastText);
        }
        return result;
    }

    /**
     * 排序考核情况
     */
    @Log(title = "考核情况", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++) {
            BizPersonCheck bizPersonCheck = bizPersonCheckService.selectBizPersonCheckById(idsArray[i]);
            bizPersonCheck.setOrderNum(Convert.toLong(i + 1));
            bizPersonCheckService.updateBizPersonCheck(bizPersonCheck);
        }
        return success();
    }

    private long getMaxOrderNum(String personId) {
        BizPersonCheck bizPersonCheck = new BizPersonCheck();
        bizPersonCheck.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonCheck> list = bizPersonCheckService.selectBizPersonCheckList(bizPersonCheck);
        if (!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }

    private String UpdateBizPersonCheckContent(String personId, List<BizPersonCheck> delList) {
        if (delList == null) {
            delList = new ArrayList<BizPersonCheck>();
        }

        if (StringUtils.isEmpty(personId)) {
            return "";
        }

        BizPerson bizPerson = bizPersonService.selectBizPersonById(personId);
        if (bizPerson == null) {
            return "";
        }

        String newPersonCheckText = "";
        String oldPersonCheckText = bizPerson.getCheckContent();
        newPersonCheckText = oldPersonCheckText;

        // 获取所有需要免表显示的考核情况
        BizPersonCheck bizPersonCheck = new BizPersonCheck();
        bizPersonCheck.setPersonId(personId);
        bizPersonCheck.setShowInAppoint("Y");
        List<BizPersonCheck> list = bizPersonCheckService.selectBizPersonCheckList(bizPersonCheck);
        if (list == null) {
            list = new ArrayList<BizPersonCheck>();
        }
        newPersonCheckText = generateNewCheckContent(oldPersonCheckText, list, delList);

        if (!newPersonCheckText.equals(oldPersonCheckText)) {
            BizPerson bizPersonUp = new BizPerson();
            bizPersonUp.setId(personId);
            bizPersonUp.setCheckContent(newPersonCheckText);
            bizPersonService.updateBizPerson(bizPersonUp);
        }
        return newPersonCheckText;
    }

    /**
     * 重新生成人员任免表考核情况
     */
    private String generateNewCheckContent(String oldPersonCheckText, List<BizPersonCheck> list,
            List<BizPersonCheck> delList) {
        if (oldPersonCheckText == null) {
            oldPersonCheckText = "";
        }

        String newPersonCheckText = oldPersonCheckText;

        if (delList == null) {
            delList = new ArrayList<>();
        }

        if (list == null) {
            list = new ArrayList<>();
        }

        List<BizPersonCheck> allDelList = ListUtils.union(list, delList);

        // 按照checkToString()方法返回的字符串长度进行降序排序
        allDelList.sort((param1, param2) -> {
            Integer len1 = param1.checkToString().length();
            Integer len2 = param2.checkToString().length();
            return -len1.compareTo(len2);
        });

        // 删除所有需要删除的考核情况（后面重新生成）
        for (BizPersonCheck item : allDelList) {
            String checkString = item.checkToString();
            if (StringUtils.isNotEmpty(checkString)) {
                newPersonCheckText = newPersonCheckText.replace(checkString + "；", "");
            }
        }

        // 根据checkToString去重
        List<BizPersonCheck> uniqueList = list.stream()
                .collect(Collectors.toMap(BizPersonCheck::checkToString, p -> p, (p1, p2) -> p1))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 按照日期正序排序
        uniqueList.sort(Comparator.comparing(BizPersonCheck::getCheckYear));

        for (BizPersonCheck item : uniqueList) {
            String checkString = item.checkToString();
            if (StringUtils.isNotEmpty(checkString)) {
                newPersonCheckText = newPersonCheckText + checkString + "；";
            }
        }

        // 去掉开头为“无”的字符串（一般是在初始化时导入的）
        if (!(newPersonCheckText == "无" || newPersonCheckText == "（无）" || newPersonCheckText == "(无)")) {
            newPersonCheckText = newPersonCheckText.replaceFirst("^(无|（无）|\\(无\\))", "");
        }

        // 最多414个字符
        if (newPersonCheckText.length() > 414 && newPersonCheckText.length() > oldPersonCheckText.length()) {
            newPersonCheckText = oldPersonCheckText;
        }

        return newPersonCheckText;
    }

    /**
     * 导入考核情况数据
     */
    public String importCheck(List<BizPersonCheckTplVM> checkList, Boolean isUpdateSupport, String userName) {
        if (StringUtils.isNull(checkList) || checkList.size() == 0) {
            throw new ServiceException("导入考核情况数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        Boolean isValid = checkImportData(checkList);
        HashSet<String> personIdSet = new HashSet<>();
        HashMap<String, Long> maxOrderNumMap = new HashMap<>();
        if (isValid) {
            for (BizPersonCheckTplVM checkTplVM : checkList) {
                personIdSet.add(checkTplVM.getPerson().getId());
            }
            // 根据人员ID集团获取每一个人的最大职务职级序号
            List<Map<String, Object>> postMaxOrderNumList = bizPersonCheckService
                    .selectMaxOrderNumGroupPerson(personIdSet.toArray(new String[0]));
            for (Map<String, Object> maxOrderNum : postMaxOrderNumList) {
                maxOrderNumMap.put(maxOrderNum.get("person_id").toString(),
                        Long.valueOf(maxOrderNum.get("order_num").toString()));
            }
        }

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        Map<String, Integer> successPersonMap = new HashMap<>();
        List<BizPersonCheck> successCheckList = new ArrayList<>();

        for (BizPersonCheckTplVM checkTplVM : checkList) {
            if (!isValid) {
                if (StringUtils.isNotEmpty(checkTplVM.getErrMsg())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + checkTplVM.getErrMsg());
                }
                continue;
            }

            try {
                BizPersonCheck check = BizPersonCheck.fromBizPersonCheckTplVM(checkTplVM);

                check.setCreateById(createUserId);
                check.setCreateBy(createUserName);
                check.setUpdateById(createUserId);
                check.setUpdateBy(createUserName);

                check.setCreateTime(currentDate);
                check.setUpdateTime(currentDate);

                // 奖惩情况序号
                if (!maxOrderNumMap.containsKey(check.getPersonId())) {
                    maxOrderNumMap.put(check.getPersonId(), 1L);
                }

                check.setOrderNum(maxOrderNumMap.get(check.getPersonId()));
                maxOrderNumMap.put(check.getPersonId(), maxOrderNumMap.get(check.getPersonId()) + 1);

                int result = bizPersonCheckService.insertBizPersonCheck(check);
                if (result > 0) {
                    successCheckList.add(check);
                    successNum++;
                    successPersonMap.put(check.getPersonId(),
                            successPersonMap.getOrDefault(check.getPersonId(), 0) + 1);
                } else {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、姓名：" + checkTplVM.getPersonName() + "导入失败：";
                    failureMsg.append(msg + "数据入库失败");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、身份证号 " + checkTplVM.getCitizenId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        // 生成人员任免表考核情况
        if (successCheckList.size() > 0) {
            importDataBizPersonUpdate(successCheckList);
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }

        successPersonMap.forEach((personId, count) -> {
            String operationModule = "考核情况";
            String operationType = PersonOperationType.IMPORT.getValue();
            String tableName = PersonTable.BIZ_PERSON_CHECK.getValue();
            String remark = createUserName + "导入了" + count + "条" + "\"" + operationModule + "\"信息。";
            rabbitService.sendPersonOperationLog(personId, operationType, operationModule, tableName, null, remark,
                    createUserId, createUserName, null);
        });

        return successMsg.toString();
    }

    /**
     * 校验导入数据
     */
    private Boolean checkImportData(List<BizPersonCheckTplVM> checkList) {
        Boolean isOk = true;

        // 根据身份证获取人员信息
        HashMap<String, BizPerson> personMap = new HashMap<>();
        BizPerson bizPersonSearch = new BizPerson();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonSearch, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }
        HashSet<String> citizenIdSet = new HashSet<>();
        HashSet<String> personIdSet = new HashSet<>(); // 人员ID
        for (BizPersonCheckTplVM pvm : checkList) {
            if (StringUtils.isNotEmpty(pvm.getCitizenId())) {
                citizenIdSet.add(pvm.getCitizenId().trim());
            }
        }
        if (citizenIdSet.size() > 0) {
            bizPersonSearch.getParams().put("citizenIds", citizenIdSet.toArray());
            List<BizPerson> personList = bizPersonService.selectBizPersonListByCitizenIds(bizPersonSearch);
            for (BizPerson person : personList) {
                personMap.put(person.getCitizenId().trim(), person);
                personIdSet.add(person.getId());
            }
        }

        // 从字典缓存中获取人员考核类别(biz_person_check_category)和人员考核结论(biz_person_check_result)
        List<SysDictData> checkCategoryMap = DictUtils.getDictCache("biz_person_check_category");
        HashSet<String> checkCategorySet = new HashSet<>();
        for (SysDictData dictData : checkCategoryMap) {
            checkCategorySet.add(dictData.getDictLabel());
        }
        List<SysDictData> checkResultMap = DictUtils.getDictCache("biz_person_check_result");
        HashSet<String> checkResultSet = new HashSet<>();
        for (SysDictData dictData : checkResultMap) {
            checkResultSet.add(dictData.getDictLabel());
        }

        HashSet<String> checkShowInAppointSet = new HashSet<>();
        checkShowInAppointSet.add("Y");
        checkShowInAppointSet.add("N");

        for (BizPersonCheckTplVM checkTplVM : checkList) {
            String preMsg = "姓名：" + checkTplVM.getPersonName() + " ";

            try {
                // 判空
                ArrayList<String> emptyMsgList = new ArrayList<>();
                if (StringUtils.isEmpty(checkTplVM.getPersonName()))
                    emptyMsgList.add("姓名");
                if (StringUtils.isEmpty(checkTplVM.getCitizenId()))
                    emptyMsgList.add("身份证");
                if (StringUtils.isEmpty(checkTplVM.getCheckCategory()))
                    emptyMsgList.add("考核类别");
                if (StringUtils.isEmpty(checkTplVM.getCheckYear()))
                    emptyMsgList.add("考核年度");
                if (StringUtils.isEmpty(checkTplVM.getCheckResult()))
                    emptyMsgList.add("考核结论类别");
                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }

                // 考核类别格式校验
                if (!checkCategorySet.contains(checkTplVM.getCheckCategory())) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "考核类别格式不正确，应为以下值之一：" + String.join(",", checkCategorySet));
                    continue;
                }

                // 考核年度格式校验
                Date checkYearDate = DateUtils.dateTime("yyyy", checkTplVM.getCheckYear());
                if (checkYearDate == null) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "考核年度格式不正确，正确示例: 2025");
                    continue;
                }
                checkTplVM.setCheckYear(DateUtils.parseDateToStr("yyyy", checkYearDate));

                // 任免表中是否显示校验
                if (!checkShowInAppointSet.contains(checkTplVM.getShowInAppoint())) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "任免表中是否显示格式不正确，应为Y或N");
                    continue;
                }

                // 考核结论类别格式校验
                if (!checkResultSet.contains(checkTplVM.getCheckResult())) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "考核结论类别格式不正确，应为以下值之一：" + String.join(",", checkResultSet));
                    continue;
                }

                // 起始日期格式校验
                Date startDate = DateUtils.dateTime("yyyy-MM", checkTplVM.getStartDay());
                if (startDate == null) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "起始日期格式不正确，正确示例: 2025-01");
                    continue;
                }
                checkTplVM.setStartDay(DateUtils.parseDateToStr("yyyy-MM", startDate));

                // 截止日期格式校验
                if (StringUtils.isNotEmpty(checkTplVM.getEndDay())) {
                    Date endDate = DateUtils.dateTime("yyyy-MM", checkTplVM.getEndDay());
                    if (endDate == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "截止日期格式不正确，正确示例: 2025-12");
                        continue;
                    }
                    checkTplVM.setEndDay(DateUtils.parseDateToStr("yyyy-MM", endDate));
                }

                // 检查人员信息
                if (!personMap.containsKey(checkTplVM.getCitizenId())) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + "人员信息不存在，检查姓名、身份证");
                    continue;
                } else {
                    if (!personMap.get(checkTplVM.getCitizenId()).getName().equals(checkTplVM.getPersonName())) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "人员信息不存在，检查姓名、身份证");
                        continue;
                    }
                }
                checkTplVM.setPerson(personMap.get(checkTplVM.getCitizenId()));

            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if (StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException) {
                    exMsg = ((InvocationTargetException) e).getTargetException().getMessage();
                }
                if (StringUtils.isEmpty(exMsg)) {
                    exMsg = "数据校验失败";
                }
                checkTplVM.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }

    private void importDataBizPersonUpdate(List<BizPersonCheck> list) {
        HashSet<String> personIdSet = new HashSet<>(); // 人员ID
        // 将奖惩对象按人员分类进行存储
        Map<String, List<BizPersonCheck>> checkMap = new HashMap<>();
        Map<String, BizPerson> personMap = new HashMap<>();
        List<BizPerson> personList = new ArrayList<>();

        try {
            if (list == null || list.size() == 0) {
                return;
            }
            // 获取人员ID集合
            for (BizPersonCheck p : list) {
                personIdSet.add(p.getPersonId());
            }

            BizPersonCheck checkWhere = new BizPersonCheck();
            checkWhere.setShowInAppoint("Y");
            List<BizPersonCheck> allList = bizPersonCheckService
                    .selectBizPersonCheckListByPersonIds(personIdSet.toArray(new String[0]), checkWhere);
            if (allList == null || allList.size() == 0) {
                return;
            }

            for (BizPersonCheck p : allList) {
                String personId = p.getPersonId();
                if (!checkMap.containsKey(personId)) {
                    checkMap.put(personId, new ArrayList<>());
                }
                checkMap.get(personId).add(p);

                BizPerson person = p.getPerson();
                if (person != null && StringUtils.isNotEmpty(person.getId())) {
                    personMap.put(personId, p.getPerson());
                }
            }

            for (Map.Entry<String, List<BizPersonCheck>> entry : checkMap.entrySet()) {
                BizPerson person = personMap.get(entry.getKey());

                if (person != null) {
                    String oldPersonCheckText = person.getCheckContent();
                    if (oldPersonCheckText == null) {
                        oldPersonCheckText = "";
                    }

                    // 获取最新的考核情况
                    String newPersonCheckText = generateNewCheckContent(oldPersonCheckText, entry.getValue(), null);
                    if (!oldPersonCheckText.equals(newPersonCheckText)) {
                        person.setCheckContent(newPersonCheckText);
                        personList.add(person);
                    }
                }
            }

            if (personList.size() > 0) {
                bizPersonService.updateCheckContentForeach(personList);
            }
        } catch (Exception e) {
            return;
        }
    }
}
