# 真实场景全流程测试指南

## 概述

本文档详细说明如何运行新疆金融监管局处罚信息爬虫系统的真实场景全流程集成测试。

## 测试类型

### 1. 基础功能测试
- 环境变量检查
- 数据模型验证
- JSON处理测试
- **无需外部服务**

### 2. 连接测试
- 数据库连接测试
- RabbitMQ连接测试
- **需要相应服务运行**

### 3. Excel导出功能测试
- 命令行参数解析测试
- Excel服务功能测试
- 导出模式业务流程测试
- **无需外部服务（Mock模式）**

### 4. 全流程集成测试
- 消息队列发送/接收
- 网页爬虫实际抓取
- AI数据清洗
- 数据库存储验证
- **需要所有服务和有效API密钥**

## 环境准备

### 1. 安装依赖服务

#### PostgreSQL
```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS
brew install postgresql

# Windows
# 下载并安装 PostgreSQL 官方安装包
```

#### RabbitMQ
```bash
# Ubuntu/Debian
sudo apt-get install rabbitmq-server

# macOS
brew install rabbitmq

# Windows
# 下载并安装 RabbitMQ 官方安装包
```

### 2. 创建测试数据库

```sql
-- 连接到PostgreSQL
psql -U postgres

-- 创建测试数据库
CREATE DATABASE test_metaorg;

-- 切换到测试数据库
\c test_metaorg;

-- 执行初始化脚本
\i sql/01_init_tables.sql;
```

### 3. 配置环境变量

复制并编辑测试配置文件：
```bash
cp .env.test .env
```

编辑`.env`文件，设置以下关键配置：
```bash
# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=test_metaorg
DATABASE_USER=postgres
DATABASE_PWD=your_password

# RabbitMQ配置
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PWD=guest

# OpenAI配置（必须设置有效的API密钥）
OPENAI_API_KEY=your_real_api_key_here
OPENAI_BASE_URL=https://api.deepseek.com
OPENAI_MODEL=deepseek-ai/DeepSeek-V3-0324
```

## 运行测试

### 1. 基础功能测试（无需外部服务）

```bash
# 运行基础测试
uv run pytest tests/test_integration_simple.py::TestSimpleIntegration::test_environment_setup -v -s
uv run pytest tests/test_integration_simple.py::TestSimpleIntegration::test_data_models -v -s
uv run pytest tests/test_integration_simple.py::TestSimpleIntegration::test_json_processing -v -s
```

### 2. 连接测试（需要服务运行）

```bash
# 启动PostgreSQL和RabbitMQ服务后运行
uv run pytest tests/test_integration_simple.py::TestSimpleIntegration::test_database_connection -v -s
uv run pytest tests/test_integration_simple.py::TestSimpleIntegration::test_rabbitmq_connection -v -s
```

### 3. 消息发送测试

```bash
# 测试向RabbitMQ发送消息
uv run pytest tests/test_integration_simple.py::TestSimpleIntegration::test_send_test_message -v -s
```

### 4. Excel导出功能测试

#### 基础导出功能测试（无需外部服务）

```bash
# 测试命令行参数解析
uv run pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_command_line_export_parameter -v -s
```

#### 完整导出功能测试（需要配置）

```bash
# 测试完整Excel导出流程（Mock模式，无需外部服务）
uv run pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_export_functionality -v -s
```

#### 实际导出测试

```bash
# 实际运行导出功能（需要完整环境配置）
uv run python -m nfra_crawler.main --export test_output.xlsx

# 验证导出文件
ls -la test_output.xlsx  # Linux/macOS
dir test_output.xlsx     # Windows
```

### 5. 全流程集成测试

#### 方式1: 使用自动化脚本

```bash
# Linux/macOS
python scripts/run_integration_test.py

# Windows
scripts\run_integration_test.bat
```

#### 方式2: 手动运行

1. **启动爬虫服务**
   ```bash
   # 在一个终端中启动爬虫服务
   uv run python -m nfra_crawler.main
   ```

2. **运行全流程测试**
   ```bash
   # Linux/MacOS
   MANUAL_TEST_MODE=true uv run pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_full_workflow_integration -v -s

   # Windows
   $env:MANUAL_TEST_MODE="true"; uv run python -m pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_full_workflow_integration -v -s
   ```

## 测试流程说明

### Excel导出测试步骤

1. **参数解析测试** 📋
   - 测试`--export`参数的命令行解析
   - 验证参数传递到应用程序
   - 检查导出模式初始化

2. **Mock导出测试** 🎭
   - 使用Mock避免实际网络请求
   - 测试导出模式的业务流程
   - 验证健康检查和配置验证

3. **Excel服务测试** 📊
   - 测试Excel文件生成功能
   - 验证数据格式转换
   - 检查文件路径处理和权限

4. **完整导出测试** 🔄
   - 测试完整的爬虫→AI清洗→Excel导出流程
   - 验证两个Sheet的数据结构
   - 检查导出日志和统计信息

### 全流程测试步骤

1. **消息发送** 📤
   - 生成唯一的测试任务ID
   - 向RabbitMQ队列发送爬虫请求消息
   - 验证消息发送成功

2. **消息接收** 📥
   - 爬虫服务接收并解析消息
   - 创建同步记录到数据库
   - 更新处理状态

3. **网页爬取** 🕷️
   - 访问新疆金融监管局官网
   - 爬取处罚公示列表页面
   - 处理分页和详情页面

4. **AI数据清洗** 🤖
   - 使用OpenAI API解析HTML表格
   - 结构化处罚信息数据
   - 识别当事人类型（个人/机构）

5. **数据存储** 💾
   - 数据去重处理
   - 批量插入处罚公示项
   - 保存处罚详情信息

6. **结果验证** ✅
   - 检查同步记录状态
   - 统计处罚公示项数量
   - 验证处罚详情数据

### 预期结果

成功的全流程测试应该显示：
```
🎉 全流程测试成功完成！
📊 最终统计:
  - 同步状态: succeeded
  - 处罚公示项: X 个
  - 处罚详情: Y 条
  - 处理耗时: Z 毫秒
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   SKIPPED (数据库连接失败: connection to server at "localhost")
   ```
   - 确保PostgreSQL服务正在运行
   - 检查数据库配置参数
   - 验证数据库用户权限

2. **RabbitMQ连接失败**
   ```
   SKIPPED (RabbitMQ连接失败:)
   ```
   - 确保RabbitMQ服务正在运行
   - 检查端口5672是否开放
   - 验证用户名密码

3. **OpenAI API调用失败**
   ```
   AI清洗未获取到有效数据
   ```
   - 检查API密钥是否有效
   - 确认API额度充足
   - 验证网络连接

4. **爬虫访问失败**
   ```
   未获取到任何处罚公示项
   ```
   - 检查网络连接
   - 确认目标网站可访问
   - 检查防火墙设置

### 调试技巧

1. **查看详细日志**
   ```bash
   # 设置调试级别日志
   LOG_LEVEL=DEBUG uv run pytest tests/test_full_workflow.py -v -s
   ```

2. **检查数据库记录**
   ```sql
   -- 查看同步记录
   SELECT * FROM biz_sync_record ORDER BY create_time DESC LIMIT 5;
   
   -- 查看处罚公示项
   SELECT * FROM biz_sync_punish_item ORDER BY create_time DESC LIMIT 5;
   
   -- 查看处罚详情
   SELECT * FROM biz_sync_punish_detail ORDER BY create_time DESC LIMIT 5;
   ```

3. **监控RabbitMQ队列**
   ```bash
   # 访问RabbitMQ管理界面
   http://localhost:15672
   # 默认用户名密码: guest/guest
   ```

## 注意事项

1. **测试环境隔离**
   - 使用独立的测试数据库
   - 使用测试专用的RabbitMQ队列
   - 避免影响生产环境

2. **API使用限制**
   - OpenAI API有调用频率限制
   - 建议使用测试专用的API密钥
   - 注意API费用消耗

3. **网络依赖**
   - 测试需要访问外部网站
   - 确保网络连接稳定
   - 考虑网络延迟影响

4. **测试时间**
   - 完整流程测试可能需要几分钟
   - 网页爬取速度取决于网络状况
   - AI处理时间取决于数据量
