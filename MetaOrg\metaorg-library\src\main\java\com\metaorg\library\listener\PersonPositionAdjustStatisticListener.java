package com.metaorg.library.listener;

import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.metaorg.library.config.RabbitConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.core.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.amqp.support.AmqpHeaders;
import com.rabbitmq.client.Channel;
import com.fasterxml.jackson.core.type.TypeReference;
import java.io.IOException;
import com.metaorg.library.service.IBizStatisticPositionAdjustService;
import com.metaorg.library.domain.BizStatisticPositionAdjust;

/**
 * 人员调整情况统计监听器
 * <AUTHOR>
 */
@Component
@Slf4j
public class PersonPositionAdjustStatisticListener {

    @Autowired
    private IBizStatisticPositionAdjustService bizStatisticPositionAdjustService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理人员调整情况统计处理队列
     */
    @RabbitListener(queues = RabbitConfig.PERSON_POSITION_ADJUST_STATISTIC_PROCESS_QUEUE)
    public void handlePersonPositionAdjustStatisticProcessQueue(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                               Channel channel) {
        String name = "处理人员调整情况统计处理队列";
        try {
            log.info(name);
            String content = new String(message.getBody());

            log.info(name + "，消息内容: {}", content);
            if (content == null || content.isEmpty()) {
                return;
            }

            Map<String, Object> payload = objectMapper.readValue(content, new TypeReference<Map<String, Object>>() {});
            if (payload == null) {
                return;
            }

            BizStatisticPositionAdjust bizStatisticPositionAdjust = new BizStatisticPositionAdjust();
            bizStatisticPositionAdjust.setParams(payload);

            bizStatisticPositionAdjustService.invokeStatisticPositionAdjust(bizStatisticPositionAdjust);

            log.info(name + "，已提交处理！");
        } catch (Exception e) {
            log.error(name + "，处理出错：{}", e.getMessage(), e);
        } finally {
            try {
                // 无论成功失败都确认消息，避免消息堆积
                channel.basicAck(deliveryTag, false);
            } catch (IOException e) {
                log.error(name + "，确认消息出错：{}", e.getMessage(), e);
            }
        }
    }
}