package com.metaorg.web.controller.library;

import java.util.*;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.library.service.IBizPersonReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import com.metaorg.common.core.domain.BaseEntity;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.ui.ModelMap;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.common.utils.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.core.text.Convert;
import org.springframework.web.bind.annotation.RequestParam;
import com.metaorg.system.domain.SysPersonPost;
import com.metaorg.system.service.ISysPersonPostService;

/**
 * 干部统计分析
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/library/statistic")
public class BizStatisticController extends BaseController {

    private String prefix = "library/statistic";

    @Autowired
    private IBizPersonReportService personReportService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysPersonPostService personPostService;

    /**
     * 跳转到统计分析页面
     */
    @GetMapping()
    public String index(ModelMap mmap)
    {       
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }

        return prefix + "/index";
    }

    /**
     * 跳转到统计分析页面
     */
    @GetMapping("/home")
    public String home(ModelMap mmap)
    {       
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }

        // 获取职务树列表
        SysPersonPost personPost = new SysPersonPost();
        personPost.setParentId("0");
        List<SysPersonPost> posts = personPostService.selectPersonPostTreeList(personPost);
        mmap.put("posts", posts);
        return prefix + "/home";
    }

    /**
     * 获取组织层面的统计分析数据
     */
    @RequiresPermissions("library:statistic:view")
    @PostMapping("/personnel/structure")
    @ResponseBody
    public AjaxResult selectPersonnelStructureStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }
        Map<String, Object> analysisData = personReportService.selectPersonnelStructureStatistic(baseEntity);
        return AjaxResult.success(analysisData);
    }

    /**
     * 获取组织层面的统计分析数据
     */
    @RequiresPermissions("library:statistic:view")
    @PostMapping("/orgs/levels")
    @ResponseBody
    public AjaxResult selectOrgLevelStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }
        Map<String, Object> analysisData = personReportService.selectOrgLevelStatistic(baseEntity);
        return AjaxResult.success(analysisData);
        }

        /**
         * 获取组织层面的岗位条线分析数据2
         */
        @RequiresPermissions("library:statistic:view")
        @PostMapping("/orgs/levels/post_line")
        @ResponseBody
        public AjaxResult selectOrgLevelPostLineStatistic(@RequestParam Map<String, Object> paramMap) {
            BaseEntity baseEntity = new BaseEntity();
            if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
                baseEntity.getParams().put("deptIds",
                        Convert.toStrArray(paramMap.get("deptIds").toString()));
            }

            if (paramMap.get("postIds") != null && paramMap.get(("postIds")) != "") {
                baseEntity.getParams().put("postIds", Convert.toStrArray(paramMap.get("postIds").toString()));
            }

            if (paramMap.get("year") != null && paramMap.get(("year")) != "") {
                baseEntity.getParams().put("year", Convert.toInt(paramMap.get("year").toString()));
            }

            Map<String, Object> analysisData = personReportService.selectOrgLevelPostLineStatistic(baseEntity);
            return AjaxResult.success(analysisData);
        }

        /**
         * 获取干部已调整情况分析
         */
        @RequiresPermissions("library:statistic:view")
        @PostMapping("/position/adjusts/adjusted")
        @ResponseBody
        public AjaxResult selectPositionAdjustedStatistic(@RequestParam Map<String, Object> paramMap) {
            BaseEntity baseEntity = new BaseEntity();
            if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
                baseEntity.getParams().put("deptIds",
                        Convert.toStrArray(paramMap.get("deptIds").toString()));
            }

            if (paramMap.get("adjustTypes") != null && paramMap.get(("adjustTypes")) != "") {
                baseEntity.getParams().put("adjustTypeList", Convert.toStrArray(paramMap.get("adjustTypes").toString()));
            }

            if (paramMap.get("year") != null && paramMap.get(("year")) != "") {
                baseEntity.getParams().put("year", Convert.toInt(paramMap.get("year").toString()));
            }

            List<Map<String, Object>> result = personReportService.selectPositionAdjustedStatistic(baseEntity);
            return AjaxResult.success(result);
        }

        /**
         * 获取干部拟调整情况分析
         */
        @RequiresPermissions("library:statistic:view")
        @PostMapping("/position/adjusts/proposed")
        @ResponseBody
        public AjaxResult selectPositionProposedStatistic(@RequestParam Map<String, Object> paramMap) {
            BaseEntity baseEntity = new BaseEntity();
            if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
                baseEntity.getParams().put("deptIds",
                        Convert.toStrArray(paramMap.get("deptIds").toString()));
            }

            if (paramMap.get("adjustTypes") != null && paramMap.get(("adjustTypes")) != "") {
                baseEntity.getParams().put("adjustTypeList", Convert.toStrArray(paramMap.get("adjustTypes").toString()));
            }

            if (paramMap.get("year") != null && paramMap.get(("year")) != "") {
                baseEntity.getParams().put("year", Convert.toInt(paramMap.get("year").toString()));
            }

            List<Map<String, Object>> result = personReportService.selectPositionProposedStatistic(baseEntity);
            return AjaxResult.success(result);
        }
}