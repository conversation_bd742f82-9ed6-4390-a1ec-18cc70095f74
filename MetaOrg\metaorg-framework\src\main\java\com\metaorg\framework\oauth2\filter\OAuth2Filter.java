package com.metaorg.framework.oauth2.filter;

import com.metaorg.common.utils.StringUtils;
import com.metaorg.framework.oauth2.auth.OAuth2Token;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

/**
 * OAuth2认证过滤器
 * 用于API接口的OAuth2 Token验证
 * 
 * <AUTHOR>
 */
public class OAuth2Filter extends AccessControlFilter {
    
    private static final Logger logger = LoggerFactory.getLogger(OAuth2Filter.class);
    
    /**
     * OAuth2 Token请求头名称
     */
    private static final String OAUTH2_TOKEN_HEADER = "OAuth2-Token";
    
    /**
     * 备用Token请求头名称
     */
    private static final String AUTHORIZATION_HEADER = "Authorization";
    
    /**
     * Bearer Token前缀
     */
    private static final String BEARER_PREFIX = "Bearer ";
    
    private final ThreadLocal<String> msgHolder = new ThreadLocal<>();
    
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception {
        return this.executeOAuth2Login(request, response);
    }
    
    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        String msg = msgHolder.get();
        msgHolder.remove();
        
        // 返回401未授权
        WebUtils.toHttp(response).setStatus(401);
        WebUtils.toHttp(response).setContentType("application/json;charset=utf-8");
        WebUtils.toHttp(response).getWriter().write(
            String.format("{\"code\":401,\"msg\":\"%s\"}", msg != null ? msg : "OAuth2认证失败")
        );
        
        return false;
    }
    
    /**
     * 执行OAuth2登录认证
     */
    protected boolean executeOAuth2Login(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpRequest = WebUtils.toHttp(request);
        
        // 获取OAuth2 Token
        String token = getOAuth2Token(httpRequest);
        if (StringUtils.isEmpty(token)) {
            msgHolder.set("缺少OAuth2访问令牌");
            return false;
        }
        
        try {
            // 创建OAuth2Token进行认证
            OAuth2Token oauth2Token = new OAuth2Token("", token, "API");
            Subject subject = this.getSubject(request, response);
            subject.login(oauth2Token);
            
            logger.debug("OAuth2 API认证成功");
            return true;
            
        } catch (AuthenticationException e) {
            logger.warn("OAuth2 API认证失败: {}", e.getMessage());
            msgHolder.set("OAuth2令牌验证失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 从请求中获取OAuth2 Token
     */
    private String getOAuth2Token(HttpServletRequest request) {
        // 优先从OAuth2-Token头获取
        String token = request.getHeader(OAUTH2_TOKEN_HEADER);
        if (StringUtils.isNotEmpty(token)) {
            return token.trim();
        }
        
        // 从Authorization头获取Bearer Token
        String authHeader = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.isNotEmpty(authHeader) && authHeader.startsWith(BEARER_PREFIX)) {
            return authHeader.substring(BEARER_PREFIX.length()).trim();
        }
        
        // 从请求参数获取
        token = request.getParameter("access_token");
        if (StringUtils.isNotEmpty(token)) {
            return token.trim();
        }
        
        return null;
    }
}
