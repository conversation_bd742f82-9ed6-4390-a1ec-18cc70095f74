<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.system.mapper.SysUserMapper">

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"        column="user_id"         />
		<result property="deptId"        column="dept_id"         />
		<result property="loginName"     column="login_name"      />
		<result property="userName"      column="user_name"       />
		<result property="unionLoginName" column="union_login_name" />
		<result property="loginType"     column="login_type"      />
		<result property="userType"      column="user_type"       />
		<result property="email"         column="email"           />
		<result property="phonenumber"   column="phonenumber"     />
		<result property="sex"           column="sex"             />
		<result property="avatar"        column="avatar"          />
		<result property="password"      column="password"        />
		<result property="salt"          column="salt"            />
		<result property="status"        column="status"          />
		<result property="delFlag"       column="del_flag"        />
		<result property="loginIp"       column="login_ip"        />
		<result property="loginDate"     column="login_date"      />
		<result property="pwdUpdateDate" column="pwd_update_date" />
		<result property="createBy"      column="create_by"       />
		<result property="createTime"    column="create_time"     />
		<result property="updateBy"      column="update_by"       />
		<result property="updateTime"    column="update_time"     />
		<result property="remark"        column="remark"          />
		<association property="dept"     column="dept_id" javaType="SysDept" resultMap="deptResult" />
		<collection  property="roles"   javaType="java.util.List"        resultMap="RoleResult" />
	</resultMap>
	
	<resultMap id="deptResult" type="SysDept">
		<id     property="deptId"    column="dept_id"     />
		<result property="parentId"  column="parent_id"   />
		<result property="deptName"  column="dept_name"   />
		<result property="ancestors" column="ancestors"   />
		<result property="orderNum"  column="order_num"   />
		<result property="leader"    column="leader"      />
		<result property="status"    column="dept_status" />
	</resultMap>
	
	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"    column="data_scope"     />
		<result property="status"       column="role_status"    />
	</resultMap>
	
	<sql id="selectUserVo">
        select  u.user_id, u.dept_id, u.login_name, u.user_name, u.union_login_name, u.login_type, u.user_type, u.email, u.avatar, u.phonenumber, 
			u.sex, u.password, u.salt, u.status, u.del_flag, u.login_ip, u.login_date, u.pwd_update_date, u.create_time, u.remark,
       		d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,
       		r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
    </sql>
	
	<select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.login_name, u.user_name, u.union_login_name, u.login_type, u.user_type, 
			u.email, u.avatar, u.phonenumber, u.password, u.sex, u.salt, u.status, u.del_flag, u.login_ip, 
			u.login_date, u.create_by, u.create_time, u.remark, d.dept_name, d.leader from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="loginName != null and loginName != ''">
			AND (
				u.login_name like concat('%', #{loginName}, '%')
				OR u.union_login_name like concat('%', #{loginName}, '%')
			)
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET (#{deptId},ancestors) ))
		</if>
		<if test="params.userIds != null">
            and user_id in
            <foreach item="userId" index="index" collection="params.userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
		<!-- 数据范围过滤 -->
		 AND 'params.dataScope' = 'params.dataScope'
	</select>
	
	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.login_name, u.user_name, u.union_login_name, u.login_type, 
			u.user_type, u.email, u.avatar, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="loginName != null and loginName != ''">
			AND u.login_name like concat('%', #{loginName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		 AND 'params.dataScope' = 'params.dataScope'
	</select>
	
	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.login_name, u.user_name, u.union_login_name, u.login_type, 
			u.user_type, u.email, u.avatar, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="loginName != null and loginName != ''">
			AND u.login_name like concat('%', #{loginName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		 AND 'params.dataScope' = 'params.dataScope'
	</select>
	
	<select id="selectUserByLoginName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.login_name = #{userName} and u.del_flag = '0'
	</select>

	<select id="selectUserByUnionLoginName" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.union_login_name = #{unionLoginName} and u.del_flag = '0'
	</select>
	
	<select id="selectUserByPhoneNumber" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.phonenumber = #{phonenumber} and u.del_flag = '0'
	</select>
	
	<select id="selectUserByEmail" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.email = #{email} and u.del_flag = '0'
	</select>
	
	<select id="checkLoginNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, login_name from sys_user where login_name=#{loginName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkUnionLoginNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, union_login_name from sys_user where union_login_name=#{unionLoginName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber=#{phonenumber} and del_flag = '0' limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email=#{email} and del_flag = '0' limit 1
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	
	<delete id="deleteUserById" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>
 	
 	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="loginName != null and loginName != ''">login_name = #{loginName},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="unionLoginName != null and unionLoginName != ''">union_login_name = #{unionLoginName},</if>
 			<if test="loginType != null and loginType != ''">login_type = #{loginType},</if>
 			<if test="userType != null and userType != ''">user_type = #{userType},</if>
 			<if test="email != null and email != ''">email = #{email},</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="salt != null and salt != ''">salt = #{salt},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="pwdUpdateDate != null">pwd_update_date = #{pwdUpdateDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
 		</set>
 		where user_id = #{userId}
	</update>
 	
 	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user
		<trim prefix="(" suffix=")" suffixOverrides=",">
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="loginName != null and loginName != ''">login_name,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="unionLoginName != null and unionLoginName != ''">union_login_name,</if>
 			<if test="loginType != null and loginType != ''">login_type,</if>
 			<if test="userType != null and userType != ''">user_type,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="salt != null and salt != ''">salt,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="pwdUpdateDate != null">pwd_update_date,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="loginName != null and loginName != ''">#{loginName},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="unionLoginName != null">#{unionLoginName},</if>
 			<if test="loginType != null and loginType != ''">#{loginType},</if>
 			<if test="userType != null and userType != ''">#{userType},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="salt != null and salt != ''">#{salt},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="pwdUpdateDate != null">#{pwdUpdateDate},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>
	
</mapper> 