<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth2授权确认</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 400px;
            max-width: 90vw;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .auth-header h2 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        .auth-header p {
            color: #666;
            margin: 0;
            font-size: 14px;
        }
        
        .app-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        
        .app-info h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .app-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .btn-group .btn {
            flex: 1;
            min-width: 120px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .scope-info {
            background: #e7f3ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .scope-info h4 {
            margin: 0 0 10px 0;
            color: #007bff;
            font-size: 14px;
        }
        
        .scope-info ul {
            margin: 0;
            padding-left: 20px;
            color: #666;
            font-size: 13px;
        }
        
        .scope-info li {
            margin-bottom: 5px;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h2>🔐 OAuth2授权确认</h2>
            <p>模拟OAuth2认证服务器授权页面</p>
        </div>
        
        <!-- 错误信息显示 -->
        <div th:if="${error}" class="error-message" th:text="${error}"></div>
        
        <!-- 应用信息 -->
        <div class="app-info">
            <h3>📱 应用信息</h3>
            <p><strong>应用名称:</strong> <span th:text="${clientId}">metaorg_client</span></p>
            <p><strong>回调地址:</strong> <span th:text="${redirectUri}">http://localhost:8488/sso/callback</span></p>
            <p><strong>状态参数:</strong> <span th:text="${state}">state_value</span></p>
        </div>
        
        <!-- 权限范围 -->
        <div class="scope-info">
            <h4>🔑 请求的权限</h4>
            <ul>
                <li>读取用户基本信息</li>
                <li>获取用户邮箱地址</li>
                <li>访问用户部门信息</li>
            </ul>
        </div>
        
        <!-- 授权码显示和确认 -->
        <div class="form-group">
            <label for="authCode">授权码 (可修改):</label>
            <input type="text" id="authCode" name="authCode" th:value="${authCode}">
            <small style="color: #666; font-size: 12px;">可以直接编辑授权码，或点击下方按钮生成新的授权码</small>
        </div>
        
        <div class="form-group">
            <label for="redirectUrl">重定向地址:</label>
            <input type="text" id="redirectUrl" name="redirectUrl" th:value="${redirectUrl}" readonly>
        </div>
        
        <div class="btn-group">
            <button type="button" class="btn btn-secondary" onclick="denyAuthorization()">拒绝授权</button>
            <button type="button" class="btn btn-info" onclick="generateNewCode()">生成新授权码</button>
            <button type="button" class="btn btn-primary" onclick="confirmAuthorization()">确认授权</button>
        </div>
    </div>
    
    <script>
        function denyAuthorization() {
            // 拒绝授权，重定向回应用系统并带上错误信息
            const redirectUri = '[[${redirectUri}]]';
            const state = '[[${state}]]';
            
            let denyUrl = redirectUri + '?error=access_denied';
            if (state) {
                denyUrl += '&state=' + state;
            }
            
            window.location.href = denyUrl;
        }
        
        function generateNewCode() {
            // 生成新的授权码
            const clientId = '[[${clientId}]]';
            const timestamp = Date.now();
            const newCode = 'testAuthCode_' + clientId + '_' + timestamp;
            
            document.getElementById('authCode').value = newCode;
            updateRedirectUrl();
        }
        
        function updateRedirectUrl() {
            // 更新重定向地址
            const redirectUri = '[[${redirectUri}]]';
            const state = '[[${state}]]';
            const authCode = document.getElementById('authCode').value;
            
            let redirectUrl = redirectUri + '?code=' + authCode;
            if (state) {
                redirectUrl += '&state=' + state;
            }
            
            document.getElementById('redirectUrl').value = redirectUrl;
        }
        
        function confirmAuthorization() {
            // 确认授权，重定向到指定地址
            const redirectUrl = document.getElementById('redirectUrl').value;
            window.location.href = redirectUrl;
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('OAuth2授权页面已加载');
            console.log('客户端ID:', '[[${clientId}]]');
            console.log('回调地址:', '[[${redirectUri}]]');
            console.log('状态参数:', '[[${state}]]');
            
            // 初始化重定向地址
            updateRedirectUrl();
            
            // 监听授权码输入变化
            document.getElementById('authCode').addEventListener('input', updateRedirectUrl);
        });
    </script>
</body>
</html>
