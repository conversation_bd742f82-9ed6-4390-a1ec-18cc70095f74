<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.library.mapper.BizStatisticPositionAdjustMapper">
    
    <resultMap type="BizStatisticPositionAdjust" id="BizStatisticPositionAdjustResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="statisticDate"    column="statistic_date"    />
        <result property="adjustDetails"    column="adjust_details"    typeHandler="com.metaorg.common.core.handler.JsonTypeHandler"/>
        <result property="description"    column="description"    />
        <result property="extras"    column="extras"    typeHandler="com.metaorg.common.core.handler.JsonTypeHandler"/>
        <result property="createById"    column="create_by_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateById"    column="update_by_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="BizPersonPositionAdjustProposed" id="BizPersonPositionAdjustProposedResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="personId"    column="person_id"    />
        <result property="personName"    column="person_name"    />
        <result property="citizenId"    column="citizen_id"    />
        <result property="currentPositionName"    column="current_position_name"    />
        <result property="newPositionName"    column="new_position_name"    />
        <result property="originalDeptId"    column="original_dept_id"    />
        <result property="originalDeptName"    column="original_dept_name"    />
        <result property="originalIsLeadingGroup"    column="original_is_leading_group"    />
        <result property="newDeptId"    column="new_dept_id"    />
        <result property="newDeptName"    column="new_dept_name"    />
        <result property="newIsLeadingGroup"    column="new_is_leading_group"    />
        <result property="isAdjustTopLeader"    column="is_adjust_top_leader"    />
        <result property="adjustTopLeaderDeptName"    column="adjust_top_leader_dept_name"    />
        <result property="appointmentArchivingStatus"    column="appointment_archiving_status"    />
        <result property="meetingTime"    column="meeting_time"    />
        <result property="meetingPersonCount"    column="meeting_person_count"    />
        <result property="remark"    column="remark"    />
        <result property="adjustDetails"    column="adjust_details"    typeHandler="com.metaorg.common.core.handler.JsonTypeHandler"/>
        <result property="extras"    column="extras"    typeHandler="com.metaorg.common.core.handler.JsonTypeHandler"/>
        <result property="createById"    column="create_by_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateById"    column="update_by_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="deptHierarchyCTE">
    with recursive dept_hierarchy as (
        select
            dept_id,
            dept_id as root_dept_id,
            dept_name as root_dept_name,
            order_num as root_dept_order_num
        from
            sys_dept
        where
            del_flag = '0'
            <if test="params.deptIds != null and params.deptIds.size() > 0"> and dept_id in 
                <foreach item="item" index="index" collection="params.deptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        union all

        select
            d.dept_id,
            dh.root_dept_id,
            dh.root_dept_name,
            dh.root_dept_order_num
        from
            sys_dept d
        inner join
            dept_hierarchy dh on d.parent_id = dh.dept_id
    )
    </sql>

    <sql id="deptHierarchyPersonCTE">
        dept_persons as (
            select distinct
                dh.root_dept_id,
                dh.dept_id,
                bpd.person_id
            from
                dept_hierarchy dh, 
                biz_person p,
                biz_person_dept bpd
            where
                dh.dept_id = bpd.dept_id and 
                p.id = bpd.person_id and 
                p.del_flag = '0' and
                p.manage_status in (1, 5)
        )
    </sql>

    <sql id="positionAdjustTagBracketsCTE">
        position_adjust_tag_brackets as (
            select
            dict_code,
            dict_label,
            dict_value,
            dict_sort
            from
                sys_dict_data
            where
                dict_type = 'biz_person_position_adjust_tag'
        )
    </sql>

    <sql id="positionAdjustsCTE">
        position_adjusts as (
            select * from (
                select 
                    id,
                    person_id,
                    organization_code,
                    to_date(position_day, 'YYYY-MM-DD') as position_day,
                    jsonb_array_elements_text(adjust_tags) as adjust_tag
                from 
                    biz_person_position
                where
                    position_day ~ '^(\d{4}([-/.]\d{2}([-/.]\d{2}){0,1}|\d{2}(\d{2}){0,1}))$'
            ) a
            <where>
                <if test="params.statisticBeginTime != null and params.statisticBeginTime != ''">
                    position_day >= #{params.statisticBeginTime}
                </if>
                <if test="params.statisticEndTime != null and params.statisticEndTime != ''">
                    and position_day &lt;= #{params.statisticEndTime}
                </if>
            </where>
        )
    </sql>

    <sql id="positionAdjustStatisticCTE">
        position_adjust_statistic as (
            select 
                d.dept_id,
                d.dept_name,
                a.adjust_tag,
                a.position_day as statistic_date,
                count(distinct a.person_id) as total_count
            from 
                position_adjusts a,
                sys_dept d
            where 
                a.organization_code = cast(d.dept_id as varchar)
            group by 
                d.dept_id,
                d.dept_name,
                a.adjust_tag,
                statistic_date
        )
    </sql>

    <sql id="deptDatesCTE">
        dept_dates as (
            select distinct
                dept_id,
                dept_name,
                statistic_date
            from
                position_adjust_statistic
        )
    </sql>

    <sql id="deletePositionAdjustStatisticCTE">
        delete_position_adjust_statistic as (
            delete from 
                biz_statistic_position_adjustment 
            where 
                batch_no like 'XT-%' and
                (
                    dept_id,
                    dept_name,
                    statistic_date
                )
                in 
                (
                    select 
                        dept_id,
                        dept_name,
                        statistic_date 
                    from 
                        dept_dates
                )
        )
    </sql>

    <select id="selectBizStatisticPositionAdjust" parameterType="BizStatisticPositionAdjust" resultMap="BizStatisticPositionAdjustResult">
        <include refid="deptHierarchyCTE"/>
        select 
            a.id,
            a.batch_no,
            a.dept_id,
            a.dept_name,
            a.statistic_date,
            a.adjust_details,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time
        from 
            biz_statistic_position_adjustment a, 
            dept_hierarchy dh
        <where>
            a.dept_id = dh.dept_id
            <if test="params.keyword != null and params.keyword != ''">
             and
             (
                a.batch_no like concat('%', #{params.keyword}, '%') OR
                a.dept_name like concat('%', #{params.keyword}, '%') OR
                a.description like concat('%', #{params.keyword}, '%')
             )
            </if>
            <if test="params.statisticBeginTime != null and params.statisticBeginTime != ''">
                and a.statistic_date &gt;= #{params.statisticBeginTime}
            </if>
            <if test="params.statisticEndTime != null and params.statisticEndTime != ''">
                and a.statistic_date &lt;= #{params.statisticEndTime}
            </if>
            <if test="params.createBeginTime != null and params.createBeginTime != ''">
                and a.create_time &gt;= #{params.createBeginTime}
            </if>
            <if test="params.createEndTime != null and params.createEndTime != ''">
                and a.create_time &lt;= #{params.createEndTime}
            </if>
            <!-- 数据范围过滤 -->
            AND 'params.dataScope' = 'params.dataScope'
        </where>
        order by create_time desc
    </select>

    <select id="selectBizPersonPositionAdjustProposed" parameterType="BizPersonPositionAdjustProposed" resultMap="BizPersonPositionAdjustProposedResult">
        <include refid="deptHierarchyCTE"/>,
        <include refid="deptHierarchyPersonCTE" />
        select 
            a.id,
            a.batch_no,
            a.person_id,
            a.person_name,
            a.citizen_id,
            a.current_position_name,
            a.new_position_name,
            a.original_dept_id,
            a.original_dept_name,
            a.original_is_leading_group,
            a.new_dept_id,
            a.new_dept_name,
            a.new_is_leading_group,
            a.is_adjust_top_leader,
            a.adjust_top_leader_dept_name,
            a.appointment_archiving_status,
            a.meeting_time,
            a.meeting_person_count,
            a.adjust_details,
            a.remark,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time
        from 
            biz_person_position_adjustment_proposed a,
            dept_persons dp
        <where>
            a.person_id = dp.person_id
            <if test="originalIsLeadingGroup != null">
                and a.original_is_leading_group = #{originalIsLeadingGroup}
            </if>
            <if test="newIsLeadingGroup != null">
                and a.new_is_leading_group = #{newIsLeadingGroup}
            </if>
            <if test="isAdjustTopLeader != null">
                and a.is_adjust_top_leader = #{isAdjustTopLeader}
            </if>
            <if test="params.keyword != null and params.keyword != ''">
             and
             (
                a.batch_no like concat('%', #{params.keyword}, '%') OR
                a.person_name like concat('%', #{params.keyword}, '%') OR
                a.citizen_id like concat('%', #{params.keyword}, '%') OR
                a.original_dept_name like concat('%', #{params.keyword}, '%') OR
                a.new_dept_name like concat('%', #{params.keyword}, '%') OR
                a.adjust_top_leader_dept_name like concat('%', #{params.keyword}, '%') OR
                a.current_position_name like concat('%', #{params.keyword}, '%') OR
                a.new_position_name like concat('%', #{params.keyword}, '%') OR
                a.remark like concat('%', #{params.keyword}, '%')
             )
            </if>
            <if test="params.meetingBeginTime != null and params.meetingBeginTime != ''">
                and a.meeting_time &gt;= #{params.meetingBeginTime}
            </if>
            <if test="params.meetingEndTime != null and params.meetingEndTime != ''">
                and a.meeting_time &lt;= #{params.meetingEndTime}
            </if>
            <if test="params.createBeginTime != null and params.createBeginTime != ''">
                and a.create_time &gt;= #{params.createBeginTime}
            </if>
            <if test="params.createEndTime != null and params.createEndTime != ''">
                and a.create_time &lt;= #{params.createEndTime}
            </if>
            <if test="params.adjustTypeList != null and params.adjustTypeList.length > 0">
                and exists (
                    select 1 
                    from 
                        jsonb_array_elements(adjust_details) as adjust_detail 
                    where 
                        (
                            (adjust_detail ->> 'value' = '是') or
                            (adjust_detail ->> 'value' ~ '^\d+$' and (adjust_detail ->> 'value')::integer > 0)
                        ) and
                        adjust_detail ->> 'name' in
                        <foreach item="item" index="index" collection="params.adjustTypeList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                )
            </if>
            <!-- 数据范围过滤 -->
            AND 'params.dataScope' = 'params.dataScope'
        </where>
        order by create_time desc
    </select>

    <select id="selectBizStatisticPositionAdjustById" parameterType="String" resultMap="BizStatisticPositionAdjustResult">
        select * from biz_statistic_position_adjustment where id = #{id}
    </select>

    <select id="selectBizPersonPositionAdjustProposedById" parameterType="String" resultMap="BizPersonPositionAdjustProposedResult">
        select * from biz_person_position_adjustment_proposed where id = #{id}
    </select>

    <insert id="batchInsertBizStatisticPositionAdjust">
        insert into biz_statistic_position_adjustment(
            id, 
            batch_no, 
            dept_id, 
            dept_name, 
            statistic_date, 
            adjust_details, 
            description,
            extras,
            create_by_id,
            create_by, 
            create_time,
            update_by_id,
            update_by, 
            update_time
        ) values
        <foreach item="item" index="index" collection="list" separator=",">
        (
            #{item.id},
            #{item.batchNo},
            #{item.deptId},
            #{item.deptName},
            #{item.statisticDate},
            #{item.adjustDetails, typeHandler=com.metaorg.common.core.handler.JsonTypeHandler}::jsonb,
            #{item.description},
            #{item.extras, typeHandler=com.metaorg.common.core.handler.JsonTypeHandler}::jsonb,
            #{item.createById},
            #{item.createBy},
            #{item.createTime},
            #{item.updateById},
            #{item.updateBy},
            #{item.updateTime}
        )
        </foreach>
    </insert>

     <insert id="batchInsertBizPersonPositionAdjustProposed">
        insert into biz_person_position_adjustment_proposed(
            id, 
            batch_no, 
            person_id, 
            person_name, 
            citizen_id, 
            current_position_name, 
            new_position_name,
            original_dept_id,
            original_dept_name,
            original_is_leading_group,
            new_dept_id,
            new_dept_name,
            new_is_leading_group,
            is_adjust_top_leader,
            adjust_top_leader_dept_name,
            appointment_archiving_status,
            meeting_time,
            meeting_person_count,
            remark,
            adjust_details, 
            extras, 
            create_by_id,
            create_by, 
            create_time, 
            update_by_id,
            update_by, 
            update_time
        ) values
        <foreach item="item" index="index" collection="list" separator=",">
        (
            #{item.id},
            #{item.batchNo},
            #{item.personId},
            #{item.personName},
            #{item.citizenId},
            #{item.currentPositionName},
            #{item.newPositionName},
            #{item.originalDeptId},
            #{item.originalDeptName},
            #{item.originalIsLeadingGroup},
            #{item.newDeptId},
            #{item.newDeptName},
            #{item.newIsLeadingGroup},
            #{item.isAdjustTopLeader},
            #{item.adjustTopLeaderDeptName},
            #{item.appointmentArchivingStatus},
            #{item.meetingTime},
            #{item.meetingPersonCount},
            #{item.remark},
            #{item.adjustDetails, typeHandler=com.metaorg.common.core.handler.JsonTypeHandler}::jsonb,
            #{item.extras, typeHandler=com.metaorg.common.core.handler.JsonTypeHandler}::jsonb,
            #{item.createById},
            #{item.createBy},
            #{item.createTime},
            #{item.updateById},
            #{item.updateBy},
            #{item.updateTime}
        )
        </foreach>
    </insert>

    <delete id="deleteBizStatisticPositionAdjust" parameterType="BizStatisticPositionAdjust">
        delete from biz_statistic_position_adjustment
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="deptId != null and deptId != ''">
                and dept_id = #{deptId}
            </if>
            <if test="statisticDate != null and statisticDate != ''">
                and statistic_date = #{statisticDate}
            </if>
            <if test="params.ids != null and params.ids.length > 0">
                and id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="deleteBizPersonPositionAdjustProposed" parameterType="BizPersonPositionAdjustProposed">
        delete from biz_person_position_adjustment_proposed
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="personId != null and personId != ''">
                and person_id = #{personId}
            </if>
            <if test="citizenId != null and citizenId != ''">
                and citizen_id = #{citizenId}
            </if>
            <if test="meetingTime != null and meetingTime != ''">
                and meeting_time = #{meetingTime}
            </if>
            <if test="params.ids != null and params.ids.length > 0">
                and id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

    <insert id="invokeStatisticPositionAdjust" parameterType="BizStatisticPositionAdjust">
        with
        <include refid="positionAdjustTagBracketsCTE"/>,
        <include refid="positionAdjustsCTE"/>,
        <include refid="positionAdjustStatisticCTE"/>,
        <include refid="deptDatesCTE"/>,
        <include refid="deletePositionAdjustStatisticCTE"/>
        insert into biz_statistic_position_adjustment 
        (
            id,
            batch_no, 
            dept_id,
            dept_name, 
            statistic_date, 
            adjust_details,
            create_by,
            create_time,
            update_by,
            update_time
        )
        select
            md5(random()::text || clock_timestamp()::text)::uuid, 
            'XT-' || to_char(now(), 'yyyymmddhh24miss') || lpad(floor(random() * 1000000)::text, 6, '0'),
            dd.dept_id,
            dd.dept_name,
            dd.statistic_date,
            json_agg(
                json_build_object(
                    'name', ptb.dict_label,
                    'value', coalesce(pas.total_count, 0)
                )
                order by ptb.dict_sort asc
            )::jsonb AS adjust_details, 
            '系统',
            now(), 
            '系统',
            now()
        from 
            dept_dates dd cross join
            position_adjust_tag_brackets ptb left join
            position_adjust_statistic pas on dd.dept_id = pas.dept_id
                and dd.dept_name = pas.dept_name
                and dd.statistic_date = pas.statistic_date
                and ptb.dict_label = pas.adjust_tag
        group by
            dd.dept_id,
            dd.dept_name,
            dd.statistic_date;
    </insert>
</mapper>
