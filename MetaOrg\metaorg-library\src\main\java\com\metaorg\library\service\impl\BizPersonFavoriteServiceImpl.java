package com.metaorg.library.service.impl;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.metaorg.library.domain.BizPersonFavorite;
import com.metaorg.library.mapper.BizPersonFavoriteMapper;
import com.metaorg.library.service.IBizPersonFavoriteService;
import com.metaorg.common.utils.ShiroUtils;

/**
 * 用户收藏Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-05-19
 */
@Service
public class BizPersonFavoriteServiceImpl implements IBizPersonFavoriteService
{
    @Autowired
    private BizPersonFavoriteMapper bizPersonFavoriteMapper;

    /**
     * 查询用户收藏
     * 
     * @param bizPersonFavorite 用户收藏信息
     * @return 用户收藏
     */
    @Override
    public List<BizPersonFavorite> selectBizPersonFavoriteList(BizPersonFavorite bizPersonFavorite)
    {
        return bizPersonFavoriteMapper.selectBizPersonFavoriteList(bizPersonFavorite);
    }

    /**
     * 通过用户ID查询用户收藏
     * 
     * @param userId 用户ID
     * @param personId 人员ID
     * @return 用户收藏列表
     */
    @Override
    public List<BizPersonFavorite> selectBizPersonFavoriteByUserId(Long userId, String personId)
    {
        return bizPersonFavoriteMapper.selectBizPersonFavoriteByUserId(userId, personId);
    }

     /**
     * 通过人员ID查询用户收藏
     * 
     * @param personId 人员ID
     * @param userId 用户ID
     * @return 用户收藏列表
     */
    @Override
    public List<BizPersonFavorite> selectBizPersonFavoriteByPersonId(String personId, Long userId)
    {
        return bizPersonFavoriteMapper.selectBizPersonFavoriteByPersonId(personId, userId);
    }

    /**
     * 批量新增用户收藏信息
     * 
     * @param bizPersonFavoriteList 用户收藏列表
     */
    @Override
    public int insertBizPersonFavorite(Long userId, String[] personIds)
    {
        int rows = 0;
        if (StringUtils.isNotNull(personIds))
        {
            // 先删除用户收藏
            bizPersonFavoriteMapper.deleteBizPersonFavoriteByUserId(userId, personIds);

            String createUserId = ShiroUtils.getUserId().toString();
            String createUserName = ShiroUtils.getUserName();
            Date currentDate = DateUtils.getNowDate();

            List<BizPersonFavorite> list = new ArrayList<BizPersonFavorite>();
            for (String personId : personIds)
            {
                BizPersonFavorite ur = new BizPersonFavorite();
                ur.setId(IdUtils.fastSimpleUUID());
                ur.setUserId(userId);
                ur.setPersonId(personId);
                ur.setCreateById(createUserId);
                ur.setCreateBy(createUserName);
                ur.setUpdateById(createUserId);
                ur.setUpdateBy(createUserName);
                ur.setCreateTime(currentDate);
                ur.setUpdateTime(currentDate);
                list.add(ur);
            }
            if (list.size() > 0)
            {
                rows = bizPersonFavoriteMapper.batchBizPersonFavorite(list);
            }
        }
        return rows;
    }

    /**
     * 通过用户ID删除用户收藏
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteBizPersonFavoriteByUserId(Long userId, String[] personIds)
    {
        return bizPersonFavoriteMapper.deleteBizPersonFavoriteByUserId(userId, personIds);
    }

    /**
     * 删除用户收藏
     * 
     * @param bizPersonFavorite 用户收藏信息
     * @return 结果
     */
    @Override
    public int deleteBizPersonFavoriteInfo(BizPersonFavorite bizPersonFavorite)
    {
        return bizPersonFavoriteMapper.deleteBizPersonFavoriteInfo(bizPersonFavorite);
    }

    /**
     * 批量取消授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要删除的用户数据ID
     * @return 结果
     */
    @Override
    public int deleteBizPersonFavoriteInfos(String personId, Long[] userIds)
    {
        return bizPersonFavoriteMapper.deleteBizPersonFavoriteInfos(personId, userIds);
    }

    /**
     * 通过用户ID和ID列表删除用户收藏
     * 
     * @param userId 用户ID
     * @param ids ID列表
     * @return 结果
     */
    @Override
    public int deleteBizPersonFavoriteByIds(Long userId, String[] ids)
    {
        return bizPersonFavoriteMapper.deleteBizPersonFavoriteByIds(userId, ids);
    }
}
