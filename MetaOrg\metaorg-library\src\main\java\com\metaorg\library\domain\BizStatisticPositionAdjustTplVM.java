package com.metaorg.library.domain;

import java.util.*;
import com.metaorg.common.annotation.Excel;
import com.metaorg.common.annotation.ExcelDynamicColumn;
import com.metaorg.common.core.domain.BaseEntity;
import javax.validation.constraints.*;

import com.metaorg.common.core.domain.entity.SysDept;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 干部职务调整统计导入对象
 *
 * <AUTHOR>
 * @date 2025-08-18
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BizStatisticPositionAdjustTplVM extends BaseEntity
{
    /** 批次代码 */
    private String batchNo;

    /** 机构名称 */
    @NotBlank
    @Excel(name = "机构名称")
    private String deptName;

    /** 调整时间，格式：yyyy-MM-DD */
    @NotBlank
    @Excel(name = "时间", dateFormat = "yyyy-MM-dd")
    private String statisticDate;

    /**
     * 动态详情字段。
     * startColumnName = "机构名称" 表示从 "机构名称" 这一列之后的所有列都将被映射到这个Map中。
     * valueType = Integer.class 表示Map的值将被转换为Integer类型。
     * defaultValue = "0" 表示如果单元格为空或无效，则存入0。
     */
    @ExcelDynamicColumn(startColumnName = "机构名称", valueType = Integer.class, defaultValue = "0")
    private Map<String, Integer> adjustDetails;

    /** 行社/部门对象 */
    private SysDept dept;

    /** 错误信息 */
    private String errMsg;
}
