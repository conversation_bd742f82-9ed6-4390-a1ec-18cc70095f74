package com.metaorg.library.domain;
import java.util.*;
import com.metaorg.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.metaorg.common.utils.uuid.IdUtils;
import com.metaorg.common.utils.StringUtils;

public class BizPersonPositionAdjustProposed extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 统计标识 */
    private String id;
    /** 批次代码 */
    private String batchNo;
    /** 人员标识 */
    private String personId;
    /** 人员姓名 */
    private String personName;
    /** 身份证号码 */
    private String citizenId;
    /** 现任职务名称 */
    private String currentPositionName;
    /** 新职务名称 */
    private String newPositionName;
    /** 调整前机构标识 */
    private String originalDeptId;
    /** 调整前机构名称 */
    private String originalDeptName;
    /** 调整前是否班子 */
    private Boolean originalIsLeadingGroup;
    /** 调整后机构标识 */
    private String newDeptId;
    /** 调整后机构名称 */
    private String newDeptName;
    /** 调整后是否班子 */
    private Boolean newIsLeadingGroup;
    /** 是否一把手 */
    private Boolean isAdjustTopLeader;
    /** 调整一把手涉及行社 */
    private String adjustTopLeaderDeptName;
    /** 干部任免表归档情况 */
    private String appointmentArchivingStatus;
    /** 党委会时间 */
    private String meetingTime;
    /** 上会人数 */
    private String meetingPersonCount;
    /** 备注 */
    private String remark;
    /** 调整详情 */
    private List<Map<String, Object>> adjustDetails;
    /** 扩展信息 */
    private Map<String, Object> extras;

    public static BizPersonPositionAdjustProposed fromBizPersonPositionAdjustProposedTplVM(BizPersonPositionAdjustProposedTplVM pvm) {
        BizPersonPositionAdjustProposed positionAdjust = new BizPersonPositionAdjustProposed();

        String batchNo = pvm.getBatchNo();

        String personId = null;
        String personName = "";
        if (pvm.getPerson() != null){
            personId = pvm.getPerson().getId();
            personName = pvm.getPerson().getName();
        }

        String originalDeptId = null;
        String originalDeptName = "";
        if (pvm.getOriginalDept() != null){
            originalDeptId = pvm.getOriginalDept().getDeptId().toString();
            originalDeptName = pvm.getOriginalDept().getDeptName();
        }

        String newDeptId = null;
        String newDeptName = "";
        if (pvm.getNewDept() != null){
            newDeptId = pvm.getNewDept().getDeptId().toString();
            newDeptName = pvm.getNewDept().getDeptName();
        }

        positionAdjust.setId(IdUtils.fastUUID());
        positionAdjust.setBatchNo(batchNo);
        positionAdjust.setPersonId(personId);
        positionAdjust.setPersonName(personName);
        positionAdjust.setCitizenId(pvm.getCitizenId());
        positionAdjust.setCurrentPositionName(pvm.getCurrentPositionName());
        positionAdjust.setNewPositionName(pvm.getNewPositionName());
        positionAdjust.setOriginalDeptId(originalDeptId);
        positionAdjust.setOriginalDeptName(originalDeptName);
        positionAdjust.setOriginalIsLeadingGroup("是".equals(pvm.getOriginalIsLeadingGroup()));
        positionAdjust.setNewDeptId(newDeptId);
        positionAdjust.setNewDeptName(newDeptName);
        positionAdjust.setNewIsLeadingGroup("是".equals(pvm.getNewIsLeadingGroup()));
        positionAdjust.setIsAdjustTopLeader("是".equals(pvm.getIsAdjustTopLeader()));
        positionAdjust.setAdjustTopLeaderDeptName(pvm.getAdjustTopLeaderDeptName());
        positionAdjust.setAppointmentArchivingStatus(pvm.getAppointmentArchivingStatus());
        positionAdjust.setMeetingTime(pvm.getMeetingTime());
        positionAdjust.setMeetingPersonCount(pvm.getMeetingPersonCount());
        positionAdjust.setRemark(pvm.getRemark());

        List<String> excludeColumns = Arrays.asList("调整前机构", "调整后机构", "调整一把手涉及行社", "干部任免表归档情况");
        List<Map<String, Object>> adjustDetails = new ArrayList<>();
        if (pvm.getAdjustDetails() != null) {
            for (Map.Entry<String, String> entry : pvm.getAdjustDetails().entrySet()) {
                String columnName = entry.getKey();

                if (StringUtils.isEmpty(columnName)) {
                    continue;
                }

                if (excludeColumns.contains(columnName)) {
                    continue;
                }

                String value = entry.getValue();
                Map<String, Object> adjustDetail = new HashMap<>();
                adjustDetail.put("name", columnName);
                adjustDetail.put("value", value);
                adjustDetails.add(adjustDetail);
            }
        }
        positionAdjust.setAdjustDetails(adjustDetails);
   
        return positionAdjust;
    }

    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getCitizenId() {
        return citizenId;
    }

    public void setCitizenId(String citizenId) {
        this.citizenId = citizenId;
    }

    public String getCurrentPositionName() {
        return currentPositionName;
    }

    public void setCurrentPositionName(String currentPositionName) {
        this.currentPositionName = currentPositionName;
    }

    public String getNewPositionName() {
        return newPositionName;
    }

    public void setNewPositionName(String newPositionName) {
        this.newPositionName = newPositionName;
    }

    public String getOriginalDeptId() {
        return originalDeptId;
    }

    public void setOriginalDeptId(String originalDeptId) {
        this.originalDeptId = originalDeptId;
    }

    public String getOriginalDeptName() {
        return originalDeptName;
    }

    public void setOriginalDeptName(String originalDeptName) {
        this.originalDeptName = originalDeptName;
    }

    public Boolean getOriginalIsLeadingGroup() {
        return originalIsLeadingGroup;
    }

    public void setOriginalIsLeadingGroup(Boolean originalIsLeadingGroup) {
        this.originalIsLeadingGroup = originalIsLeadingGroup;
    }

    public String getNewDeptId() {
        return newDeptId;
    }

    public void setNewDeptId(String newDeptId) {
        this.newDeptId = newDeptId;
    }

    public String getNewDeptName() {
        return newDeptName;
    }

    public void setNewDeptName(String newDeptName) {
        this.newDeptName = newDeptName;
    }

    public Boolean getNewIsLeadingGroup() {
        return newIsLeadingGroup;
    }

    public void setNewIsLeadingGroup(Boolean newIsLeadingGroup) {
        this.newIsLeadingGroup = newIsLeadingGroup;
    }

    public Boolean getIsAdjustTopLeader() {
        return isAdjustTopLeader;
    }

    public void setIsAdjustTopLeader(Boolean isAdjustTopLeader) {
        this.isAdjustTopLeader = isAdjustTopLeader;
    }

    public String getAdjustTopLeaderDeptName() {
        return adjustTopLeaderDeptName;
    }

    public void setAdjustTopLeaderDeptName(String adjustTopLeaderDeptName) {
        this.adjustTopLeaderDeptName = adjustTopLeaderDeptName;
    }

    public String getAppointmentArchivingStatus() {
        return appointmentArchivingStatus;
    }

    public void setAppointmentArchivingStatus(String appointmentArchivingStatus) {
        this.appointmentArchivingStatus = appointmentArchivingStatus;
    }

    public String getMeetingTime() {
        return meetingTime;
    }

    public void setMeetingTime(String meetingTime) {
        this.meetingTime = meetingTime;
    }

    public String getMeetingPersonCount() {
        return meetingPersonCount;
    }
    
    public void setMeetingPersonCount(String meetingPersonCount) {
        this.meetingPersonCount = meetingPersonCount;
    }

    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<Map<String, Object>> getAdjustDetails() {
        return adjustDetails;
    }
    
    public void setAdjustDetails(List<Map<String, Object>> adjustDetails) {
        this.adjustDetails = adjustDetails;
    }

    public Map<String, Object> getExtras() {
        return extras;
    }
    
    public void setExtras(Map<String, Object> extras) {
        this.extras = extras;
    }   

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("batchNo", getBatchNo())
            .append("personId", getPersonId())
            .append("personName", getPersonName())
            .append("citizenId", getCitizenId())
            .append("currentPositionName", getCurrentPositionName())
            .append("newPositionName", getNewPositionName())
            .append("originalDeptId", getOriginalDeptId())
            .append("originalDeptName", getOriginalDeptName())
            .append("originalIsLeadingGroup", getOriginalIsLeadingGroup())
            .append("newDeptId", getNewDeptId())
            .append("newDeptName", getNewDeptName())
            .append("newIsLeadingGroup", getNewIsLeadingGroup())
            .append("isAdjustTopLeader", getIsAdjustTopLeader())
            .append("adjustTopLeaderDeptName", getAdjustTopLeaderDeptName())
            .append("appointmentArchivingStatus", getAppointmentArchivingStatus())
            .append("meetingTime", getMeetingTime())
            .append("meetingPersonCount", getMeetingPersonCount())
            .append("adjustDetails", getAdjustDetails())
            .append("remark", getRemark())
            .append("extras", getExtras())
            .toString();
    }
}
