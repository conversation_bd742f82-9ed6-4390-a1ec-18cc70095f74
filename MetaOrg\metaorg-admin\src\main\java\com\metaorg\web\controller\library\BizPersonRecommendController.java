package com.metaorg.web.controller.library;

import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.library.domain.*;
import com.metaorg.library.service.*;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.utils.DataScopeUtils;
import com.metaorg.web.controller.tool.POIUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.shiro.authz.annotation.Logical;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 推荐班子人选Controller
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Controller
@RequestMapping("/library/recommend")
public class BizPersonRecommendController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(BizPersonRecommendController.class);
    private String prefix = "library/recommend";

    @Autowired
    private IBizPersonRecommend bizPersonRecommendService;

    @Autowired
    private IBizPersonEducationService educationService;

    @Autowired
    private IBizPersonPoliticsService politicsService;

    @Autowired
    private IBizPersonResumeService resumeService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private  IBizPersonPositionService positionService;

    @RequiresPermissions("library:recommend:view")
    @GetMapping()
    public String recommend(ModelMap mmap)
    {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if(StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        return prefix + "/recommend";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizPersonRecommend bizPersonRecommend, Map<String, Object> paramMap){
        long deptId = -1;
        if(paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonRecommend.getParams().put("deptId", deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonRecommend, getSysUser(), "d", "", "", deptId);
        }
    }

    /**
     * 查询推荐班子人选列表
     */
    @RequiresPermissions(value={"library:recommend:list","library:person:view"},logical= Logical.OR)
    @PostMapping({"/list/{personId}", "/list"})
    @ResponseBody
    public TableDataInfo list(@RequestParam Map<String, Object> paramMap, BizPersonRecommend bizPersonRecommend)
    {
        getQueryOptions(bizPersonRecommend, paramMap);
        startPage();
        List<BizPersonRecommend> list = bizPersonRecommendService.selectBizPersonRecommendList(bizPersonRecommend);
        return getDataTable(list);
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename)
    {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists())
        {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

    /**
     * 导出推荐班子人选列表
     */
    @RequiresPermissions("library:recommend:export")
    @Log(title = "推荐班子人选", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestParam Map<String, Object> paramMap, BizPersonRecommend bizPersonRecommend, HttpServletResponse resp)
    {
        getQueryOptions(bizPersonRecommend, paramMap);
        bizPersonRecommend.getParams().put("orderBy", "recommend_day desc,a.id desc");
        List<BizPersonRecommend> list = bizPersonRecommendService.selectBizPersonRecommendList(bizPersonRecommend);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "推荐班子人选_" + System.currentTimeMillis() +".xls";
        try
        {
            List<BizPerson> personList = new ArrayList<>();
            for(BizPersonRecommend pr : list){
                personList.add(pr.getPerson());
            }
            positionService.fillPositionForPersons(personList);

            File file = new File("./config/template/tjbzrx_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            //通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            for(int i = 0; i < list.size(); i++) {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                row.getCell(0).setCellValue(i + 1);  //序号
                row.getCell(1).setCellValue(list.get(i).getPerson().getDept().getDeptName());  //组织名称
                row.getCell(2).setCellValue(list.get(i).getPerson().getJobUnit());  //职务
                row.getCell(3).setCellValue(list.get(i).getPerson().getName());  //姓名
                row.getCell(4).setCellValue(list.get(i).getPerson().getSex());  //性别
                row.getCell(5).setCellValue(list.get(i).getPerson().getNation());  //民族
                row.getCell(6).setCellValue(list.get(i).getPerson().getBirthday() == null ? "" : list.get(i).getPerson().getBirthday().replace("-", ".").replace("Invalid date", ""));  //出生年月
                row.getCell(7).setCellValue(list.get(i).getPerson().getAge().replace("岁", ""));  //年龄

                //学历
                BizPersonEducation education = new BizPersonEducation();  //学历对象
                education.setPersonId(list.get(i).getPersonId());
                List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);
                BizPersonEducation highestEducation = null; //最高学历
                String ZuiGaoXueLi = "";  //最高学历
                String ZuiGaoBiYeYuanXiao = "";  //最高学校及院系
                // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum)); //正序
                if(educations != null) {
                    for (BizPersonEducation item : educations) {
                        //根据是否最高学历标识获取最高学历对象
                        if(item.getHighestDegreeFlag().equalsIgnoreCase("Y")) {
                            highestEducation = item;
                        }
                    }
                    //如果没有标识最高学历获取最后一个
                    if(highestEducation == null && educations.size() > 0) {
                        highestEducation = educations.get(educations.size() - 1);
                    }
                    if(highestEducation != null) {
                        ZuiGaoBiYeYuanXiao = highestEducation.getEduSchool();
                        ZuiGaoXueLi = highestEducation.getEduName();
                    }
                }
                row.getCell(8).setCellValue(ZuiGaoXueLi);  //最高学历

                //通过政治情况获取入党时间
                BizPersonPolitics politics = new BizPersonPolitics();
                politics.setPersonId(list.get(i).getPersonId());
                List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);
                BizPersonPolitics bizPersonPolitics = null;
                if(politicsList != null) {
                    bizPersonPolitics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
                        @Override
                        public boolean test(BizPersonPolitics bizPersonPolitics) {
                            return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
                        }
                    }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
                        @Override
                        public BizPersonPolitics get() {
                            return new BizPersonPolitics();
                        }
                    });
                }
                //入党时间
                if(bizPersonPolitics != null && bizPersonPolitics.getJoinPartyDay() != null){
                    row.getCell(9).setCellValue(bizPersonPolitics.getJoinPartyDay().replace("-", ".").replace("Invalid date", ""));
                }else{
                    row.getCell(9).setCellValue("");
                }
                //参加工作时间
                row.getCell(10).setCellValue(list.get(i).getPerson().getJobDay() == null ? "" : list.get(i).getPerson().getJobDay().replace("-", ".").replace("Invalid date", ""));
                //根据最新职职务级职务获取
                if(list.get(i).getPerson() != null && list.get(i).getPerson().getPositions() !=null
                        && list.get(i).getPerson().getPositions().size() > 0){
                    BizPersonPosition personPosition = positionService.mergePositionsIntoSingle(list.get(i).getPerson().getPositions());
                    row.getCell(11).setCellValue(personPosition.getPositionDay().replace("-", "."));
                    row.getCell(12).setCellValue(personPosition.getSameRankDay().replace("-", "."));
                }else{
                    //从简历中获取最新任职时间
                    BizPersonResume resume = new BizPersonResume();
                    resume.setPersonId(list.get(i).getPersonId());
                    List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume, "start_date desc");
                    BizPersonResume lastResume = null;
                    if(resumes != null) {
                        lastResume = resumes.stream().findFirst().orElseGet(new Supplier<BizPersonResume>() {
                            @Override
                            public BizPersonResume get() {
                                return new BizPersonResume();
                            }
                        });
                    }
                    if(lastResume != null && lastResume.getStartDate() != null){
                        row.getCell(11).setCellValue(lastResume.getStartDate().replace("-", ".").replace("Invalid date", ""));
                    }else{
                        row.getCell(11).setCellValue("");
                    }
                    row.getCell(12).setCellValue("");  //任同职级时间
                }
                row.getCell(13).setCellValue(ZuiGaoBiYeYuanXiao);  //毕业学校及专业
                //推荐时间
                row.getCell(14).setCellValue(list.get(i).getRecommendDay() == null ? "" : list.get(i).getRecommendDay().replace("-", ".").replace("Invalid date", ""));
                row.getCell(15).setCellValue(list.get(i).getRecommendPosition());  //推荐岗位
                row.getCell(16).setCellValue(list.get(i).getApprovePaperNo());  //文号
                row.getCell(17).setCellValue(list.get(i).getNote());  //备注
            }
            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        }catch (Exception e)
        {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        }
        finally
        {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 新增推荐班子人选
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        return prefix + "/add";
    }

    /**
     * 新增保存推荐班子人选
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "推荐班子人选", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "推荐班子人选", tableName = PersonTable.BIZ_PERSON_RECOMMEND, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonRecommend bizPersonRecommend)
    {
        bizPersonRecommend.setCreateBy(getLoginName());
        return toAjax(bizPersonRecommendService.insertBizPersonRecommend(bizPersonRecommend));
    }

    /**
     * 修改推荐班子人选
     */
    @RequiresPermissions(value={"library:person:edit","library:recommend:edit"},logical= Logical.OR)
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonRecommend bizPersonRecommend = bizPersonRecommendService.selectBizPersonRecommendById(id);
        mmap.put("bizPersonRecommend", bizPersonRecommend);
        return prefix + "/edit";
    }

    /**
     * 修改保存推荐班子人选
     */
    @RequiresPermissions(value={"library:person:edit","library:recommend:edit"},logical= Logical.OR)
    @Log(title = "推荐班子人选", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "推荐班子人选", tableName = PersonTable.BIZ_PERSON_RECOMMEND, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonRecommend bizPersonRecommend)
    {
        bizPersonRecommend.setUpdateBy(getLoginName());
        return toAjax(bizPersonRecommendService.updateBizPersonRecommend(bizPersonRecommend));
    }

    /**
     * 删除推荐班子人选
     */
    @RequiresPermissions(value={"library:person:edit","library:recommend:remove"},logical= Logical.OR)
    @Log(title = "推荐班子人选", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "推荐班子人选", tableName = PersonTable.BIZ_PERSON_RECOMMEND, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonRecommendService.deleteBizPersonRecommendByIds(ids));
    }
}
