#!/bin/bash

# 新疆金融监管局处罚信息爬虫启动脚本

set -e

echo "=== 新疆金融监管局处罚信息爬虫启动脚本 ==="

# 检查环境变量
if [ -z "$OPENAI_API_KEY" ]; then
    echo "错误: 未设置 OPENAI_API_KEY 环境变量"
    exit 1
fi

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "警告: .env 文件不存在，使用默认配置"
    cp .env.example .env
fi

# 创建必要的目录
mkdir -p logs
mkdir -p config

# 安装依赖
echo "安装Python依赖..."
uv sync

# 安装playwright浏览器
echo "安装Playwright浏览器..."
uv run playwright install

# 运行健康检查
echo "执行健康检查..."
uv run python -m nfra_crawler.cli health

# 启动应用
echo "启动NFRA爬虫应用..."
uv run python -m nfra_crawler.main
