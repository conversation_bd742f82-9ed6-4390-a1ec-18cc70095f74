package com.metaorg.library.domain;

import java.util.*;
import com.metaorg.common.annotation.Excel;
import com.metaorg.common.annotation.ExcelDynamicColumn;
import com.metaorg.common.core.domain.BaseEntity;
import javax.validation.constraints.*;

import com.metaorg.common.core.domain.entity.SysDept;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 干部职务调整统计导入对象
 *
 * <AUTHOR>
 * @date 2025-08-18
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BizPersonPositionAdjustProposedTplVM extends BaseEntity
{
    /** 批次代码 */
    private String batchNo;

    /** 姓名 */
    @NotBlank
    @Excel(name = "姓名")
    private String personName;

    /** 身份证号码 */
    @NotBlank
    @Excel(name = "身份证号码")
    private String citizenId;

    /** 党委会时间 */
    @Excel(name = "党委会时间")
    private String meetingTime;

    /** 上会人数 */
    @Excel(name = "上会研究人数")
    private String meetingPersonCount;

    /** 现任职务 */
    @Excel(name = "现任职务")
    private String currentPositionName;

    /** 新职务名称 */
    @Excel(name = "拟任免（聘/解聘）职务")
    private String newPositionName;

    /** 调整前机构名称 */
    @Excel(name = "调整前机构")
    private String originalDeptName;

    /** 调整后机构名称 */
    @Excel(name = "调整后机构")
    private String newDeptName;

    /** 调整前是否班子 */
    @Excel(name = "调整前是否班子")
    private String originalIsLeadingGroup;

    /** 调整后是否班子 */
    @Excel(name = "调整后是否班子")
    private String newIsLeadingGroup;

    /** 是否一把手调整 */
    @Excel(name = "是否一把手调整")
    private String isAdjustTopLeader;

    /** 调整一把手涉及行社 */
    @Excel(name = "调整一把手涉及行社")
    private String adjustTopLeaderDeptName;

    /** 干部任免表归档情况 */
    @Excel(name = "干部任免表归档情况")
    private String appointmentArchivingStatus;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /**
     * 动态详情字段。
     * startColumnName = "备注" 表示从 "备注" 这一列之后的所有列都将被映射到这个Map中。
     * valueType = String.class 表示Map的值将被转换为String类型。
     * defaultValue = "" 表示如果单元格为空或无效，则存入""。
     */
    @ExcelDynamicColumn(startColumnName = "干部任免表归档情况", valueType = String.class, defaultValue = "")
    private Map<String, String> adjustDetails;

    /** 人员信息 */
    private BizPerson person;

    /** 调整前机构 */
    private SysDept originalDept;

    /** 调整后机构 */
    private SysDept newDept;

    /** 错误信息 */
    private String errMsg;
}
