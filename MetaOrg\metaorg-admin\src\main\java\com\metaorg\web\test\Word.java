package com.metaorg.web.test;


import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.file.FileUtils;
import org.apache.poi.util.IOUtils;

import javax.imageio.ImageIO;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;

public class Word {
    public static void main(String[] args) throws IOException {
//        String source = "C:\\Users\\<USER>\\Desktop\\任免表.docx";
//        String target = "C:\\Users\\<USER>\\Desktop\\1.docx";
//        String imgPath = "C:\\Users\\<USER>\\Desktop\\QQ截图20230603121023.png";
//        FileInputStream imgInputStream = null;
//        FileOutputStream fileOutputStream = null;
//        try{
//            imgInputStream = new FileInputStream(imgPath);
//            FileInputStream finalImgInputStream = imgInputStream;
//            XWPFTemplate template = XWPFTemplate.compile(source).render(
//                    new HashMap<String, Object>(){{
//                        put("title", "Hi, poi-tl Word模板引擎");
//                        put("buffered", Pictures.ofBufferedImage(ImageIO.read(finalImgInputStream)
//                                , PictureType.PNG).size(100, 130).create());
//                    }});
//            fileOutputStream = new FileOutputStream(target);
//            template.writeAndClose(fileOutputStream);
//        }catch (Exception e) {
//            throw new UtilException("请联系网站管理员！");
//        }finally {
//            FileUtils.closeFile(imgInputStream);
//            FileUtils.closeFile(fileOutputStream);
//        }
    }
}
