package com.metaorg.web.controller.api.message;

import java.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.metaorg.library.service.IBizMessageNotifySubscriberService;
import com.metaorg.common.core.domain.ResponseBodyApi;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import com.metaorg.library.domain.BizMessageNotifySubscriber;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.enums.BusinessType;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.utils.DateUtils;
import org.springframework.web.bind.annotation.RequestBody;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.core.text.Convert;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/api/messages/notify/subscribes")
public class MessageNotifySubscribeController extends BaseController  {
    @Autowired
    private IBizMessageNotifySubscriberService bizMessageNotifySubscriberService;

    /**
     * 获取列表查询条件
    */
    private void getQueryOptions(BizMessageNotifySubscriber bizMessageNotifySubscriber, Map<String, Object> paramMap) {
        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizMessageNotifySubscriber.getParams().put("keyword", keyword);
        }
    }

    @Log(title = "PAD端-查询消息通知订阅列表", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("")
    @ResponseBody
    public ResponseBodyApi<List<BizMessageNotifySubscriber>> getSubscribes(HttpServletResponse response, @RequestParam Map<String, Object> paramMap) {
        BizMessageNotifySubscriber bizMessageNotifySubscriber = new BizMessageNotifySubscriber();
       
        String subscriberId = getUserId().toString();
        bizMessageNotifySubscriber.setSubscriberId(subscriberId);

        getQueryOptions(bizMessageNotifySubscriber, paramMap);
        
        startPage();
        List<BizMessageNotifySubscriber> list = bizMessageNotifySubscriberService.selectBizMessageNotifySubscriberList(bizMessageNotifySubscriber);

        ResponseBodyApi<List<BizMessageNotifySubscriber>> rsBody = new ResponseBodyApi<List<BizMessageNotifySubscriber>>(list);
        rsBody.setMessage("OK");
        return rsBody;
    }

    @Log(title = "PAD端-订阅消息通知", businessType = BusinessType.UPDATE, isSaveResponseData = true)
    @PostMapping("")
    @ResponseBody
    public ResponseBodyApi<BizMessageNotifySubscriber> subscribe(@RequestBody Map<String, Object> paramMap) {
        String templateId = Convert.toStr(paramMap.get("template_id"), "");
        if (StringUtils.isEmpty(templateId)) {
            return new ResponseBodyApi<BizMessageNotifySubscriber>("500",false, "模板标识不能为空");
        }

        Boolean subscribedSystemNotify = Convert.toBool(paramMap.get("subscribed_system_notify"), false);
        Boolean subscribedSmsNotify = Convert.toBool(paramMap.get("subscribed_sms_notify"), false);
        Boolean subscribedAppNotify = Convert.toBool(paramMap.get("subscribed_app_notify"), false);

        String subscriberId = getUserId().toString();
        String subscriberName = getUserName();

        String[] receiverIds = new String[] { subscriberId };
        bizMessageNotifySubscriberService.deleteBizMessageNotifySubscriberByTemplateId(templateId, receiverIds);

        BizMessageNotifySubscriber subscriber = new BizMessageNotifySubscriber();
        subscriber.setTemplateId(templateId);
        subscriber.setSubscriberId(subscriberId);
        subscriber.setSubscriberName(subscriberName);
        subscriber.setSubscribedSystemNotify(subscribedSystemNotify);
        subscriber.setSubscribedSmsNotify(subscribedSmsNotify);
        subscriber.setSubscribedAppNotify(subscribedAppNotify);
        subscriber.setCreateBy(subscriberName);
        subscriber.setCreateById(subscriberId);
        subscriber.setUpdateBy(subscriberName);
        subscriber.setUpdateById(subscriberId);

        Date currentDate = DateUtils.getNowDate();
        subscriber.setCreateTime(currentDate);
        subscriber.setUpdateTime(currentDate);

        bizMessageNotifySubscriberService.insertBizMessageNotifySubscriber(subscriber);

        ResponseBodyApi<BizMessageNotifySubscriber> rsBody = new ResponseBodyApi<BizMessageNotifySubscriber>(subscriber);
        rsBody.setMessage("OK");
        return rsBody;
    }
}
