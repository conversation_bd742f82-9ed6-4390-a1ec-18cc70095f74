/**
 * Minified by jsDelivr using Terser v5.39.0.
 * Original file: /npm/big.js@7.0.1/big.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(r){"use strict";var e,t=1e6,n=1e6,i="[big.js] ",o=i+"Invalid ",s=o+"decimal places",c=o+"rounding mode",f=i+"Division by zero",u={},h=void 0,l=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function a(r,e,t,n){var i=r.c;if(t===h&&(t=r.constructor.RM),0!==t&&1!==t&&2!==t&&3!==t)throw Error(c);if(e<1)n=3===t&&(n||!!i[0])||0===e&&(1===t&&i[0]>=5||2===t&&(i[0]>5||5===i[0]&&(n||i[1]!==h))),i.length=1,n?(r.e=r.e-e+1,i[0]=1):i[0]=r.e=0;else if(e<i.length){if(n=1===t&&i[e]>=5||2===t&&(i[e]>5||5===i[e]&&(n||i[e+1]!==h||1&i[e-1]))||3===t&&(n||!!i[0]),i.length=e,n)for(;++i[--e]>9;)if(i[e]=0,0===e){++r.e,i.unshift(1);break}for(e=i.length;!i[--e];)i.pop()}return r}function p(r,e,t){var n=r.e,i=r.c.join(""),o=i.length;if(e)i=i.charAt(0)+(o>1?"."+i.slice(1):"")+(n<0?"e":"e+")+n;else if(n<0){for(;++n;)i="0"+i;i="0."+i}else if(n>0)if(++n>o)for(n-=o;n--;)i+="0";else n<o&&(i=i.slice(0,n)+"."+i.slice(n));else o>1&&(i=i.charAt(0)+"."+i.slice(1));return r.s<0&&t?"-"+i:i}u.abs=function(){var r=new this.constructor(this);return r.s=1,r},u.cmp=function(r){var e,t=this,n=t.c,i=(r=new t.constructor(r)).c,o=t.s,s=r.s,c=t.e,f=r.e;if(!n[0]||!i[0])return n[0]?o:i[0]?-s:0;if(o!=s)return o;if(e=o<0,c!=f)return c>f^e?1:-1;for(s=(c=n.length)<(f=i.length)?c:f,o=-1;++o<s;)if(n[o]!=i[o])return n[o]>i[o]^e?1:-1;return c==f?0:c>f^e?1:-1},u.div=function(r){var e=this,n=e.constructor,i=e.c,o=(r=new n(r)).c,c=e.s==r.s?1:-1,u=n.DP;if(u!==~~u||u<0||u>t)throw Error(s);if(!o[0])throw Error(f);if(!i[0])return r.s=c,r.c=[r.e=0],r;var l,p,g,w,v,d=o.slice(),m=l=o.length,E=i.length,b=i.slice(0,l),P=b.length,D=r,M=D.c=[],x=0,y=u+(D.e=e.e-r.e)+1;for(D.s=c,c=y<0?0:y,d.unshift(0);P++<l;)b.push(0);do{for(g=0;g<10;g++){if(l!=(P=b.length))w=l>P?1:-1;else for(v=-1,w=0;++v<l;)if(o[v]!=b[v]){w=o[v]>b[v]?1:-1;break}if(!(w<0))break;for(p=P==l?o:d;P;){if(b[--P]<p[P]){for(v=P;v&&!b[--v];)b[v]=9;--b[v],b[P]+=10}b[P]-=p[P]}for(;!b[0];)b.shift()}M[x++]=w?g:++g,b[0]&&w?b[P]=i[m]||0:b=[i[m]]}while((m++<E||b[0]!==h)&&c--);return M[0]||1==x||(M.shift(),D.e--,y--),x>y&&a(D,y,n.RM,b[0]!==h),D},u.eq=function(r){return 0===this.cmp(r)},u.gt=function(r){return this.cmp(r)>0},u.gte=function(r){return this.cmp(r)>-1},u.lt=function(r){return this.cmp(r)<0},u.lte=function(r){return this.cmp(r)<1},u.minus=u.sub=function(r){var e,t,n,i,o=this,s=o.constructor,c=o.s,f=(r=new s(r)).s;if(c!=f)return r.s=-f,o.plus(r);var u=o.c.slice(),h=o.e,l=r.c,a=r.e;if(!u[0]||!l[0])return l[0]?r.s=-f:u[0]?r=new s(o):r.s=1,r;if(c=h-a){for((i=c<0)?(c=-c,n=u):(a=h,n=l),n.reverse(),f=c;f--;)n.push(0);n.reverse()}else for(t=((i=u.length<l.length)?u:l).length,c=f=0;f<t;f++)if(u[f]!=l[f]){i=u[f]<l[f];break}if(i&&(n=u,u=l,l=n,r.s=-r.s),(f=(t=l.length)-(e=u.length))>0)for(;f--;)u[e++]=0;for(f=e;t>c;){if(u[--t]<l[t]){for(e=t;e&&!u[--e];)u[e]=9;--u[e],u[t]+=10}u[t]-=l[t]}for(;0===u[--f];)u.pop();for(;0===u[0];)u.shift(),--a;return u[0]||(r.s=1,u=[a=0]),r.c=u,r.e=a,r},u.mod=function(r){var e,t=this,n=t.constructor,i=t.s,o=(r=new n(r)).s;if(!r.c[0])throw Error(f);return t.s=r.s=1,e=1==r.cmp(t),t.s=i,r.s=o,e?new n(t):(i=n.DP,o=n.RM,n.DP=n.RM=0,t=t.div(r),n.DP=i,n.RM=o,this.minus(t.times(r)))},u.neg=function(){var r=new this.constructor(this);return r.s=-r.s,r},u.plus=u.add=function(r){var e,t,n,i=this,o=i.constructor;if(r=new o(r),i.s!=r.s)return r.s=-r.s,i.minus(r);var s=i.e,c=i.c,f=r.e,u=r.c;if(!c[0]||!u[0])return u[0]||(c[0]?r=new o(i):r.s=i.s),r;if(c=c.slice(),e=s-f){for(e>0?(f=s,n=u):(e=-e,n=c),n.reverse();e--;)n.push(0);n.reverse()}for(c.length-u.length<0&&(n=u,u=c,c=n),e=u.length,t=0;e;c[e]%=10)t=(c[--e]=c[e]+u[e]+t)/10|0;for(t&&(c.unshift(t),++f),e=c.length;0===c[--e];)c.pop();return r.c=c,r.e=f,r},u.pow=function(r){var e=this,t=new e.constructor("1"),i=t,s=r<0;if(r!==~~r||r<-1e6||r>n)throw Error(o+"exponent");for(s&&(r=-r);1&r&&(i=i.times(e)),r>>=1;)e=e.times(e);return s?t.div(i):i},u.prec=function(r,e){if(r!==~~r||r<1||r>t)throw Error(o+"precision");return a(new this.constructor(this),r,e)},u.round=function(r,e){if(r===h)r=0;else if(r!==~~r||r<-t||r>t)throw Error(s);return a(new this.constructor(this),r+this.e+1,e)},u.sqrt=function(){var r,e,t,n=this,o=n.constructor,s=n.s,c=n.e,f=new o("0.5");if(!n.c[0])return new o(n);if(s<0)throw Error(i+"No square root");0===(s=Math.sqrt(+p(n,!0,!0)))||s===1/0?((e=n.c.join("")).length+c&1||(e+="0"),c=((c+1)/2|0)-(c<0||1&c),r=new o(((s=Math.sqrt(e))==1/0?"5e":(s=s.toExponential()).slice(0,s.indexOf("e")+1))+c)):r=new o(s+""),c=r.e+(o.DP+=4);do{t=r,r=f.times(t.plus(n.div(t)))}while(t.c.slice(0,c).join("")!==r.c.slice(0,c).join(""));return a(r,(o.DP-=4)+r.e+1,o.RM)},u.times=u.mul=function(r){var e,t=this,n=t.constructor,i=t.c,o=(r=new n(r)).c,s=i.length,c=o.length,f=t.e,u=r.e;if(r.s=t.s==r.s?1:-1,!i[0]||!o[0])return r.c=[r.e=0],r;for(r.e=f+u,s<c&&(e=i,i=o,o=e,u=s,s=c,c=u),e=new Array(u=s+c);u--;)e[u]=0;for(f=c;f--;){for(c=0,u=s+f;u>f;)c=e[u]+o[f]*i[u-f-1]+c,e[u--]=c%10,c=c/10|0;e[u]=c}for(c?++r.e:e.shift(),f=e.length;!e[--f];)e.pop();return r.c=e,r},u.toExponential=function(r,e){var n=this,i=n.c[0];if(r!==h){if(r!==~~r||r<0||r>t)throw Error(s);for(n=a(new n.constructor(n),++r,e);n.c.length<r;)n.c.push(0)}return p(n,!0,!!i)},u.toFixed=function(r,e){var n=this,i=n.c[0];if(r!==h){if(r!==~~r||r<0||r>t)throw Error(s);for(r=r+(n=a(new n.constructor(n),r+n.e+1,e)).e+1;n.c.length<r;)n.c.push(0)}return p(n,!1,!!i)},u.toJSON=u.toString=function(){var r=this,e=r.constructor;return p(r,r.e<=e.NE||r.e>=e.PE,!!r.c[0])},u.toNumber=function(){var r=+p(this,!0,!0);if(!0===this.constructor.strict&&!this.eq(r.toString()))throw Error(i+"Imprecise conversion");return r},u.toPrecision=function(r,e){var n=this,i=n.constructor,s=n.c[0];if(r!==h){if(r!==~~r||r<1||r>t)throw Error(o+"precision");for(n=a(new i(n),r,e);n.c.length<r;)n.c.push(0)}return p(n,r<=n.e||n.e<=i.NE||n.e>=i.PE,!!s)},u.valueOf=function(){var r=this,e=r.constructor;if(!0===e.strict)throw Error(i+"valueOf disallowed");return p(r,r.e<=e.NE||r.e>=e.PE,!0)},e=function r(){function e(t){var n=this;if(!(n instanceof e))return t===h&&0===arguments.length?r():new e(t);if(t instanceof e)n.s=t.s,n.e=t.e,n.c=t.c.slice();else{if("string"!=typeof t){if(!0===e.strict&&"bigint"!=typeof t)throw TypeError(o+"value");t=0===t&&1/t<0?"-0":String(t)}!function(r,e){var t,n,i;if(!l.test(e))throw Error(o+"number");r.s="-"==e.charAt(0)?(e=e.slice(1),-1):1,(t=e.indexOf("."))>-1&&(e=e.replace(".",""));(n=e.search(/e/i))>0?(t<0&&(t=n),t+=+e.slice(n+1),e=e.substring(0,n)):t<0&&(t=e.length);for(i=e.length,n=0;n<i&&"0"==e.charAt(n);)++n;if(n==i)r.c=[r.e=0];else{for(;i>0&&"0"==e.charAt(--i););for(r.e=t-n-1,r.c=[],t=0;n<=i;)r.c[t++]=+e.charAt(n++)}}(n,t)}n.constructor=e}return e.prototype=u,e.DP=20,e.RM=1,e.NE=-7,e.PE=21,e.strict=false,e.roundDown=0,e.roundHalfUp=1,e.roundHalfEven=2,e.roundUp=3,e}(),e.default=e.Big=e,"function"==typeof define&&define.amd?define((function(){return e})):"undefined"!=typeof module&&module.exports?module.exports=e:r.Big=e}(this);
//# sourceMappingURL=/sm/81da3170e8f7aff6fe695c4e9b3364afbbca5559de7b79a746d16e9a24b325e7.map
