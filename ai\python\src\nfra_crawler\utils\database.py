"""
数据库操作工具类
"""

from psycopg2.extras import RealDictCursor
from psycopg2.pool import ThreadedConnectionPool
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import json
from loguru import logger

from nfra_crawler.config.settings import settings
from nfra_crawler.models.database import (
    BizSyncRecord, 
    BizSyncPunishItem, 
    BizSyncPunishDetail,
)

class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.connection_pool: Optional[ThreadedConnectionPool] = None
        # 延迟初始化，不在导入时连接数据库
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            self.connection_pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=20,
                dsn=settings.database.connection_string
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    def get_connection(self):
        """获取数据库连接"""
        if not self.connection_pool:
            self._initialize_pool()
        return self.connection_pool.getconn()
    
    def return_connection(self, conn):
        """归还数据库连接"""
        if self.connection_pool:
            self.connection_pool.putconn(conn)
    
    def close_pool(self):
        """关闭连接池"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("数据库连接池已关闭")
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询语句"""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                if cursor.description:
                    return [dict(row) for row in cursor.fetchall()]
                return []
        except Exception as e:
            logger.error(f"执行查询失败: {e}, SQL: {query}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    
    def execute_insert(self, query: str, params: tuple = None) -> str:
        """执行插入语句，返回插入的ID"""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                conn.commit()
                # 如果查询包含RETURNING子句，获取返回值
                if cursor.description:
                    result = cursor.fetchone()
                    return result[0] if result else None
                return None
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"执行插入失败: {e}, SQL: {query}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    
    def execute_batch_insert(self, query: str, params_list: List[tuple]) -> int:
        """批量插入"""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cursor:
                cursor.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"批量插入失败: {e}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新语句，返回影响的行数"""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"执行更新失败: {e}, SQL: {query}")
            raise
        finally:
            if conn:
                self.return_connection(conn)

class BizPersonDAO:
    """人员数据访问对象"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def get_persons_by_names(self, name_list: List[str]) -> List[Dict[str, Any]]:
        """根据姓名获取人员信息"""
        if not name_list:
            return []
            
        placeholders = ', '.join(['%s'] * len(name_list))
        query = f"SELECT id, name FROM biz_person WHERE del_flag = '0' AND name IN ({placeholders})"
        results = self.db.execute_query(query, tuple(name_list))
        return results if results else []


class SyncRecordDAO:
    """同步记录数据访问对象"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def create_sync_record(self, record: BizSyncRecord) -> str:
        """创建同步记录"""
        query = """
        INSERT INTO biz_sync_record (
            id, business_type, business_id, request_id, begin_date, end_date, request_time,
            status, remark, create_by, create_time, update_by, update_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        """
        params = (
            record.id, record.business_type, record.business_id, record.request_id, record.begin_date, record.end_date,
            record.request_time, record.status, record.remark, record.create_by,
            record.create_time, record.update_by, record.update_time
        )
        return self.db.execute_insert(query, params)

    def update_sync_record_status(self, record_id: str, status: str,
                                 completed_time: datetime = None,
                                 elapsed: int = None,
                                 remark: str = None) -> int:
        """更新同步记录状态"""
        query = """
        UPDATE biz_sync_record
        SET status = %s, completed_time = %s, elapsed = %s, remark = %s,
            update_time = %s, update_by = %s
        WHERE id = %s
        """
        params = (
            status, completed_time, elapsed, remark,
            datetime.now(), "system", record_id
        )
        return self.db.execute_update(query, params)

    def get_sync_record_by_request_id(self, request_id: str) -> Optional[Dict[str, Any]]:
        """根据请求ID获取同步记录"""
        query = "SELECT * FROM biz_sync_record WHERE request_id = %s"
        results = self.db.execute_query(query, (request_id,))
        return results[0] if results else None


class PunishItemDAO:
    """处罚公示列表数据访问对象"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def create_punish_item(self, item: BizSyncPunishItem) -> str:
        """创建处罚公示项"""
        query = """
        INSERT INTO biz_sync_punish_item (
            id, sync_record_id, request_id, title, reward_punish_org,
            published_date, source_id, source_url, create_by, create_time,
            update_by, update_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        """
        params = (
            item.id, item.sync_record_id, item.request_id, item.title,
            item.reward_punish_org, item.published_date, item.source_id,
            item.source_url, item.create_by, item.create_time,
            item.update_by, item.update_time
        )
        return self.db.execute_insert(query, params)

    def batch_create_punish_items(self, items: List[BizSyncPunishItem]) -> int:
        """批量创建处罚公示项"""
        query = """
        INSERT INTO biz_sync_punish_item (
            id, sync_record_id, request_id, title, reward_punish_org,
            published_date, source_name, source_id, source_url, create_by, create_time,
            update_by, update_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params_list = [
            (
                item.id, item.sync_record_id, item.request_id, item.title,
                item.reward_punish_org, item.published_date, item.source_name, item.source_id,
                item.source_url, item.create_by, item.create_time,
                item.update_by, item.update_time
            ) for item in items
        ]
        return self.db.execute_batch_insert(query, params_list)

    def check_item_exists(self, title: str, source_id: str) -> bool:
        """检查处罚公示项是否已存在（去重）"""
        query = """
        SELECT COUNT(*) FROM biz_sync_punish_item
        WHERE title = %s AND source_id = %s
        """
        result = self.db.execute_query(query, (title, source_id))
        return result[0]['count'] > 0 if result else False


class PunishDetailDAO:
    """处罚公示明细数据访问对象"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def create_punish_detail(self, detail: BizSyncPunishDetail) -> str:
        """创建处罚公示明细"""
        query = """
        INSERT INTO biz_sync_punish_detail (
            id, punish_item_id, request_id, order_num, party_name,
            party_position, punish_doc_no, violation, punish_basis, punish_content, punish_date, 
            decision_authority, party_type, punish_json_content,
            source_id, source_url, bind_person_id, bind_status,
            create_by, create_time, update_by, update_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        """
        params = (
            detail.id, detail.punish_item_id, detail.request_id, detail.order_num,
            detail.party_name, detail.party_position, detail.punish_doc_no,
            detail.violation, detail.punish_basis, detail.punish_content, detail.punish_date, detail.decision_authority,
            detail.party_type, json.dumps(detail.punish_json_content, ensure_ascii=False) if detail.punish_json_content else None,
            detail.source_id, detail.source_url, detail.bind_person_id, detail.bind_status,
            detail.create_by, detail.create_time, detail.update_by, detail.update_time
        )
        return self.db.execute_insert(query, params)

    def batch_create_punish_details(self, details: List[BizSyncPunishDetail]) -> int:
        """批量创建处罚公示明细"""
        query = """
        INSERT INTO biz_sync_punish_detail (
            id, punish_item_id, request_id, order_num, party_name,
            party_position, punish_doc_no, violation, punish_basis, punish_content, punish_date, 
            decision_authority, party_type, punish_json_content,
            source_id, source_url, bind_person_id, bind_status,
            create_by, create_time, update_by, update_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params_list = [
            (
                detail.id, detail.punish_item_id, detail.request_id, detail.order_num,
                detail.party_name, detail.party_position, detail.punish_doc_no,
                detail.violation, detail.punish_basis, detail.punish_content, detail.punish_date, 
                detail.decision_authority,
                detail.party_type, json.dumps(detail.punish_json_content, ensure_ascii=False) if detail.punish_json_content else None,
                detail.source_id, detail.source_url, detail.bind_person_id, detail.bind_status,
                detail.create_by, detail.create_time, detail.update_by, detail.update_time
            ) for detail in details
        ]
        return self.db.execute_batch_insert(query, params_list)


# 数据访问对象工厂
class DAOFactory:
    """数据访问对象工厂"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self._biz_person_dao = None
        self._sync_record_dao = None
        self._punish_item_dao = None
        self._punish_detail_dao = None

    @property
    def biz_person_dao(self) -> BizPersonDAO:
        if not self._biz_person_dao:
            self._biz_person_dao = BizPersonDAO(self.db_manager)
        return self._biz_person_dao

    @property
    def sync_record_dao(self) -> SyncRecordDAO:
        if not self._sync_record_dao:
            self._sync_record_dao = SyncRecordDAO(self.db_manager)
        return self._sync_record_dao

    @property
    def punish_item_dao(self) -> PunishItemDAO:
        if not self._punish_item_dao:
            self._punish_item_dao = PunishItemDAO(self.db_manager)
        return self._punish_item_dao

    @property
    def punish_detail_dao(self) -> PunishDetailDAO:
        if not self._punish_detail_dao:
            self._punish_detail_dao = PunishDetailDAO(self.db_manager)
        return self._punish_detail_dao


# 全局数据库管理器实例
db_manager = DatabaseManager()
dao_factory = DAOFactory(db_manager)
