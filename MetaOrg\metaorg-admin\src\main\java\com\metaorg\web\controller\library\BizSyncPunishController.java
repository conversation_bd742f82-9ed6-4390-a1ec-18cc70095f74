package com.metaorg.web.controller.library;

import java.util.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.library.domain.BizSyncPunishDetail;
import com.metaorg.library.domain.BizSyncPunishItem;
import com.metaorg.library.service.IBizSyncPunishService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.page.TableDataInfo;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import com.metaorg.common.core.text.Convert;
import com.metaorg.library.service.IBizPersonService;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.domain.BizPersonRewardPunish;
import com.metaorg.library.service.IBizPersonRewardPunishService;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.DateUtils;
import org.springframework.web.multipart.MultipartFile;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.exception.ServiceException;
import com.metaorg.library.domain.BizSyncPunishItemTplVM;
import com.metaorg.library.domain.BizSyncPunishDetailTplVM;
import java.lang.reflect.InvocationTargetException;

/**
 * 处罚公示同步Controller
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Controller
@RequestMapping("/library/reward_punish/nfra_punish")
public class BizSyncPunishController extends BaseController
{
    private String prefix = "library/reward_punish/nfra_punish";

    @Autowired
    private IBizSyncPunishService bizSyncPunishService;

    @Autowired
    private IBizPersonService bizPersonService;

    @Autowired
    private IBizPersonRewardPunishService bizPersonRewardPunishService;

    @RequiresPermissions("library:nfra_punish:view")
    @GetMapping()
    public String nfraPunish()
    {
        return prefix + "/nfra_punish";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizSyncPunishDetail bizSyncPunishDetail, Map<String, Object> paramMap) {
        if (paramMap.get("bindStatusList") != null && paramMap.get(("bindStatusList")) != "") {
            bizSyncPunishDetail.getParams().put("bindStatusList",
                    Convert.toStrArray(paramMap.get("bindStatusList").toString()));
        }
    }

    /**
     * 查询处罚公示项列表
     */
    @RequiresPermissions("library:nfra_punish:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BizSyncPunishDetail bizSyncPunishDetail, @RequestParam Map<String, Object> paramMap)
    {
        getQueryOptions(bizSyncPunishDetail, paramMap);
        startPage();
        List<BizSyncPunishDetail> list = bizSyncPunishService.selectBizSyncPunishDetailList(bizSyncPunishDetail);
        return getDataTable(list);
    }

    @RequiresPermissions("library:nfra_punish:edit")
    @GetMapping("/selectPerson/{detailId}")
    public String selectPerson(@PathVariable(value = "detailId") String detailId, ModelMap mmap) {
        BizSyncPunishDetail bizSyncPunishDetail = bizSyncPunishService.selectBizSyncPunishDetailById(detailId);
        if (bizSyncPunishDetail == null) {
            return prefix + "/selectPerson";
        }
        mmap.put("id", bizSyncPunishDetail.getId());
        mmap.put("personName", bizSyncPunishDetail.getPartyName());
        return prefix + "/select_person";
    }

    /**
     * 关联人员
     */
    @RequiresPermissions("library:nfra_punish:edit")
    @PostMapping("/getPersonList")
    @ResponseBody
    @Log(title = "关联人员", businessType = BusinessType.OTHER)
    public TableDataInfo getPersonList(BizSyncPunishDetail bizSyncPunishDetail)
    {
        BizSyncPunishDetail detail = bizSyncPunishService.selectBizSyncPunishDetailById(bizSyncPunishDetail.getId());
        if (detail == null) {
            return getDataTable(new ArrayList<>());
        }
        startPage();
        String personName = detail.getPartyName();
        BizPerson bizPerson = new BizPerson();
        bizPerson.setDelFlag("0");
        bizPerson.setParams(new HashMap<>());
        bizPerson.getParams().put("nameList", Arrays.asList(personName));
        List<BizPerson> persons = bizPersonService.selectBizPersonList(bizPerson);

        return getDataTable(persons);
    }

    /**
     * 关联人员
     */
    @RequiresPermissions("library:nfra_punish:edit")
    @PostMapping("/bindPerson")
    @ResponseBody
    @Log(title = "关联人员", businessType = BusinessType.OTHER)
    public AjaxResult bindPerson(BizSyncPunishDetail bizSyncPunishDetail)
    {
        try {
            BizSyncPunishDetail punishDetailInfo = bizSyncPunishService.selectBizSyncPunishDetailById(bizSyncPunishDetail.getId());
            if (punishDetailInfo == null) {
                return AjaxResult.error("关联人员失败：找不到处罚公示项！");
            }

            // 生成该人员的处罚信息到biz_person_award_punish表
            String personId = bizSyncPunishDetail.getBindPersonId();
            String personPosition = bizSyncPunishDetail.getPartyPosition();
            String punishContent = punishDetailInfo.getPunishContent();
            String punishReason = punishDetailInfo.getViolation();
            String punishDocNo = punishDetailInfo.getPunishDocNo();
            String decisionAuthority = punishDetailInfo.getDecisionAuthority();

            BizPersonRewardPunish bizPersonRewardPunish = new BizPersonRewardPunish();
            bizPersonRewardPunish.setPersonId(personId);
            bizPersonRewardPunish.setRewardPunishPosition(personPosition);
            bizPersonRewardPunish.setSourceType("国家金融监督管理总局");
            bizPersonRewardPunish.setSourceId(bizSyncPunishDetail.getId());

            bizPersonRewardPunish.setShowInAppoint("Y");
            bizPersonRewardPunish.setRewardPunishDocNo(punishDocNo);
            bizPersonRewardPunish.setRewardPunishCategory("惩处");
            bizPersonRewardPunish.setRewardPunishReason(punishReason);
            bizPersonRewardPunish.setRewardPunishName(punishContent);
            bizPersonRewardPunish.setRewardPunishDay(punishDetailInfo.getPunishDate());
            bizPersonRewardPunish.setRewardPunishOrg(decisionAuthority);

            bizPersonRewardPunish.setRewardPunishDetail(bizPersonRewardPunish.rewardPunishToString());
            bizPersonRewardPunish.setOrderNum(bizPersonRewardPunishService.getMaxOrderNum(personId));
            
            Date currentDate = DateUtils.getNowDate();
            bizPersonRewardPunish.setCreateTime(currentDate);
            bizPersonRewardPunish.setUpdateTime(currentDate);

            bizPersonRewardPunish.setCreateById(getUserId().toString());
            bizPersonRewardPunish.setCreateBy(getLoginName());
            bizPersonRewardPunish.setUpdateById(getUserId().toString());
            bizPersonRewardPunish.setUpdateBy(getLoginName());

            String rewardPunishId = bizPersonRewardPunishService.insertBizPersonRewardPunishReturnID(bizPersonRewardPunish);
            if (StringUtils.isEmpty(rewardPunishId)) {
                return AjaxResult.error("关联人员失败：生成奖惩情况失败！");
            }

            // 关联人员
            bizSyncPunishDetail.setBindStatus("confirmed");
            bizSyncPunishDetail.setBindPersonId(personId);
            bizSyncPunishDetail.setBindPunishId(rewardPunishId);

            bizSyncPunishService.updateBizSyncPunishDetailStatus(bizSyncPunishDetail);
            
            return AjaxResult.success("关联人员成功！");
        } catch (Exception e) {
            logger.error("关联人员失败", e);
            return AjaxResult.error("关联人员失败：" + e.getMessage());
        }
    }

    /**
     * 取消关联人员
     */
    @RequiresPermissions("library:nfra_punish:edit")
    @PostMapping("/cancelBindPerson")
    @ResponseBody
    @Log(title = "取消关联人员", businessType = BusinessType.OTHER)
    public AjaxResult cancelBindPerson(BizSyncPunishDetail bizSyncPunishDetail)
    {
        try {

            BizSyncPunishDetail punishDetailInfo = bizSyncPunishService.selectBizSyncPunishDetailById(bizSyncPunishDetail.getId());
            if (punishDetailInfo == null) {
                return AjaxResult.error("关联人员失败：找不到处罚公示项！");
            }

            // 删除奖惩情况
            String bindPunishId = punishDetailInfo.getBindPunishId();
            if (StringUtils.isNotEmpty(bindPunishId)) {
                bizPersonRewardPunishService.deleteBizPersonRewardPunishById(bindPunishId);
            }

            // 关联人员
            bizSyncPunishDetail.setBindStatus("pending");
            bizSyncPunishDetail.setBindPunishId("");
            bizSyncPunishService.updateBizSyncPunishDetailStatus(bizSyncPunishDetail);
            
            return AjaxResult.success("取消关联成功！");
        } catch (Exception e) {
            logger.error("取消关联失败", e);
            return AjaxResult.error("取消关联失败：" + e.getMessage());
        }
    }

    /**
     * 同步处罚公示
     */
    @RequiresPermissions("library:nfra_punish:sync")
    @PostMapping("/manualSync")
    @ResponseBody
    @Log(title = "处罚公示数据", businessType = BusinessType.OTHER)
    public AjaxResult manualSync()
    {
        try {
            // 同步处罚详情
            bizSyncPunishService.syncPunishDetailsRequest(null);
            
            return AjaxResult.success("同步请求提交成功，系统将在后台进行数据同步！");
        } catch (Exception e) {
            logger.error("同步处罚公示数据失败", e);
            return AjaxResult.error("同步请求提交失败：" + e.getMessage());
        }
    }

    /**
     * 导入处罚公示数据
     */
    @Log(title = "导入处罚公示信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions("library:nfra_punish:edit")
    @PostMapping("/import")
    @ResponseBody
    public AjaxResult importPerson(MultipartFile file) throws Exception
    {
        ExcelUtil<BizSyncPunishItemTplVM> punishItemUtil = new ExcelUtil<>(BizSyncPunishItemTplVM.class);
        List<BizSyncPunishItemTplVM> punishItemList = punishItemUtil.importExcel("处罚公示列表", file.getInputStream(), 0);

        ExcelUtil<BizSyncPunishDetailTplVM> punishDetailUtil = new ExcelUtil<>(BizSyncPunishDetailTplVM.class);
        List<BizSyncPunishDetailTplVM> punishDetailList = punishDetailUtil.importExcel("处罚公示明细", file.getInputStream(), 0);
       
        String message = importBizSyncPunishItemData(punishItemList, punishDetailList);
        return AjaxResult.success(message);
    }

    private String importBizSyncPunishItemData(List<BizSyncPunishItemTplVM> punishItems, List<BizSyncPunishDetailTplVM> punishDetails){
        if (StringUtils.isNull(punishItems) || punishItems.size() == 0)
        {
            throw new ServiceException("处罚公示列表数据不能为空！");
        }

        if (StringUtils.isNull(punishDetails) || punishDetails.size() == 0)
        {
            throw new ServiceException("处罚公示明细数据不能为空！");
        }

        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 导入数据检查
        Boolean punishItemIsValid = checkBizSyncPunishItemData(punishItems);
        Boolean punishDetailIsValid = checkBizSyncPunishDetailData(punishDetails);

        if (!punishItemIsValid) {
            for (BizSyncPunishItemTplVM pvm : punishItems) {
                if (StringUtils.isNotEmpty(pvm.getErrMsg())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
            } 
            if (failureNum > 0)
            {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            }
        }
        if (!punishDetailIsValid) {
            for (BizSyncPunishDetailTplVM pvm : punishDetails) {
                if (StringUtils.isNotEmpty(pvm.getErrMsg())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
            } 
            if (failureNum > 0)
            {
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            }
        }

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        HashSet<String> sourceIdSet = new HashSet<>();
        List<BizSyncPunishItem> bizSyncPunishItemList = new ArrayList<>();
        List<BizSyncPunishDetail> bizSyncPunishDetailList = new ArrayList<>();

        for (BizSyncPunishItemTplVM pvm : punishItems) {
            if (StringUtils.isNotEmpty(pvm.getSourceId())) {
                sourceIdSet.add(pvm.getSourceId());
            }

            BizSyncPunishItem person = BizSyncPunishItem.fromBizSyncPunishItemTplVM(pvm);

            person.setCreateById(createUserId);
            person.setCreateBy(createUserName);
            person.setCreateTime(currentDate);
            person.setUpdateById(createUserId);
            person.setUpdateBy(createUserName);
            person.setUpdateTime(currentDate);

            bizSyncPunishItemList.add(person);
        }

        for (BizSyncPunishDetailTplVM pvm : punishDetails) {
            if (StringUtils.isNotEmpty(pvm.getSourceId())) {
                sourceIdSet.add(pvm.getSourceId());
            }

            BizSyncPunishDetail person = BizSyncPunishDetail.fromBizSyncPunishDetailTplVM(pvm);

            person.setCreateById(createUserId);
            person.setCreateBy(createUserName);
            person.setCreateTime(currentDate);
            person.setUpdateById(createUserId);
            person.setUpdateBy(createUserName);
            person.setUpdateTime(currentDate);

            bizSyncPunishDetailList.add(person);
        }

        try {
            String[] sourceIds = sourceIdSet.toArray(new String[0]);

            // 删除处罚公示项
            BizSyncPunishItem deleteBizSyncPunishItem = new BizSyncPunishItem();
            deleteBizSyncPunishItem.getParams().put("sourceIds", sourceIds);
            bizSyncPunishService.deleteBizSyncPunishItem(deleteBizSyncPunishItem);

            // 新增处罚公示项
            bizSyncPunishService.batchInsertBizSyncPunishItem(bizSyncPunishItemList);

            // 删除处罚公示明细
            BizSyncPunishDetail deleteBizSyncPunishDetail = new BizSyncPunishDetail();
            deleteBizSyncPunishDetail.getParams().put("sourceIds", sourceIds);
            bizSyncPunishService.deleteBizSyncPunishDetail(deleteBizSyncPunishDetail);

            // 新增处罚公示明细
            bizSyncPunishService.batchInsertBizSyncPunishDetail(bizSyncPunishDetailList);
        }
        catch (Exception e) {
            failureNum = 1;
            String exMsg = e.getMessage();
            failureMsg.append("数据入库失败：" + exMsg);
        }

        if (failureNum > 0)
        {
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！");
        }

        return successMsg.toString();
    }

    private Boolean checkBizSyncPunishItemData(List<BizSyncPunishItemTplVM> checkList) {
        Boolean isOk = true;

        int rowIndex = 0;
        for (BizSyncPunishItemTplVM checkTplVM : checkList) {
            rowIndex++;
            String preMsg = "第" + rowIndex + "行数据：标题：" + checkTplVM.getTitle();

            try {
                // 判断是否为空
                ArrayList<String> emptyMsgList = new ArrayList<>();

                if (StringUtils.isEmpty(checkTplVM.getId()))
                    emptyMsgList.add("处罚公示标识");

                if (StringUtils.isEmpty(checkTplVM.getSyncRecordId()))
                    emptyMsgList.add("同步记录标识");

                if (StringUtils.isEmpty(checkTplVM.getRequestId()))
                    emptyMsgList.add("同步请求标识");

                if (StringUtils.isEmpty(checkTplVM.getTitle()))
                    emptyMsgList.add("标题");

                if (StringUtils.isEmpty(checkTplVM.getRewardPunishOrg()))
                    emptyMsgList.add("批准机关");

                if (StringUtils.isEmpty(checkTplVM.getPublishedDate()))
                    emptyMsgList.add("发布日期");

                if (StringUtils.isEmpty(checkTplVM.getSourceId()))
                    emptyMsgList.add("来源标识");

                if (StringUtils.isEmpty(checkTplVM.getSourceUrl()))
                    emptyMsgList.add("来源网址");

                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }

                // 发布日期
                if (StringUtils.isNotEmpty(checkTplVM.getPublishedDate())) {
                    Date publishedDate = DateUtils.parseDate(checkTplVM.getPublishedDate());
                    if (publishedDate == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "发布日期不正确，正确示例: 2025-01-01");
                        continue;
                    }
                }
            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if (StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException) {
                    exMsg = ((InvocationTargetException) e).getTargetException().getMessage();
                }
                if (StringUtils.isEmpty(exMsg)) {
                    exMsg = "数据校验失败";
                }
                checkTplVM.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }

    private Boolean checkBizSyncPunishDetailData(List<BizSyncPunishDetailTplVM> checkList) {
        Boolean isOk = true;

        int rowIndex = 0;
        for (BizSyncPunishDetailTplVM checkTplVM : checkList) {
            rowIndex++;
            String preMsg = "第" + rowIndex + "行数据：当事人名称：" + checkTplVM.getPartyName();

            try {
                // 判断是否为空
                ArrayList<String> emptyMsgList = new ArrayList<>();

                if (StringUtils.isEmpty(checkTplVM.getPunishItemId()))
                    emptyMsgList.add("处罚公示标识");

                if (StringUtils.isEmpty(checkTplVM.getRequestId()))
                    emptyMsgList.add("同步请求标识");

                if (StringUtils.isEmpty(checkTplVM.getPartyName()))
                    emptyMsgList.add("当事人名称");

                if (StringUtils.isEmpty(checkTplVM.getSourceId()))
                    emptyMsgList.add("来源标识");

                if (StringUtils.isEmpty(checkTplVM.getSourceUrl()))
                    emptyMsgList.add("来源网址");

                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    checkTplVM.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }

                // 作出处罚决定的日期
                if (StringUtils.isNotEmpty(checkTplVM.getPunishDate())) {
                    Date punishDate = DateUtils.parseDate(checkTplVM.getPunishDate());
                    if (punishDate == null) {
                        isOk = false;
                        checkTplVM.setErrMsg(preMsg + "作出处罚决定的日期不正确，正确示例: 2025-01-01");
                        continue;
                    }
                }
            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if (StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException) {
                    exMsg = ((InvocationTargetException) e).getTargetException().getMessage();
                }
                if (StringUtils.isEmpty(exMsg)) {
                    exMsg = "数据校验失败";
                }
                checkTplVM.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }
}