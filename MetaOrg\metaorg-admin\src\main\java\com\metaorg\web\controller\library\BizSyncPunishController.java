package com.metaorg.web.controller.library;

import java.util.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.library.domain.BizSyncPunishDetail;
import com.metaorg.library.service.IBizSyncPunishService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.page.TableDataInfo;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import com.metaorg.common.core.text.Convert;
import com.metaorg.library.service.IBizPersonService;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.domain.BizPersonRewardPunish;
import com.metaorg.library.service.IBizPersonRewardPunishService;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.DateUtils;

/**
 * 处罚公示同步Controller
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Controller
@RequestMapping("/library/reward_punish/nfra_punish")
public class BizSyncPunishController extends BaseController
{
    private String prefix = "library/reward_punish/nfra_punish";

    @Autowired
    private IBizSyncPunishService bizSyncPunishService;

    @Autowired
    private IBizPersonService bizPersonService;

    @Autowired
    private IBizPersonRewardPunishService bizPersonRewardPunishService;

    @RequiresPermissions("library:nfra_punish:view")
    @GetMapping()
    public String nfraPunish()
    {
        return prefix + "/nfra_punish";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizSyncPunishDetail bizSyncPunishDetail, Map<String, Object> paramMap) {
        if (paramMap.get("bindStatusList") != null && paramMap.get(("bindStatusList")) != "") {
            bizSyncPunishDetail.getParams().put("bindStatusList",
                    Convert.toStrArray(paramMap.get("bindStatusList").toString()));
        }
    }

    /**
     * 查询处罚公示项列表
     */
    @RequiresPermissions("library:nfra_punish:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BizSyncPunishDetail bizSyncPunishDetail, @RequestParam Map<String, Object> paramMap)
    {
        getQueryOptions(bizSyncPunishDetail, paramMap);
        startPage();
        List<BizSyncPunishDetail> list = bizSyncPunishService.selectBizSyncPunishDetailList(bizSyncPunishDetail);
        return getDataTable(list);
    }

    @RequiresPermissions("library:nfra_punish:edit")
    @GetMapping("/selectPerson/{detailId}")
    public String selectPerson(@PathVariable(value = "detailId") String detailId, ModelMap mmap) {
        BizSyncPunishDetail bizSyncPunishDetail = bizSyncPunishService.selectBizSyncPunishDetailById(detailId);
        if (bizSyncPunishDetail == null) {
            return prefix + "/selectPerson";
        }
        mmap.put("id", bizSyncPunishDetail.getId());
        mmap.put("personName", bizSyncPunishDetail.getPartyName());
        return prefix + "/select_person";
    }

    /**
     * 关联人员
     */
    @RequiresPermissions("library:nfra_punish:edit")
    @PostMapping("/getPersonList")
    @ResponseBody
    @Log(title = "关联人员", businessType = BusinessType.OTHER)
    public TableDataInfo getPersonList(BizSyncPunishDetail bizSyncPunishDetail)
    {
        BizSyncPunishDetail detail = bizSyncPunishService.selectBizSyncPunishDetailById(bizSyncPunishDetail.getId());
        if (detail == null) {
            return getDataTable(new ArrayList<>());
        }
        startPage();
        String personName = detail.getPartyName();
        BizPerson bizPerson = new BizPerson();
        bizPerson.setDelFlag("0");
        bizPerson.setParams(new HashMap<>());
        bizPerson.getParams().put("nameList", Arrays.asList(personName));
        List<BizPerson> persons = bizPersonService.selectBizPersonList(bizPerson);

        return getDataTable(persons);
    }

    /**
     * 关联人员
     */
    @RequiresPermissions("library:nfra_punish:edit")
    @PostMapping("/bindPerson")
    @ResponseBody
    @Log(title = "关联人员", businessType = BusinessType.OTHER)
    public AjaxResult bindPerson(BizSyncPunishDetail bizSyncPunishDetail)
    {
        try {
            BizSyncPunishDetail punishDetailInfo = bizSyncPunishService.selectBizSyncPunishDetailById(bizSyncPunishDetail.getId());
            if (punishDetailInfo == null) {
                return AjaxResult.error("关联人员失败：找不到处罚公示项！");
            }

            // 生成该人员的处罚信息到biz_person_award_punish表
            String personId = bizSyncPunishDetail.getBindPersonId();
            String personPosition = bizSyncPunishDetail.getPartyPosition();
            String punishContent = punishDetailInfo.getPunishContent();
            String punishReason = punishDetailInfo.getViolation();
            String punishDocNo = punishDetailInfo.getPunishDocNo();
            String decisionAuthority = punishDetailInfo.getDecisionAuthority();

            BizPersonRewardPunish bizPersonRewardPunish = new BizPersonRewardPunish();
            bizPersonRewardPunish.setPersonId(personId);
            bizPersonRewardPunish.setRewardPunishPosition(personPosition);
            bizPersonRewardPunish.setSourceType("国家金融监督管理总局");
            bizPersonRewardPunish.setSourceId(bizSyncPunishDetail.getId());

            bizPersonRewardPunish.setShowInAppoint("Y");
            bizPersonRewardPunish.setRewardPunishDocNo(punishDocNo);
            bizPersonRewardPunish.setRewardPunishCategory("惩处");
            bizPersonRewardPunish.setRewardPunishReason(punishReason);
            bizPersonRewardPunish.setRewardPunishName(punishContent);
            bizPersonRewardPunish.setRewardPunishDay(punishDetailInfo.getPunishDate());
            bizPersonRewardPunish.setRewardPunishOrg(decisionAuthority);

            bizPersonRewardPunish.setRewardPunishDetail(bizPersonRewardPunish.rewardPunishToString());
            bizPersonRewardPunish.setOrderNum(bizPersonRewardPunishService.getMaxOrderNum(personId));
            
            Date currentDate = DateUtils.getNowDate();
            bizPersonRewardPunish.setCreateTime(currentDate);
            bizPersonRewardPunish.setUpdateTime(currentDate);

            bizPersonRewardPunish.setCreateById(getUserId().toString());
            bizPersonRewardPunish.setCreateBy(getLoginName());
            bizPersonRewardPunish.setUpdateById(getUserId().toString());
            bizPersonRewardPunish.setUpdateBy(getLoginName());

            String rewardPunishId = bizPersonRewardPunishService.insertBizPersonRewardPunishReturnID(bizPersonRewardPunish);
            if (StringUtils.isEmpty(rewardPunishId)) {
                return AjaxResult.error("关联人员失败：生成奖惩情况失败！");
            }

            // 关联人员
            bizSyncPunishDetail.setBindStatus("confirmed");
            bizSyncPunishDetail.setBindPersonId(personId);
            bizSyncPunishDetail.setBindPunishId(rewardPunishId);

            bizSyncPunishService.updateBizSyncPunishDetailStatus(bizSyncPunishDetail);
            
            return AjaxResult.success("关联人员成功！");
        } catch (Exception e) {
            logger.error("关联人员失败", e);
            return AjaxResult.error("关联人员失败：" + e.getMessage());
        }
    }

    /**
     * 取消关联人员
     */
    @RequiresPermissions("library:nfra_punish:edit")
    @PostMapping("/cancelBindPerson")
    @ResponseBody
    @Log(title = "取消关联人员", businessType = BusinessType.OTHER)
    public AjaxResult cancelBindPerson(BizSyncPunishDetail bizSyncPunishDetail)
    {
        try {

            BizSyncPunishDetail punishDetailInfo = bizSyncPunishService.selectBizSyncPunishDetailById(bizSyncPunishDetail.getId());
            if (punishDetailInfo == null) {
                return AjaxResult.error("关联人员失败：找不到处罚公示项！");
            }

            // 删除奖惩情况
            String bindPunishId = punishDetailInfo.getBindPunishId();
            if (StringUtils.isNotEmpty(bindPunishId)) {
                bizPersonRewardPunishService.deleteBizPersonRewardPunishById(bindPunishId);
            }

            // 关联人员
            bizSyncPunishDetail.setBindStatus("pending");
            bizSyncPunishDetail.setBindPunishId("");
            bizSyncPunishService.updateBizSyncPunishDetailStatus(bizSyncPunishDetail);
            
            return AjaxResult.success("取消关联成功！");
        } catch (Exception e) {
            logger.error("取消关联失败", e);
            return AjaxResult.error("取消关联失败：" + e.getMessage());
        }
    }

    /**
     * 同步处罚公示
     */
    @RequiresPermissions("library:nfra_punish:sync")
    @PostMapping("/manualSync")
    @ResponseBody
    @Log(title = "处罚公示数据", businessType = BusinessType.OTHER)
    public AjaxResult manualSync()
    {
        try {
            // 同步处罚详情
            bizSyncPunishService.syncPunishDetailsRequest(null);
            
            return AjaxResult.success("同步请求提交成功，系统将在后台进行数据同步！");
        } catch (Exception e) {
            logger.error("同步处罚公示数据失败", e);
            return AjaxResult.error("同步请求提交失败：" + e.getMessage());
        }
    }
} 