package com.metaorg.web.controller.system;

import java.util.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

import com.metaorg.common.core.domain.BaseEntity;
import com.metaorg.common.core.domain.entity.SysRole;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.framework.aspectj.DataScopeAspect;
import com.metaorg.library.domain.BizElement;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.service.IBizMessageNotifyRecordService;
import com.metaorg.library.service.IBizPersonReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.constant.ShiroConstants;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.entity.SysMenu;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.CookieUtils;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.utils.ServletUtils;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.framework.shiro.service.SysPasswordService;
import com.metaorg.system.service.ISysConfigService;
import com.metaorg.system.service.ISysMenuService;
import com.metaorg.library.domain.BizMessageNotifyRecord;

/**
 * 首页 业务处理
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
public class SysIndexController extends BaseController
{
    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private IBizPersonReportService bizPersonReportService;

    @Autowired
    private IBizMessageNotifyRecordService bizMessageNotifyRecordService;

    // 系统首页
    @GetMapping("/index")
    public String index(ModelMap mmap)
    {
        // 取身份信息
        SysUser user = getSysUser();
        // 根据用户id取出菜单
        List<SysMenu> menus = menuService.selectMenusByUser(user);
        mmap.put("menus", menus);
        mmap.put("user", user);
        mmap.put("sideTheme", configService.selectConfigByKey("sys.index.sideTheme"));
        mmap.put("skinName", configService.selectConfigByKey("sys.index.skinName"));
        Boolean footer = Convert.toBool(configService.selectConfigByKey("sys.index.footer"), true);
        Boolean tagsView = Convert.toBool(configService.selectConfigByKey("sys.index.tagsView"), true);
        mmap.put("footer", footer);
        mmap.put("tagsView", tagsView);
        mmap.put("mainClass", contentMainClass(footer, tagsView));
        mmap.put("copyrightYear", RuoYiConfig.getCopyrightYear());
        mmap.put("demoEnabled", RuoYiConfig.isDemoEnabled());
        if(user.getLoginType().equals("local")){
            mmap.put("isDefaultModifyPwd", initPasswordIsModify(user.getPwdUpdateDate()));
            mmap.put("isPasswordExpired", passwordIsExpiration(user.getPwdUpdateDate()));
        }
        mmap.put("isMobile", ServletUtils.checkAgentIsMobile(ServletUtils.getRequest().getHeader("User-Agent")));

        // 查询未读消息通知数量
        BizMessageNotifyRecord bizMessageNotifyRecord = new BizMessageNotifyRecord();
        bizMessageNotifyRecord.setIsRead(false);
        bizMessageNotifyRecord.setReceiverId(user.getUserId());
        List<BizMessageNotifyRecord> bizMessageNotifyRecordList = bizMessageNotifyRecordService.selectBizMessageNotifyRecordList(bizMessageNotifyRecord);

        mmap.put("unreadMessageNotifyCount", bizMessageNotifyRecordList.size());

        // 获取前5条未读的通知
        List<BizMessageNotifyRecord> unreadMessageNotifyList = bizMessageNotifyRecordList.stream()
                .limit(5)
                .collect(Collectors.toList());
        mmap.put("unreadMessageNotifyList", unreadMessageNotifyList);
        
        // 菜单导航显示风格
        String menuStyle = configService.selectConfigByKey("sys.index.menuStyle");
        // 移动端，默认使左侧导航菜单，否则取默认配置
        String indexStyle = ServletUtils.checkAgentIsMobile(ServletUtils.getRequest().getHeader("User-Agent")) ? "index" : menuStyle;

        // 优先Cookie配置导航菜单
        Cookie[] cookies = ServletUtils.getRequest().getCookies();
        for (Cookie cookie : cookies)
        {
            if (StringUtils.isNotEmpty(cookie.getName()) && "nav-style".equalsIgnoreCase(cookie.getName()))
            {
                indexStyle = cookie.getValue();
                break;
            }
        }
        String webIndex = "topnav".equalsIgnoreCase(indexStyle) ? "index-topnav" : "index";
        return webIndex;
    }

    // 锁定屏幕
    @GetMapping("/lockscreen")
    public String lockscreen(ModelMap mmap)
    {
        mmap.put("user", getSysUser());
        ServletUtils.getSession().setAttribute(ShiroConstants.LOCK_SCREEN, true);
        return "lock";
    }

    // 解锁屏幕
    @PostMapping("/unlockscreen")
    @ResponseBody
    public AjaxResult unlockscreen(String password)
    {
        SysUser user = getSysUser();
        if (StringUtils.isNull(user))
        {
            return AjaxResult.error("服务器超时，请重新登录");
        }
        if (passwordService.matches(user, password))
        {
            ServletUtils.getSession().removeAttribute(ShiroConstants.LOCK_SCREEN);
            return AjaxResult.success();
        }
        return AjaxResult.error("密码不正确，请重新输入。");
    }

    // 切换主题
    @GetMapping("/system/switchSkin")
    public String switchSkin()
    {
        return "skin";
    }

    // 切换菜单
    @GetMapping("/system/menuStyle/{style}")
    public void menuStyle(@PathVariable String style, HttpServletResponse response)
    {
        CookieUtils.setCookie(response, "nav-style", style);
    }

    // 系统介绍
    @GetMapping("/system/main")
    public String main(@RequestParam Map<String, Object> paramMap, BizPerson bizPerson,ModelMap mmap)
    {
        mmap.put("version", RuoYiConfig.getVersion());
        return "main_v1";
    }

    // 查询年龄比例
    @GetMapping("/system/age_list")
    @ResponseBody
    public TableDataInfo age_list(@RequestParam(value = "deptId", defaultValue = "") String deptId,
    @RequestParam(value = "ids", defaultValue = "") String ids,BizPerson bizPerson, ModelMap mmap)
    {
        String params = "";
        if (!getSysUser().isAdmin()) {
            dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", getSysUser().getDeptId());
            params = bizPerson.getParams().get("dataScope")!=null ? bizPerson.getParams().get("dataScope").toString() : "";
        }

        List<BizElement> ageGroup;

        if (!deptId.equals("")) {
            Integer searchDeptId = Convert.toInt(deptId, 0);
            params += " and (dept_id = " + searchDeptId + " or dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET ("
                    + searchDeptId +",ancestors) ) )";
        }

        if(!ids.equals("") && ids!=null){
            ageGroup = bizPersonReportService.selectAgeGroupByIdsNew(ids);
        }else{
            ageGroup = bizPersonReportService.selectAgeGroupNew(params);
        }

        return getDataTable(ageGroup);
    }

    @GetMapping("/system/education_list")
    @ResponseBody
    public TableDataInfo education_list(@RequestParam(value = "deptId", defaultValue = "") String deptId,
    @RequestParam(value = "ids", defaultValue = "") String ids, @RequestParam(value = "type", defaultValue = "") String type,BizPerson bizPerson, ModelMap mmap)
    {
        String params = "";
        if (!getSysUser().isAdmin()) {
            dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", getSysUser().getDeptId());
            params = bizPerson.getParams().get("dataScope")!=null ? bizPerson.getParams().get("dataScope").toString() : "";
        }

        List<BizElement> eduGroup;

        if(!ids.equals("") && ids != null){
            eduGroup = bizPersonReportService.selectEduGroupByIdsNew(ids);
        }else{
            if (!deptId.equals("")) {
                Integer searchDeptId = Convert.toInt(deptId, 0);
                params += " and (dept_id = " + searchDeptId + " or dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET ("
                        + searchDeptId +",ancestors) ) )";
            }
            eduGroup = bizPersonReportService.selectEduGroupNew(params);
        }

        // 创建一个存储元素的List
        List<BizElement> array = new ArrayList<>();
        array.addAll(eduGroup);
        if(!type.equals("") && type != null){
            if(type.equals("full_time")){
                 array = eduGroup.stream()
                        .filter(element -> element.getName().contains("全日制教育"))
                        .collect(Collectors.toList());
            }else if(type.equals("in_service")){
                 array = eduGroup.stream()
                        .filter(element -> element.getName().contains("在职教育"))
                        .collect(Collectors.toList());
            }
        }
        return getDataTable(array);
    }

    // 查询年龄比例
    @GetMapping("/system/sex_list")
    @ResponseBody
    public TableDataInfo sex_list(@RequestParam(value = "deptId", defaultValue = "") String deptId,
    @RequestParam(value = "ids", defaultValue = "") String ids, BizPerson bizPerson, ModelMap mmap)
    {
        String params = "";
        if (!getSysUser().isAdmin()) {
            dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", getSysUser().getDeptId());
            params = bizPerson.getParams().get("dataScope")!=null ? bizPerson.getParams().get("dataScope").toString() : "";
        }

        List<BizElement> sexGroup;

        if (!deptId.equals("")) {
            Integer searchDeptId = Convert.toInt(deptId, 0);
            params += " and (dept_id = " + searchDeptId + " or dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET ("
                    + searchDeptId +",ancestors) ) )";
        }

        if(!ids.equals("") && ids!=null){
            sexGroup = bizPersonReportService.selectSexGroupByIds(ids);
        }else{
            sexGroup = bizPersonReportService.selectSexGroup(params);
        }

        return getDataTable(sexGroup);
    }

    // 查询民族比例
    @GetMapping("/system/nation_list")
    @ResponseBody
    public TableDataInfo nation_list(@RequestParam(value = "deptId", required = false) Integer deptId,
    @RequestParam(value = "ids", defaultValue = "") String ids, BizPerson bizPerson, ModelMap mmap)
    {
        String params = "";
        if (!getSysUser().isAdmin()) {
            dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", getSysUser().getDeptId());
            params = bizPerson.getParams().get("dataScope")!=null ? bizPerson.getParams().get("dataScope").toString() : "";
        }

        List<BizElement> nationGroup;

        if (deptId != null && deptId > 0) {
            params += " and (dept_id = " + deptId + " or dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET ("
                    + deptId + ",ancestors) ) )";
        }

        if (!ids.equals("") && ids != null){
            nationGroup = bizPersonReportService.selectNationGroupByIds(ids);
        } else {
            nationGroup = bizPersonReportService.selectNationGroup(params);
        }
        return getDataTable(nationGroup);
    }

    public static void dataScopeFilter(BaseEntity baseEntity, SysUser user, String deptAlias, String userAlias, String permission, long deptId)
    {
        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<String>();

        for (SysRole role : user.getRoles())
        {
            String dataScope = role.getDataScope();
            if (!DataScopeAspect.DATA_SCOPE_CUSTOM.equals(dataScope) && conditions.contains(dataScope))
            {
                continue;
            }
            if (StringUtils.isNotEmpty(permission) && StringUtils.isNotEmpty(role.getPermissions())
                    && !StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission)))
            {
                continue;
            }
            if (DataScopeAspect.DATA_SCOPE_ALL.equals(dataScope))
            {
                sqlString = new StringBuilder();
                conditions.add(dataScope);
                break;
            }
            else if (DataScopeAspect.DATA_SCOPE_CUSTOM.equals(dataScope))
            {
                sqlString.append(StringUtils.format(
                        " OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ", deptAlias,
                        role.getRoleId()));
            }
            else if (DataScopeAspect.DATA_SCOPE_DEPT.equals(dataScope))
            {
                sqlString.append(StringUtils.format(" OR {}.dept_id = {} ", deptAlias, deptId));
            }
            else if (DataScopeAspect.DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope))
            {
                sqlString.append(StringUtils.format(
                        " OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )",
                        deptAlias, deptId, deptId));
            }
            else if (DataScopeAspect.DATA_SCOPE_SELF.equals(dataScope))
            {
                if (StringUtils.isNotBlank(userAlias))
                {
                    sqlString.append(StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId()));
                }
                else
                {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
                }
            }
            conditions.add(dataScope);
        }

        // 多角色情况下，所有角色都不包含传递过来的权限字符，这个时候sqlString也会为空，所以要限制一下,不查询任何数据
        if (StringUtils.isEmpty(conditions))
        {
            sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
        }

        if (StringUtils.isNotBlank(sqlString.toString()))
        {
            baseEntity.getParams().put(DataScopeAspect.DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
        }
    }

    // content-main class
    public String contentMainClass(Boolean footer, Boolean tagsView)
    {
        if (!footer && !tagsView)
        {
            return "tagsview-footer-hide";
        }
        else if (!footer)
        {
            return "footer-hide";
        }
        else if (!tagsView)
        {
            return "tagsview-hide";
        }
        return StringUtils.EMPTY;
    }

    // 检查初始密码是否提醒修改
    public boolean initPasswordIsModify(Date pwdUpdateDate)
    {
        Integer initPasswordModify = Convert.toInt(configService.selectConfigByKey("sys.account.initPasswordModify"));
        return initPasswordModify != null && initPasswordModify == 1 && pwdUpdateDate == null;
    }

    // 检查密码是否过期
    public boolean passwordIsExpiration(Date pwdUpdateDate)
    {
        Integer passwordValidateDays = Convert.toInt(configService.selectConfigByKey("sys.account.passwordValidateDays"));
        if (passwordValidateDays != null && passwordValidateDays > 0)
        {
            if (StringUtils.isNull(pwdUpdateDate))
            {
                // 如果从未修改过初始密码，直接提醒过期
                return true;
            }
            Date nowDate = DateUtils.getNowDate();
            return DateUtils.differentDaysByMillisecond(nowDate, pwdUpdateDate) > passwordValidateDays;
        }
        return false;
    }
}
