package com.metaorg.common.utils.http;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 通用JSON解析工具类
 * 提供直观易用的JSON操作方法，支持容错解析和嵌套字段访问
 */
public class JsonUtils {
    
    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private JsonUtils() { }
    
    // ==================== 基础解析方法 ====================
    
    /**
     * 解析JSON字符串为Map
     * 
     * @param json JSON字符串
     * @return 解析后的Map，解析失败返回null
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> parseJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(json, Map.class);
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", e.getMessage());
            return null;
        }
    }
    
    // ==================== 嵌套字段访问方法 ====================
    
    /**
     * 从JSON字符串中获取嵌套字段的字符串值
     * 支持点号分隔的路径，如 "data.user.name"
     * 
     * @param json JSON字符串
     * @param path 字段路径，支持点号分隔，如 "data.user.name"
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static String getString(String json, String path, String defaultValue) {
        Object value = getNestedValue(json, path);
        return value != null ? String.valueOf(value) : defaultValue;
    }
    
    /**
     * 从JSON字符串中获取嵌套字段的字符串值（支持多个可能的路径）
     * 
     * @param json JSON字符串
     * @param defaultValue 默认值
     * @param paths 可能的字段路径数组
     * @return 字段值
     */
    public static String getString(String json, String defaultValue, String... paths) {
        for (String path : paths) {
            Object value = getNestedValue(json, path);
            if (value != null) {
                return String.valueOf(value);
            }
        }
        return defaultValue;
    }
    
    /**
     * 从JSON字符串中获取嵌套字段的整数值
     * 
     * @param json JSON字符串
     * @param path 字段路径，支持点号分隔，如 "data.user.age"
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static int getInt(String json, String path, int defaultValue) {
        Object value = getNestedValue(json, path);
        return parseInt(value, defaultValue);
    }
    
    /**
     * 从JSON字符串中获取嵌套字段的整数值（支持多个可能的路径）
     * 
     * @param json JSON字符串
     * @param defaultValue 默认值
     * @param paths 可能的字段路径数组
     * @return 字段值
     */
    public static int getInt(String json, int defaultValue, String... paths) {
        for (String path : paths) {
            Object value = getNestedValue(json, path);
            if (value != null) {
                return parseInt(value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * 从JSON字符串中获取嵌套字段的布尔值
     * 
     * @param json JSON字符串
     * @param path 字段路径，支持点号分隔，如 "data.user.isAdmin"
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static boolean getBoolean(String json, String path, boolean defaultValue) {
        Object value = getNestedValue(json, path);
        return parseBoolean(value, defaultValue);
    }
    
    /**
     * 从JSON字符串中获取嵌套字段的对象值
     * 
     * @param json JSON字符串
     * @param path 字段路径，支持点号分隔，如 "data.user"
     * @return 字段值（Map类型）
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getObject(String json, String path) {
        Object value = getNestedValue(json, path);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return null;
    }
    
    /**
     * 从JSON字符串中获取嵌套字段的数组值
     * 
     * @param json JSON字符串
     * @param path 字段路径，支持点号分隔，如 "data.user.hobbies"
     * @return 字段值（字符串数组）
     */
    public static String[] getStringArray(String json, String path) {
        Object value = getNestedValue(json, path);
        return parseStringArray(value);
    }
    
    /**
     * 获取嵌套字段的值
     * 
     * @param json JSON字符串
     * @param path 字段路径，支持点号分隔
     * @return 字段值
     */
    private static Object getNestedValue(String json, String path) {
        if (path == null || path.trim().isEmpty()) {
            return null;
        }
        
        Map<String, Object> map = parseJson(json);
        if (map == null) {
            return null;
        }
        
        return getNestedValueFromMap(map, path);
    }
    
    /**
     * 从Map中获取嵌套字段的值
     * 
     * @param map 数据Map
     * @param path 字段路径，支持点号分隔
     * @return 字段值
     */
    @SuppressWarnings("unchecked")
    private static Object getNestedValueFromMap(Map<String, Object> map, String path) {
        if (map == null || path == null || path.trim().isEmpty()) {
            return null;
        }
        
        String[] pathParts = path.split("\\.");
        Object current = map;
        
        for (String part : pathParts) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(part);
                if (current == null) {
                    return null;
                }
            } else {
                return null;
            }
        }
        
        return current;
    }
    
    // ==================== 标准API响应解析方法 ====================
    
    /**
     * 从JSON字符串中获取响应码
     * 
     * @param json JSON字符串
     * @param defaultValue 默认值
     * @return 响应码
     */
    public static int getCode(String json, int defaultValue) {
        return getInt(json, defaultValue, "code", "status", "resultCode", "errorCode", "returnCode");
    }
    
    /**
     * 从JSON字符串中获取响应消息
     * 
     * @param json JSON字符串
     * @param defaultValue 默认值
     * @return 响应消息
     */
    public static String getMessage(String json, String defaultValue) {
        return getString(json, defaultValue, "msg", "message", "errorMsg", "description", "reason");
    }
    
    /**
     * 从JSON字符串中获取响应数据
     * 
     * @param json JSON字符串
     * @return 响应数据（Map类型）
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getData(String json) {
        Map<String, Object> map = parseJson(json);
        if (map == null) return null;
        
        // 尝试多种可能的data字段名
        String[] dataFields = {"data", "result", "content", "body", "payload"};
        for (String fieldName : dataFields) {
            Object value = map.get(fieldName);
            if (value instanceof Map) {
                return (Map<String, Object>) value;
            }
        }
        return null;
    }
    
    /**
     * 判断JSON响应是否成功
     * 
     * @param json JSON字符串
     * @return 是否成功
     */
    public static boolean isSuccess(String json) {
        return getCode(json, -1) == 0;
    }
    
    // ==================== 从Map中获取字段值的方法 ====================
    
    /**
     * 从Map中获取字符串字段值
     * 
     * @param map 数据Map
     * @param fieldName 字段名
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static String getString(Map<String, Object> map, String fieldName, String defaultValue) {
        if (map == null) return defaultValue;
        Object value = map.get(fieldName);
        return value != null ? String.valueOf(value) : defaultValue;
    }
    
    /**
     * 从Map中获取字符串字段值（支持多个可能的字段名）
     * 
     * @param map 数据Map
     * @param defaultValue 默认值
     * @param fieldNames 可能的字段名
     * @return 字段值
     */
    public static String getString(Map<String, Object> map, String defaultValue, String... fieldNames) {
        if (map == null) return defaultValue;
        
        for (String fieldName : fieldNames) {
            Object value = map.get(fieldName);
            if (value != null) {
                return String.valueOf(value);
            }
        }
        return defaultValue;
    }
    
    /**
     * 从Map中获取整数字段值
     * 
     * @param map 数据Map
     * @param fieldName 字段名
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static int getInt(Map<String, Object> map, String fieldName, int defaultValue) {
        if (map == null) return defaultValue;
        Object value = map.get(fieldName);
        return parseInt(value, defaultValue);
    }
    
    /**
     * 从Map中获取整数字段值（支持多个可能的字段名）
     * 
     * @param map 数据Map
     * @param defaultValue 默认值
     * @param fieldNames 可能的字段名
     * @return 字段值
     */
    public static int getInt(Map<String, Object> map, int defaultValue, String... fieldNames) {
        if (map == null) return defaultValue;
        
        for (String fieldName : fieldNames) {
            Object value = map.get(fieldName);
            if (value != null) {
                return parseInt(value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * 从Map中获取布尔字段值
     * 
     * @param map 数据Map
     * @param fieldName 字段名
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static boolean getBoolean(Map<String, Object> map, String fieldName, boolean defaultValue) {
        if (map == null) return defaultValue;
        Object value = map.get(fieldName);
        return parseBoolean(value, defaultValue);
    }
    
    /**
     * 从Map中获取对象字段值
     * 
     * @param map 数据Map
     * @param fieldName 字段名
     * @return 字段值（Map类型）
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getObject(Map<String, Object> map, String fieldName) {
        if (map == null) return null;
        Object value = map.get(fieldName);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return null;
    }
    
    /**
     * 从Map中获取数组字段值
     * 
     * @param map 数据Map
     * @param fieldName 字段名
     * @return 字段值（字符串数组）
     */
    public static String[] getStringArray(Map<String, Object> map, String fieldName) {
        if (map == null) return new String[0];
        Object value = map.get(fieldName);
        return parseStringArray(value);
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 安全地解析整数
     */
    private static int parseInt(Object obj, int defaultValue) {
        if (obj == null) return defaultValue;
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        try {
            return Integer.parseInt(String.valueOf(obj));
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 安全地解析布尔值
     */
    private static boolean parseBoolean(Object obj, boolean defaultValue) {
        if (obj == null) return defaultValue;
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        try {
            return Boolean.parseBoolean(String.valueOf(obj));
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    /**
     * 安全地解析字符串数组
     */
    private static String[] parseStringArray(Object obj) {
        if (obj == null) return new String[0];
        
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            String[] array = new String[list.size()];
            for (int i = 0; i < list.size(); i++) {
                array[i] = String.valueOf(list.get(i));
            }
            return array;
        }
        
        if (obj instanceof String[]) {
            return (String[]) obj;
        }
        
        return new String[0];
    }
    
    // ==================== 创建JSON的方法 ====================
    
    /**
     * 创建标准API响应JSON
     * 
     * @param code 响应码
     * @param msg 响应消息
     * @param data 响应数据
     * @return JSON字符串
     */
    public static String createResponse(int code, String msg, Object data) {
        try {
            java.util.HashMap<String, Object> map = new java.util.HashMap<>();
            map.put("code", code);
            map.put("msg", msg);
            map.put("data", data);
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            log.error("创建JSON响应失败: {}", e.getMessage());
            return "{\"code\":" + code + ",\"msg\":\"" + msg + "\",\"data\":null}";
        }
    }
    
    /**
     * 创建成功响应JSON
     * 
     * @param data 响应数据
     * @return JSON字符串
     */
    public static String createSuccessResponse(Object data) {
        return createResponse(0, "成功", data);
    }
    
    /**
     * 创建错误响应JSON
     * 
     * @param code 错误码
     * @param msg 错误消息
     * @return JSON字符串
     */
    public static String createErrorResponse(int code, String msg) {
        return createResponse(code, msg, null);
    }
} 