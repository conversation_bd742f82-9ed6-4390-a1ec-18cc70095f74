#!/usr/bin/env python3
"""
NFRA爬虫安装脚本
自动设置虚拟环境和依赖
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    if sys.version_info < (3, 9):
        print(f"❌ Python版本过低: {sys.version}")
        print("   需要Python 3.9或更高版本")
        return False
    print(f"  ✅ Python版本: {sys.version}")
    return True

def install_uv():
    """安装uv包管理器"""
    print("📦 检查uv包管理器...")
    try:
        subprocess.run(["uv", "--version"], capture_output=True, check=True)
        print("  ✅ uv已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  📥 安装uv...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "uv"], check=True)
            print("  ✅ uv安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"  ❌ uv安装失败: {e}")
            return False

def setup_environment():
    """设置环境"""
    print("🔧 设置项目环境...")
    
    # 检查项目文件
    required_files = ["pyproject.toml", "src/nfra_crawler/main.py"]
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 找不到必要文件: {file}")
            return False
    
    # 复制环境变量文件
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print("  ✅ 创建.env文件")
        else:
            print("  ⚠️ 未找到.env.example文件")
    
    # 创建必要目录
    dirs = ["logs", "outputs", "config"]
    for dir_name in dirs:
        os.makedirs(dir_name, exist_ok=True)
        print(f"  ✅ 创建目录: {dir_name}")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("📚 安装项目依赖...")
    try:
        # 同步依赖
        subprocess.run(["uv", "sync"], check=True)
        print("  ✅ Python依赖安装成功")
        
        # 安装playwright浏览器
        print("  🌐 安装Playwright浏览器...")
        subprocess.run(["uv", "run", "playwright", "install", "chromium"], check=True)
        print("  ✅ Playwright浏览器安装成功")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"  ❌ 依赖安装失败: {e}")
        return False

def run_health_check():
    """运行健康检查"""
    print("🏥 运行健康检查...")
    try:
        result = subprocess.run(
            ["uv", "run", "python", "-m", "nfra_crawler.cli", "health"],
            capture_output=True, text=True, timeout=30
        )
        
        if result.returncode == 0:
            print("  ✅ 健康检查通过")
            return True
        else:
            print("  ⚠️ 健康检查失败（可能需要配置数据库和API密钥）")
            print(f"  输出: {result.stdout}")
            print(f"  错误: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("  ⚠️ 健康检查超时")
        return False
    except Exception as e:
        print(f"  ❌ 健康检查异常: {e}")
        return False

def create_run_script():
    """创建运行脚本"""
    print("📝 创建运行脚本...")
    
    # Windows批处理脚本
    bat_content = '''@echo off
REM NFRA爬虫运行脚本

echo === NFRA爬虫 ===

if "%1"=="--export" if not "%2"=="" (
    echo 导出模式: 导出到文件 %2
    uv run python -m nfra_crawler.main --export "%2"
) else if "%1"=="--help" (
    uv run python -m nfra_crawler.main --help
) else if "%1"=="--health" (
    uv run python -m nfra_crawler.cli health
) else if "%1"=="--test" (
    uv run python -m nfra_crawler.cli test-crawler
) else (
    echo 正常模式: 启动消息队列处理
    uv run python -m nfra_crawler.main %*
)

pause
'''
    
    with open("run.bat", "w", encoding="utf-8") as f:
        f.write(bat_content)
    print("  ✅ 创建 run.bat")
    
    # Linux/macOS shell脚本
    sh_content = '''#!/bin/bash

# NFRA爬虫运行脚本

echo "=== NFRA爬虫 ==="

if [ "$1" = "--export" ] && [ -n "$2" ]; then
    echo "导出模式: 导出到文件 $2"
    uv run python -m nfra_crawler.main --export "$2"
elif [ "$1" = "--help" ]; then
    uv run python -m nfra_crawler.main --help
elif [ "$1" = "--health" ]; then
    uv run python -m nfra_crawler.cli health
elif [ "$1" = "--test" ]; then
    uv run python -m nfra_crawler.cli test-crawler
else
    echo "正常模式: 启动消息队列处理"
    uv run python -m nfra_crawler.main "$@"
fi
'''
    
    with open("run.sh", "w", encoding="utf-8") as f:
        f.write(sh_content)
    
    # 设置执行权限
    try:
        os.chmod("run.sh", 0o755)
        print("  ✅ 创建 run.sh")
    except:
        print("  ⚠️ 创建 run.sh（可能需要手动设置执行权限）")

def main():
    """主函数"""
    print("🎯 NFRA爬虫安装程序")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("src/nfra_crawler/main.py"):
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    # 步骤1: 检查Python版本
    if not check_python_version():
        return 1
    
    # 步骤2: 安装uv
    if not install_uv():
        return 1
    
    # 步骤3: 设置环境
    if not setup_environment():
        return 1
    
    # 步骤4: 安装依赖
    if not install_dependencies():
        return 1
    
    # 步骤5: 创建运行脚本
    create_run_script()
    
    # 步骤6: 健康检查
    health_ok = run_health_check()
    
    print("\n🎉 安装完成！")
    print("\n📋 使用方法:")
    print("  Windows: run.bat --export data.xlsx")
    print("  Linux/macOS: ./run.sh --export data.xlsx")
    print("  直接运行: uv run python -m nfra_crawler.main --export data.xlsx")
    
    if not health_ok:
        print("\n⚠️ 注意事项:")
        print("  1. 请编辑.env文件，设置必要的环境变量")
        print("  2. 确保OPENAI_API_KEY已正确配置")
        print("  3. 如需数据库功能，请配置PostgreSQL连接")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
