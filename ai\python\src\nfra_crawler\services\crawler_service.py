"""
网页爬虫服务
"""

import asyncio
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse, parse_qs
from datetime import datetime, date
from loguru import logger

from crawl4ai import Async<PERSON>ebCrawler, BrowserConfig, CrawlerRunConfig, <PERSON>acheMode
from nfra_crawler.utils.html import html_utils
from nfra_crawler.config.settings import settings
from nfra_crawler.models.database import CrawlRequest, BizSyncPunishItem
from urllib.parse import urljoin

class CrawlerService:
    """爬虫服务类"""
    
    def __init__(self):
        self.browser_config = BrowserConfig(
            headless=settings.crawler.headless,
            java_script_enabled=True,
            verbose=False
        )
        self.base_url = settings.crawler.target_url
        self.timeout = settings.crawler.timeout
    
    async def crawl_punish_list(self, crawl_request: CrawlRequest, sync_record_id: str) -> List[BizSyncPunishItem]:
        request_id = crawl_request.request_id
        
        """爬取处罚公示列表"""
        logger.info(f"开始爬取处罚公示列表，请求ID: {request_id}")
        
        all_items = []
        page = 1
        max_pages = crawl_request.page_count if crawl_request.page_count > 0 else 10000  # 设置最大页数限制，防止无限循环
        
        if crawl_request.url:
            self.base_url = crawl_request.url

        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            while max_pages > 0:
                logger.debug(f"正在爬取第 {page} 页")
                
                try:
                    # 构建页面URL
                    page_url = self._build_page_url(page)
        
                    logger.debug(f"开始爬取处罚公示列表，请求URL: {page_url}")
                    
                    # 配置爬虫运行参数
                    run_config = CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        remove_overlay_elements=True,
                        wait_until="domcontentloaded",
                        wait_for="css:a.ng-scope",
                        page_timeout=self.timeout * 1000,
                        delay_before_return_html=3,  # 等待3秒让JavaScript执行完成
                        js_code=self._get_pagination_js_code(page) if page > 1 else None
                    )
                    
                    # 执行爬取
                    result = await crawler.arun(url=page_url, config=run_config)
                    
                    if not result.success:
                        logger.error(f"爬取第 {page} 页失败: {result.error_message}")
                        break
                    
                    # 解析页面内容
                    page_items = await self._parse_list_page(result.html, request_id, sync_record_id)
                    
                    if not page_items:
                        logger.info(f"第 {page} 页没有找到处罚记录，停止爬取")
                        break
                    
                    logger.info(f"第 {page} 页找到 {len(page_items)} 条记录")

                    # 根据日期范围过滤处罚记录
                    filtered_items = self._filter_items_by_date_range(page_items, crawl_request)

                    logger.info(f"日期过滤后，剩余 {len(filtered_items)} 条处罚记录")
                    
                    all_items.extend(filtered_items)
                    
                    if len(page_items) != len(filtered_items):
                        logger.info("数据已超过日期范围，停止爬取")
                        break;
                    
                    # 检查是否有下一页
                    if not self._has_next_page(result.html, page):
                        logger.info("已到达最后一页")
                        break
                    
                    page += 1
                    max_pages -= 1
                    
                    # 添加延迟，避免请求过于频繁
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"爬取第 {page} 页时发生错误: {e}")
                    break
        
        logger.info(f"爬取完成，共获取 {len(all_items)} 条处罚记录")
        return all_items
    
    def _get_pagination_js_code(self, page_num: int) -> str:
        """
        生成分页点击的JavaScript代码
        
        Args:
            page_num: 目标页码
            
        Returns:
            JavaScript代码字符串
        """
        return f"""
        // 等待页面加载完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 查找并点击指定页码
        const pageSelectors = [
            'a:contains("{page_num}")',
            'a[href*="page={page_num}"]',
            'a[onclick*="{page_num}"]',
            `a:nth-of-type({page_num})`,
            '.pagination a:contains("{page_num}")',
            '.pager a:contains("{page_num}")'
        ];
        
        let clicked = false;
        for (let selector of pageSelectors) {{
            try {{
                const element = document.querySelector(selector);
                if (element && element.textContent.trim() === "{page_num}") {{
                    element.click();
                    clicked = true;
                    break;
                }}
            }} catch (e) {{
                console.log("Selector failed:", selector, e);
            }}
        }}
        
        // 如果没找到具体页码，尝试点击"下一页"
        if (!clicked && {page_num} > 1) {{
            const nextSelectors = [
                'a:contains("下一页")',
                'a:contains("Next")',
                'a[title*="下一页"]',
                '.next-page',
                '.pagination .next'
            ];
            
            for (let selector of nextSelectors) {{
                try {{
                    const element = document.querySelector(selector);
                    if (element) {{
                        for (let i = 1; i < {page_num}; i++) {{
                            element.click();
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }}
                        clicked = true;
                        break;
                    }}
                }} catch (e) {{
                    console.log("Next page selector failed:", selector, e);
                }}
            }}
        }}
        
        // 等待页面更新
        if (clicked) {{
            await new Promise(resolve => setTimeout(resolve, 3000));
        }}
        
        return clicked;
        """
        
    async def crawl_punish_detail(self, item: BizSyncPunishItem) -> Optional[str]:
        """爬取处罚详情页面的HTML内容"""
        if not item.source_url:
            logger.warning(f"处罚项 {item.id} 没有详情页面URL")
            return None
        
        logger.info(f"开始爬取处罚详情页面: {item.source_url}")
        
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            try:
                run_config = CrawlerRunConfig(
                    css_selector="#wenzhang-content table",
                    cache_mode=CacheMode.BYPASS,
                    wait_for="js:() => document.querySelector('#wenzhang-content table')?.rows.length > 0",
                    wait_until="domcontentloaded"
                )
                
                result = await crawler.arun(url=item.source_url, config=run_config)
                
                if result.success:
                    # 提取包含处罚信息的表格HTML
                    table_html = self._extract_table_html(result.html)
                    return table_html
                else:
                    logger.error(f"爬取详情页面失败: {result.error_message}")
                    return None
                    
            except Exception as e:
                logger.error(f"爬取详情页面时发生错误: {e}")
                return None
    
    def _build_page_url(self, page: int) -> str:
        """构建分页URL"""
        if page == 1:
            return self.base_url
        
        # 根据实际网站的分页规则构建URL
        # 这里需要根据实际的网站分页机制进行调整
        base_parts = self.base_url.split('#')
        if len(base_parts) > 1:
            return f"{base_parts[0]}#{page}"
        else:
            return f"{self.base_url}&page={page}"
    
    async def _parse_list_page(self, html: str, request_id: str, sync_record_id: str) -> List[BizSyncPunishItem]:
        """使用AI智能解析处罚公示列表页面"""
        try:
            # # 首先尝试AI解析
            # items = await self._parse_list_page_with_ai(html, request_id, sync_record_id)
            # if items:
            #     logger.info(f"AI解析成功，获取到 {len(items)} 个处罚项")
            #     return items

            # # AI解析失败时回退到传统解析
            # logger.warning("AI解析失败，回退到传统解析方法")
            return self._parse_list_page_traditional(html, request_id, sync_record_id)

        except Exception as e:
            logger.error(f"解析列表页面时发生错误: {e}")
            return []

    async def _parse_list_page_with_ai(self, html: str, request_id: str, sync_record_id: str) -> List[BizSyncPunishItem]:
        """使用AI解析处罚公示列表页面"""
        from nfra_crawler.services.ai_service import ai_service

        # 提取关键的HTML片段
        soup = html_utils.remove_style_attributes(html)
        
        main_container = soup.find('div', class_='list caidan-right-list')
        if not main_container:
            logger.warning("未找到主列表容器")
            return []

        # 清理HTML，只保留核心内容
        clean_html = str(main_container)

        # AI解析提示词
        ai_prompt = '''
        请解析以下HTML内容，提取处罚公示列表信息。返回JSON格式数据：

        {
            "items": [
                {
                    "title": "完整标题",
                    "org_name": "机构名称（如：喀什金融监管分局、新疆金融监管局等）",
                    "published_date": "发布日期(YYYY-MM-DD格式)",
                    "doc_id": "文档ID",
                    "detail_url": "详情页面相对路径"
                }
            ]
        }

        注意：
        1. 从标题中准确提取机构名称，如"喀什金融监管分局行政处罚信息公示列表"中的"喀什金融监管分局"
        2. 确保日期格式正确
        3. 提取href中的docId参数
        4. 只提取真实的处罚公示项，忽略导航链接等无关内容
        '''

        try:
            result = await ai_service.parse_html_content(clean_html, ai_prompt)
            return self._convert_ai_result_to_items(result, request_id, sync_record_id)
        except Exception as e:
            logger.error(f"AI解析失败: {e}")
            return []
    
    def _convert_ai_result_to_items(self, ai_result: dict, request_id: str, sync_record_id: str) -> List[BizSyncPunishItem]:
        """将AI解析结果转换为BizSyncPunishItem对象"""
        items = []

        for item_data in ai_result.get('items', []):
            try:
                # 构建完整URL
                relative_url = item_data.get('detail_url', '')
                source_url = urljoin(self.base_url, relative_url)

                logger.debug(f"构建URL: {relative_url} -> {source_url}")

                # 解析日期
                published_date = None
                if item_data.get('published_date'):
                    try:
                        published_date = datetime.strptime(item_data['published_date'], '%Y-%m-%d')
                    except ValueError:
                        logger.warning(f"无法解析日期: {item_data['published_date']}")

                item = BizSyncPunishItem(
                    sync_record_id=sync_record_id,
                    request_id=request_id,
                    title=item_data.get('title', ''),
                    reward_punish_org=item_data.get('org_name', ''),
                    published_date=published_date,
                    source_id=item_data.get('doc_id', ''),
                    source_url=source_url
                )

                items.append(item)
                logger.debug(f"转换AI结果: {item.title} ({source_url}) - {item.reward_punish_org}")

            except Exception as e:
                logger.error(f"转换AI结果时出错: {e}")
                continue

        return items

    def _parse_list_page_traditional(self, html: str, request_id: str, sync_record_id: str) -> List[BizSyncPunishItem]:
        """传统方法解析列表页面（AI解析失败时的备用方案）"""
        items = []

        try:
            soup = html_utils.remove_style_attributes(html)

            # 查找所有列表项
            panel_rows = soup.find_all('div', class_='panel-row ng-scope')
            logger.info(f"传统解析找到 {len(panel_rows)} 个列表项")

            for row in panel_rows:
                try:
                    # 提取标题和链接
                    title_span = row.find('span', class_='title')
                    if not title_span:
                        continue

                    title_link = title_span.find('a', class_='ng-binding')
                    if not title_link:
                        continue

                    title = title_link.get_text(strip=True)
                    relative_url = title_link.get('href', '')

                    # 构建完整URL
                    source_url = urljoin(self.base_url, relative_url)

                    # 提取机构名称
                    org_name = self._extract_org_name_traditional(title)

                    # 提取发布日期
                    date_span = row.find('span', class_='date ng-binding')
                    published_date = None
                    if date_span:
                        date_text = date_span.get_text(strip=True)
                        try:
                            published_date = datetime.strptime(date_text, '%Y-%m-%d')
                        except ValueError:
                            logger.warning(f"无法解析日期: {date_text}")

                    # 提取docId作为source_id
                    doc_id_match = re.search(r'docId=(\d+)', relative_url)
                    source_id = doc_id_match.group(1) if doc_id_match else ''

                    # 创建处罚公示项
                    item = BizSyncPunishItem(
                        sync_record_id=sync_record_id,
                        request_id=request_id,
                        title=title,
                        reward_punish_org=org_name,
                        published_date=published_date,
                        source_id=source_id,
                        source_url=source_url
                    )

                    items.append(item)
                    logger.debug(f"传统解析到处罚项: {title} - {org_name}")

                except Exception as e:
                    logger.error(f"传统解析列表项时出错: {e}")
                    continue

        except Exception as e:
            logger.error(f"传统解析列表页面时发生错误: {e}")

        return items

    def _extract_org_name_traditional(self, title: str) -> str:
        """从标题中提取金融监管机构名称
    
        Args:
            title: 行政处罚信息标题
            
        Returns:
            提取到的机构名称，如未匹配到则返回空字符串
        """
        
        # 预处理：去除括号及编号部分
        cleaned_title = re.sub(r'（.*?）|\(.*?\)|〔.*?〕', '', title)
        
        # 正则模式匹配机构名称（按优先级排序）
        patterns = [
            r'(国家金融监督管理总局.*?监管(?:分局|局))',  # 完整国家机构名称
            r'(.*?金融监管(?:分局|局))',  # 地方金融监管机构
            r'(.*?监管(?:分局|局))'  # 通用监管机构
        ]

        for pattern in patterns:
            match = re.search(pattern, cleaned_title)
            if match:
                return match.group(1)
                
        return ""  # 未匹配到返回空字符串

    def _has_next_page(self, html: str, page: int) -> bool:
        """
        检查是否有下一页

        Args:
            html: 页面HTML内容
            page: 当前页码

        Returns:
            bool: 是否有下一页
        """
        try:
            soup = html_utils.remove_style_attributes(html)

            # 查找是否有下一个数字的页码链接
            page_links = soup.find_all('a', class_='ng-scope')
            current_page_found = False
            next_page_found = False

            for link in page_links:
                link_text = link.get_text().strip()
                if link_text.isdigit():
                    page_num = int(link_text)
                    if page_num == page:
                        current_page_found = True
                    elif page_num > page:
                        next_page_found = True
                        logger.debug(f"找到后续页码链接: {page_num}")
                        break

            if current_page_found and next_page_found:
                return True

            return False

        except Exception as e:
            logger.warning(f"检查下一页时发生错误: {e}")
            return False

    def _extract_table_html(self, html: str) -> Optional[str]:
        """提取包含处罚信息的表格HTML"""
        try:
            soup = html_utils.remove_style_attributes(html)

            # 查找包含处罚信息的表格
            # table = soup.find(id='wenzhang-content') 
            # if table:
            #     logger.debug(f"包含处罚信息的表格HTML: {str(table)}")
            #     return str(table)

            tables = soup.find_all('table')
            logger.debug(f"页面包含 {len(tables)} 个表格： {tables}")

            for i, table in enumerate(tables):
                table_text = table.get_text()

                # 更精确的关键词匹配
                punishment_keywords = ['当事人', '违法违规', '行政处罚', '作出决定机关', '处罚决定书']

                # 检查是否包含多个关键词（提高准确性）
                keyword_count = sum(1 for keyword in punishment_keywords if keyword in table_text)

                if keyword_count >= 3:  # 至少包含3个关键词
                    logger.info(f"找到处罚信息表格 {i+1}，包含 {keyword_count} 个关键词，文本长度: {len(table_text)}")
                    return str(table)
                elif keyword_count >= 1:
                    logger.debug(f"表格 {i+1} 包含 {keyword_count} 个关键词，可能是处罚表格")

            # 如果没有找到高匹配度的表格，尝试更宽松的匹配
            for i, table in enumerate(tables):
                table_text = table.get_text()
                basic_keywords = ['当事人', '处罚', '违法', '决定书', '机关']

                if sum(1 for keyword in basic_keywords if keyword in table_text) >= 3:
                    logger.info(f"使用宽松匹配找到处罚表格 {i+1}")
                    return str(table)

            logger.warning("未找到包含处罚信息的表格")
            return None

        except Exception as e:
            logger.error(f"提取表格HTML时发生错误: {e}")
            return None

    def _filter_items_by_date_range(self, items: List[BizSyncPunishItem], crawl_request: CrawlRequest) -> List[BizSyncPunishItem]:
        """
        根据日期范围过滤处罚记录

        Args:
            items: 处罚记录列表
            crawl_request: 爬虫请求对象，包含日期范围

        Returns:
            过滤后的处罚记录列表
        """
        if not crawl_request.begin_date and not crawl_request.end_date:
            # 如果没有设置日期范围，返回所有记录
            return items

        filtered_items = []

        for item in items:
            # 如果 published_date 为 None，跳过该记录
            if item.published_date is None:
                logger.debug(f"跳过没有发布日期的记录: {item.title}")
                continue

            # 将 datetime 转换为 date 进行比较
            published_date = item.published_date.date()

            # 检查是否在日期范围内
            in_range = True
            
            if crawl_request.begin_date:
                if published_date < crawl_request.begin_date:
                    in_range = False
                    logger.debug(f"记录 {item.title} 发布日期 {published_date} 早于开始日期 {crawl_request.begin_date}")

            if crawl_request.end_date and in_range:
                if published_date > crawl_request.end_date:
                    in_range = False
                    logger.debug(f"记录 {item.title} 发布日期 {published_date} 晚于结束日期 {crawl_request.end_date}")

            if in_range:
                filtered_items.append(item)
                logger.debug(f"保留记录: {item.title} (发布日期: {published_date})")

        return filtered_items


# 全局爬虫服务实例
crawler_service = CrawlerService()
