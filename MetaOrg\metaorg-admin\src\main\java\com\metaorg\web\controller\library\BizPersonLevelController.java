package com.metaorg.web.controller.library;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonLevel;
import com.metaorg.library.service.IBizPersonLevelService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 职务层次Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/level")
public class BizPersonLevelController extends BaseController
{
    private String prefix = "library/level";

    @Autowired
    private IBizPersonLevelService bizPersonLevelService;

    @RequiresPermissions("library:level:view")
    @GetMapping()
    public String level()
    {
        return prefix + "/level";
    }

    /**
     * 查询职务层次列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonLevel bizPersonLevel)
    {
        startPage();
        List<BizPersonLevel> list = bizPersonLevelService.selectBizPersonLevelList(bizPersonLevel);
        return getDataTable(list);
    }

    /**
     * 导出职务层次列表
     */
    @RequiresPermissions("library:level:export")
    @Log(title = "职务层次情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonLevel bizPersonLevel)
    {
        List<BizPersonLevel> list = bizPersonLevelService.selectBizPersonLevelList(bizPersonLevel);
        ExcelUtil<BizPersonLevel> util = new ExcelUtil<BizPersonLevel>(BizPersonLevel.class);
        return util.exportExcel(list, "职务层次数据");
    }

    /**
     * 新增职务层次
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存职务层次
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "职务层次情况", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "职务层次情况", tableName = PersonTable.BIZ_PERSON_LEVEL, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonLevel bizPersonLevel)
    {
        return toAjax(bizPersonLevelService.insertBizPersonLevel(bizPersonLevel));
    }

    /**
     * 修改职务层次
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonLevel bizPersonLevel = bizPersonLevelService.selectBizPersonLevelById(id);
        mmap.put("bizPersonLevel", bizPersonLevel);
        return prefix + "/edit";
    }

    /**
     * 修改保存职务层次
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "职务层次情况", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "职务层次情况", tableName = PersonTable.BIZ_PERSON_LEVEL, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonLevel bizPersonLevel)
    {
        return toAjax(bizPersonLevelService.updateBizPersonLevel(bizPersonLevel));
    }

    /**
     * 删除职务层次
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "职务层次情况", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "职务层次情况", tableName = PersonTable.BIZ_PERSON_LEVEL, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonLevelService.deleteBizPersonLevelByIds(ids));
    }

    /**
     * 排序职务层次
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "职务层次情况", businessType = BusinessType.UPDATE)
    @PostMapping( "/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids)
    {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++)
        {
            BizPersonLevel bizPersonLevel = bizPersonLevelService.selectBizPersonLevelById(idsArray[i]);
            bizPersonLevel.setOrderNum(Convert.toLong(i + 1));
            bizPersonLevelService.updateBizPersonLevel(bizPersonLevel);
        }
        return success();
    }

    private long getMaxOrderNum(String personId)
    {
        BizPersonLevel bizPersonLevel = new BizPersonLevel();
        bizPersonLevel.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonLevel> list = bizPersonLevelService.selectBizPersonLevelList(bizPersonLevel);
        if(!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
