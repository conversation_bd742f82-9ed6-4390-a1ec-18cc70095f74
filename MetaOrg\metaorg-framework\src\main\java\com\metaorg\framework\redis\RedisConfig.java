package com.metaorg.framework.redis;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;

import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.beans.factory.annotation.Autowired;



@Configuration
@EnableCaching // 开启缓存支持
public class RedisConfig extends CachingConfigurerSupport {

	@Resource
	private LettuceConnectionFactory lettuceConnectionFactory;
	
	@Autowired
	private RedisNamespaceUtil redisNamespaceUtil;

	// 缓存管理器
	@Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {

        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(keySerializer()))//key序列化方式
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(valueSerializer()))//value序列化方式
                .disableCachingNullValues()
                .entryTtl(Duration.ofSeconds(30*60));//缓存过期时间


        RedisCacheManager.RedisCacheManagerBuilder builder = RedisCacheManager.RedisCacheManagerBuilder
                .fromConnectionFactory(lettuceConnectionFactory)
                .cacheDefaults(config)
                .transactionAware()
                .withInitialCacheConfigurations(getRedisCacheConfigurationMap());

        return builder.build();
    }
	
	   private RedisSerializer<String> keySerializer() {
	        return new StringRedisSerializer();
	    }

	    private RedisSerializer<Object> valueSerializer() {
	        // 对于Shiro Session，使用Java序列化更可靠
	        // 避免Jackson序列化复杂对象的兼容性问题
	        JdkSerializationRedisSerializer serializer = new JdkSerializationRedisSerializer();
	        
	        
	        
	        return serializer;
	    }
	
	
    private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
        Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = new HashMap<>();
        /**
         * @CacheConfig(cacheNames = "SsoCache")
			public class SsoCache{
				@Cacheable(keyGenerator = "cacheKeyGenerator")
				public String getTokenByGsid(String gsid) 
			}
			//二者选其一,可以使用value上的信息，来替换类上cacheNames的信息
			@Cacheable(value = "BasicDataCache",keyGenerator = "cacheKeyGenerator")
			public String getTokenByGsid(String gsid) 
         */
        //SsoCache和BasicDataCache进行过期时间配置
        redisCacheConfigurationMap.put("menuCache", this.getRedisCacheConfigurationWithTtl(24*60*60));
        redisCacheConfigurationMap.put("BasicDataCache", this.getRedisCacheConfigurationWithTtl(30*60));
        return redisCacheConfigurationMap;
    }

    private RedisCacheConfiguration getRedisCacheConfigurationWithTtl(Integer seconds) {

        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig();
        redisCacheConfiguration = redisCacheConfiguration.entryTtl(Duration.ofSeconds(seconds))
        		.serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(keySerializer()))//key序列化方式
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(valueSerializer()))//value序列化方式;
                ;
        return redisCacheConfiguration;
    }
	
    @Bean(name = "cacheKeyGenerator")
    public KeyGenerator cacheKeyGenerator() {
        return new KeyGenerator() {
            @Override
            public Object generate(Object target, Method method, Object... params) {
            	
            	StringBuffer sb = new StringBuffer();
                sb.append(target.getClass().getName());
                sb.append(method.getName());
                for (Object obj : params) {
                    sb.append(obj.toString());
                }
                return sb.toString();
            }
        };
    }

	/**
	 * RedisTemplate配置
	 */
	@Bean
	public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory lettuceConnectionFactory) {
		// 设置序列化
		RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
		redisTemplate.setKeySerializer(keySerializer());
        redisTemplate.setHashKeySerializer(keySerializer());
        redisTemplate.setHashValueSerializer(valueSerializer());
        redisTemplate.setValueSerializer(valueSerializer());
        redisTemplate.setConnectionFactory(lettuceConnectionFactory);
        
        // 测试Redis连接
        try {
            redisTemplate.afterPropertiesSet();
        } catch (Exception e) {
            System.err.println("Failed to configure RedisTemplate: " + e.getMessage());
            e.printStackTrace();
        }
        
		return redisTemplate;
	}
	
	/**
	 * 带命名空间的RedisTemplate配置
	 */
	@Bean
	public NamespacedRedisTemplate<String, Object> namespacedRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
		NamespacedRedisTemplate<String, Object> namespacedRedisTemplate = new NamespacedRedisTemplate<>(redisTemplate, redisNamespaceUtil);
		return namespacedRedisTemplate;
	}
	
	/**
	 * 带命名空间的StringRedisTemplate配置
	 */
	@Bean
	public NamespacedRedisTemplate<String, String> namespacedStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
		NamespacedRedisTemplate<String, String> namespacedStringRedisTemplate = new NamespacedRedisTemplate<>(stringRedisTemplate, redisNamespaceUtil);
		return namespacedStringRedisTemplate;
	}
}