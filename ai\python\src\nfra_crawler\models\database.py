"""
数据库模型定义
"""

from datetime import datetime, date
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, Json, field_serializer
from nanoid import generate

class BizSyncRecord(BaseModel):
    """同步记录表模型"""
    id: str = Field(default_factory=lambda: str(generate()))
    business_type: Optional[str] = None
    business_id: Optional[str] = None
    request_id: Optional[str] = None
    begin_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    request_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None
    elapsed: Optional[int] = None  # 毫秒
    status: str = "pending"  # pending, processing, failed, succeeded
    remark: Optional[str] = None
    create_by: Optional[str] = "system"
    create_time: datetime = Field(default_factory=datetime.now)
    update_by: Optional[str] = "system"
    update_time: datetime = Field(default_factory=datetime.now)


class BizSyncPunishItem(BaseModel):
    """处罚公示列表模型"""
    id: str = Field(default_factory=lambda: str(generate()))
    sync_record_id: Optional[str] = None
    request_id: Optional[str] = None
    title: Optional[str] = None
    reward_punish_org: Optional[str] = None
    published_date: Optional[datetime] = None
    source_name: Optional[str] = "国家金融监督管理总局"
    source_id: Optional[str] = None
    source_url: Optional[str] = None
    create_by: Optional[str] = "system"
    create_time: datetime = Field(default_factory=datetime.now)
    update_by: Optional[str] = "system"
    update_time: datetime = Field(default_factory=datetime.now)


class BizSyncPunishDetail(BaseModel):
    """处罚公示明细模型"""
    id: str = Field(default_factory=lambda: str(generate()))
    punish_item_id: Optional[str] = None
    request_id: Optional[str] = None
    order_num: int = 0
    party_name: Optional[str] = None
    party_position: Optional[str] = None
    punish_doc_no: Optional[str] = None
    violation: Optional[str] = None
    punish_basis: Optional[str] = None
    punish_content: Optional[str] = None
    punish_date: Optional[datetime] = None
    decision_authority: Optional[str] = None
    party_type: Optional[str] = None  # 机构, 个人
    punish_json_content: Optional[Json[Dict[str, Any]]] = None
    source_id: Optional[str] = None
    source_url: Optional[str] = None
    bind_person_id: Optional[str] = None
    bind_punish_id: Optional[str] = None
    bind_status: Optional[str] = "none"
    create_by: Optional[str] = "system"
    create_time: datetime = Field(default_factory=datetime.now)
    update_by: Optional[str] = "system"
    update_time: datetime = Field(default_factory=datetime.now)
    
    @field_serializer('punish_date')
    def serialize_punish_date(self, v: datetime, _info):
        return v.isoformat() if v else None


class CrawlRequest(BaseModel):
    """爬虫请求模型"""
    create_time: int  # 时间戳（毫秒）
    request_id: str
    url: str
    page_count: int = 0
    begin_date: Optional[date] = None  # 开始日期
    end_date: Optional[date] = None    # 结束日期


class PunishDetailData(BaseModel):
    """处罚详情数据模型（用于AI清洗后的结构化数据）"""
    serial_no: Optional[str] = "1"
    party_name: str
    party_position: str = ""
    punish_doc_no: str = ""
    violation: str
    punish_basis: Optional[str] = None
    punish_content: str
    punish_date: Optional[datetime] = None
    decision_authority: str
    party_type: str  # 个人, 机构
    source_id: Optional[str] = None  # 详情页面URL中的docId
    source_url: Optional[str] = None  # 详情页面URL
    
    @field_serializer('punish_date')
    def serialize_punish_date(self, v: datetime, _info):
        return v.isoformat() if v else None
