package com.metaorg.web.controller.api;

import com.deepoove.poi.XWPFTemplate;
import com.github.pagehelper.PageHelper;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.BaseEntity;
import com.metaorg.common.core.domain.ResponseBodyApi;
import com.metaorg.common.core.domain.Ztree;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.library.domain.*;
import com.metaorg.library.entity.PersonXml;
import com.metaorg.library.service.*;
import com.metaorg.library.utils.AsposeUtil;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.utils.DataScopeUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.poi.util.IOUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.utils.DictUtils;
import com.metaorg.system.domain.SysPersonPost;
import com.metaorg.system.service.ISysPersonPostService;
import com.metaorg.common.core.domain.entity.SysDictData;
import com.metaorg.system.service.ISysDictTypeService;
import com.metaorg.library.service.IBizStatisticPositionAdjustService;
import com.metaorg.library.service.IBizPersonService;


import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/api/org")
public class OrgController extends BaseController {

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IBizPersonService bizPersonService;

    @Autowired
    private IBizPersonResumeService resumeService;

    @Autowired
    private IBizPersonFamilyService familyService;

    @Autowired
    private IBizPersonEducationService educationService;

    @Autowired
    private IBizPersonPoliticsService politicsService;

    @Autowired
    private IBizPersonRewardPunishService bizPersonRewardPunishService;

    @Autowired
    private IBizPersonTagService bizPersonTagService;

    @Autowired
    private IBizPersonReportService bizPersonReportService;

    @Autowired
    private ISysPersonPostService personPostService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IBizStatisticPositionAdjustService bizStatisticPositionAdjustService;

    /**
     * 获取人员列表查询条件
     */
    private void getPersonQueryOptions(BizPerson bizPerson, Map<String, Object> paramMap) {
        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizPerson.setKeyword(keyword);
        }

        String name = Convert.toStr(paramMap.get("name"), "");
        if (StringUtils.isNotEmpty(name)) {
            bizPerson.setName(name);
        }

        String sex = Convert.toStr(paramMap.get("sex"), "");
        if (StringUtils.isNotEmpty(sex)) {
            bizPerson.setSex(sex);
        }

        if(paramMap.get("manageCategoryList") != null && paramMap.get(("manageCategoryList")) != ""){
            bizPerson.getParams().put("manageCategoryList", Convert.toStrArray(paramMap.get("manageCategoryList").toString()));
        }

        if(paramMap.get("retireLeaveFlagSearch") != null && paramMap.get(("retireLeaveFlagSearch")) != ""){
            bizPerson.setRetireLeaveFlag(paramMap.get("retireLeaveFlagSearch").toString());
        }

        if(paramMap.get("nation") != null && paramMap.get(("nation")) != ""){
            bizPerson.setNation(paramMap.get("nation").toString());
        }

        Long deptId = Convert.toLong(paramMap.get("deptId"), 0L);
        if (deptId > 0) {
            bizPerson.setDeptId(deptId);
        }

        String personType = Convert.toStr(paramMap.get("personType"), "");

        if ("-1".equals(personType) || "1".equals(personType) || "2".equals(personType) || "3".equals(personType) || "4".equals(personType) || "5".equals(personType) || "7".equals(personType)) {
            // 未删除
            bizPerson.setDelFlag("0");
        }

        switch (personType) {
            // 现职人员
            case "1":
                bizPerson.setManageStatus("1,5");
                break;

            // 已退休
            case "2":
                bizPerson.setManageStatus("2");
                break;

            // 调离
            case "3":
                bizPerson.setManageStatus("3");
                break;

            // 已去世
            case "4":
                bizPerson.setManageStatus("4");
                break;

            // 其他
            case "5":
                bizPerson.setManageStatus("6");
                break;

            // 已删除干部
            case "6":
                bizPerson.setDelFlag("2");
                break;

            // 领导班子
            case "7":
                bizPerson.setLeaderFlag("Y");
                break;

            // 免职（相当于辞退）
            case "8":
                bizPerson.setManageStatus("8");
                break;

            // 未删除
            case "-1":
                bizPerson.setDelFlag("0");
                break;

            // 全部
            default:
                break;
        }

        int ageStart = Convert.toInt(paramMap.get("ageStart"), -1);
        if (ageStart >= 0) {
            bizPerson.getParams().put("ageStart", ageStart);
        }

        int ageEnd = Convert.toInt(paramMap.get("ageEnd"), -1);
        if (ageEnd >= 0) {
            bizPerson.getParams().put("ageEnd", ageEnd);
        }

        String ageLabel = Convert.toStr(paramMap.get("ageLabel"), "");
        if(StringUtils.isNotEmpty(ageLabel)){
            //根据lable获取字典值
            String ageValue = DictUtils.getDictValue("dashborad_age_stage", ageLabel);
            if(StringUtils.isNotEmpty(ageValue)){
                String[] ageValues = ageValue.split("-");
                if(ageValues.length > 0 && StringUtils.isNotEmpty(ageValues[0])){
                    bizPerson.getParams().put("ageStart", Convert.toInt(ageValues[0], 0));
                }
                if(ageValues.length > 1 && StringUtils.isNotEmpty(ageValues[1])){
                    bizPerson.getParams().put("ageEnd", Convert.toInt(ageValues[1], 200) - 1);
                }
            }
        }

        String education = Convert.toStr(paramMap.get("education") , "");
        if (StringUtils.isNotEmpty(education)) {

            String[] educations = education.split("-");
            if (educations.length == 2) {
                bizPerson.getParams().put("eduCategory", educations[0]);
                bizPerson.getParams().put("eduName", educations[1]);
            }
        }

        String leadingGroupLevel = Convert.toStr(paramMap.get("leadingGroupLevel"), "");
        if (StringUtils.isNotEmpty(leadingGroupLevel)) {
            bizPerson.getParams().put("leadingGroupLevel", leadingGroupLevel);
        }

        String isEmployed = Convert.toStr(paramMap.get("isEmployed"), "");
        if(StringUtils.isNotEmpty(isEmployed)){
            bizPerson.getParams().put("isEmployed", isEmployed);
        }

        String positionName = Convert.toStr(paramMap.get("positionName"), "");
        if(StringUtils.isNotEmpty(positionName)){
            bizPerson.getParams().put("positionName", positionName);
        }

        if (paramMap.get("positionAdjustTagList") != null && paramMap.get(("positionAdjustTagList")) != "") {
            bizPerson.getParams().put("positionAdjustTagList", Convert.toStrArray(paramMap.get("positionAdjustTagList").toString()));
        }
        String adjustBeginTime = Convert.toStr(paramMap.get("adjustBeginTime"), "");
        String adjustEndTime = Convert.toStr(paramMap.get("adjustEndTime"), "");
        if (StringUtils.isNotEmpty(adjustBeginTime) && StringUtils.isNotEmpty(adjustEndTime)) {
            bizPerson.getParams().put("adjustBeginTime", adjustBeginTime);
            bizPerson.getParams().put("adjustEndTime", adjustEndTime);
        }

        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }
    }

    @Log(title = "组织管理", businessType = BusinessType.OTHER)
    @GetMapping("/dept_tree")
    @ResponseBody
    public ResponseBodyApi<List<Ztree>> deptTree(HttpServletResponse response) {
        List<Ztree> ztrees = deptService.selectDeptTree(new SysDept());
        return new ResponseBodyApi<>(ztrees);
    }

    @Log(title = "PAD端人员信息Old", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("/person_data_old")
    @ResponseBody
    public ResponseBodyApi<List<BizPerson>> personDataOld(HttpServletResponse response) {
        BizPerson bizPerson = new BizPerson();
        bizPerson.setDelFlag("0");
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }
        PageHelper.startPage(1, 100000, "p.order_num asc,p.id desc");
        List<BizPerson> list = bizPersonService.selectAllBizPersonList(bizPerson);
        for (BizPerson person : list) {
            getPersonPdfData(person);
        }
        bizPersonService.fillPersonDeptForPersons(list);
        return new ResponseBodyApi<>(list);
    }

    @Log(title = "PAD端-同步全部人员信息", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("/person_data")
    @ResponseBody
    public ResponseBodyApi<List<BizPerson>> personData(HttpServletResponse response, @RequestParam Map<String, Object> paramMap) {
        BizPerson bizPerson = new BizPerson();
        bizPerson.setDelFlag("0");
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }
        startPage();
        List<BizPerson> list = bizPersonService.selectBizPersonList(bizPerson);
        bizPersonService.fillPersonDeptForPersons(list, "");
        for (BizPerson person : list) {
            getPersonPdfData(person);
        }

        ResponseBodyApi<List<BizPerson>> rsBody = new ResponseBodyApi<>(list);
        rsBody.setMessage("OK");
        return rsBody;
    }

    @Log(title = "PAD端-在线查询人员列表", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("/persons")
    @ResponseBody
    public ResponseBodyApi<List<BizPerson>> getPersons(HttpServletResponse response, @RequestParam Map<String, Object> paramMap) {
        BizPerson bizPerson = new BizPerson();
        bizPerson.setDelFlag("0");

        getPersonQueryOptions(bizPerson, paramMap);

        startPage();
        List<BizPerson> list = bizPersonService.selectBizPersonList(bizPerson);

        bizPersonService.fillPersonFavoritedByCurrentUser(list);

        ResponseBodyApi<List<BizPerson>> rsBody = new ResponseBodyApi<>(list);
        rsBody.setMessage("OK");
        return rsBody;
    }

    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.VIEW)
    @Log(title = "PAD端-在线查询人员详情", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("/persons/{id}")
    @ResponseBody
    public ResponseBodyApi<BizPerson> getPerson(@PathVariable("id") String id) {
        BizPerson person = bizPersonService.selectBizPersonById(id);
        if (person != null) {
            getPersonPdfData(person);
            List<BizPerson> list = new ArrayList<BizPerson>();
            list.add(person);
            bizPersonService.fillPersonDeptForPersons(list);
        }

        ResponseBodyApi<BizPerson> rsBody = new ResponseBodyApi<>(person);
        rsBody.setMessage("OK");
        return rsBody;
    }

    @PersonOperationLog(module = "360画像", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.VIEW)
    @Log(title = "PAD端-在线查询人员画像信息", businessType = BusinessType.OTHER, isSaveResponseData = false)
    @GetMapping("/persons/profile/{id}")
    @ResponseBody
    public ResponseBodyApi<BizPerson> getPersonProfile(@PathVariable("id") String id) {
        BizPerson person = bizPersonService.selectBizPersonById(id);
        if (person != null) {
            getPersonPdfData(person);
            List<BizPerson> list = new ArrayList<BizPerson>();
            list.add(person);
            bizPersonService.fillPersonDeptForPersons(list);

            BizPersonPolitics politics = new BizPersonPolitics();
            politics.setPersonId(id);
            List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);
            BizPersonPolitics filteredPolitics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
                @Override
                public boolean test(BizPersonPolitics bizPersonPolitics) {
                    return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
                }
            }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
                @Override
                public BizPersonPolitics get() {
                    return new BizPersonPolitics();
                }
            });
            person.setPolitics(Collections.singletonList(filteredPolitics));

            BizPersonRewardPunish bizPersonRewardPunish = new BizPersonRewardPunish();
            bizPersonRewardPunish.setPersonId(id);
            bizPersonRewardPunish.getParams().put("orderby", "a.reward_punish_day desc,a.id desc");
            List<BizPersonRewardPunish> rewardPunishList = bizPersonRewardPunishService.selectBizPersonRewardPunishList(bizPersonRewardPunish);
            person.setRewardPunishs(rewardPunishList);

            // 查询人员标签
            BizPersonTag bizPersonTag = new BizPersonTag();
            bizPersonTag.setPersonId(id);
            List<BizPersonTag> tags = bizPersonTagService.selectBizPersonTagList(bizPersonTag);
            person.setTags(tags);
        }

        ResponseBodyApi<BizPerson> rsBody = new ResponseBodyApi<>(person);
        rsBody.setMessage("OK");
        return rsBody;
    }

    private void getPersonPdfData(BizPerson person) {
        String personId = person.getId();
        PersonXml xml = PersonXml.fromBizPerson(person);

        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(personId);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);
        person.setResumes(resumes);

        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(personId);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);
        person.setFamilies(families);

        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(personId);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);
        person.setEducations(educations);

        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(personId);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        politics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        });

        xml.setResumes(resumes);
        xml.setFamilies(families);
        xml.setEducations(educations);
        xml.setPolitics(politics);

        OutputStream out = null;
      
        try {
            File resource = new File("./config/rmb_template.docx").getAbsoluteFile();

            XWPFTemplate template = XWPFTemplate.compile(resource).render(xml.toWordTemplateData());
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            template.writeAndClose(byteArrayOutputStream);

            ByteArrayOutputStream asposePdfByteArrayOutputStream = new ByteArrayOutputStream();
            AsposeUtil bean = new AsposeUtil();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            bean.word2pdf(asposePdfByteArrayOutputStream, byteArrayInputStream);

            person.setPdfData(asposePdfByteArrayOutputStream.toByteArray());

            asposePdfByteArrayOutputStream.close();
            byteArrayInputStream.close();
            byteArrayOutputStream.close();
        } catch (Exception e) {
        } finally {
            IOUtils.closeQuietly(out);
        }
    }

    @GetMapping("/pdf/{personId}")
    @RequiresPermissions("library:person:export")
    public void exportsPdf(@PathVariable(value = "personId") String personId, HttpServletResponse resp) {
        BizPerson person = bizPersonService.selectBizPersonById(personId);
        if (person == null) {
            return;
        }

        PersonXml xml = PersonXml.fromBizPerson(person);

        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(personId);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);

        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(personId);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);

        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(personId);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);

        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(personId);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        politics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        });

        xml.setResumes(resumes);
        xml.setFamilies(families);
        xml.setEducations(educations);
        xml.setPolitics(politics);

        OutputStream out = null;
        String safeName = StringEscapeUtils.escapeHtml4(person.getName()); //防止文件名被浏览器解析
        safeName = StringEscapeUtils.unescapeHtml4(safeName);
        String filename = safeName + "_" + System.currentTimeMillis() + ".pdf";

        try {
            File resource = new File("./config/rmb_template.docx").getAbsoluteFile();

            XWPFTemplate template = XWPFTemplate.compile(resource).render(xml.toWordTemplateData());
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            template.writeAndClose(byteArrayOutputStream);

            out = resp.getOutputStream();

            AsposeUtil bean = new AsposeUtil();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            bean.word2pdf(out, byteArrayInputStream);
            filename = FileUtils.fixFilenameByRegex(filename);

            byteArrayInputStream.close();
            byteArrayOutputStream.close();

            resp.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, "UTF-8"));
            resp.setContentType("application/pdf");// 定义输出类型
            resp.setHeader("Pragma", "no-cache");
            resp.addHeader("Cache-Control", "must-revalidate");
            resp.addHeader("Cache-Control", "no-cache");
            resp.addHeader("Cache-Control", "no-store");
            resp.setDateHeader("Expires", 0);

        } catch (Exception e) {
//            log.error("导出Pdf异常{}", e.getMessage());
            throw new UtilException("导出Pdf失败，请联系网站管理员！");
        } finally {
            IOUtils.closeQuietly(out);
        }
    }

    @PersonOperationLog(module = "基本信息", tableName = PersonTable.BIZ_PERSON, operationType = PersonOperationType.VIEW)
    @GetMapping("/person_pdf/{personId}")
    @ResponseBody
    public ResponseBodyApi<BizPerson> personPdf(@PathVariable(value = "personId") String personId) {
        BizPerson person = bizPersonService.selectBizPersonById(personId);
        if (person == null) {
            return new ResponseBodyApi<>(false, "人员不存在");
        }

        PersonXml xml = PersonXml.fromBizPerson(person);

        BizPersonResume resume = new BizPersonResume();
        resume.setPersonId(personId);
        List<BizPersonResume> resumes = resumeService.selectBizPersonResumeListOrderBy(resume);

        BizPersonFamily family = new BizPersonFamily();
        family.setPersonId(personId);
        List<BizPersonFamily> families = familyService.selectBizPersonFamilyListOrderBy(family);

        BizPersonEducation education = new BizPersonEducation();
        education.setPersonId(personId);
        List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);

        BizPersonPolitics politics = new BizPersonPolitics();
        politics.setPersonId(personId);
        List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);

        politics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
            @Override
            public boolean test(BizPersonPolitics bizPersonPolitics) {
                return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
            }
        }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
            @Override
            public BizPersonPolitics get() {
                return new BizPersonPolitics();
            }
        });

        xml.setResumes(resumes);
        xml.setFamilies(families);
        xml.setEducations(educations);
        xml.setPolitics(politics);

        OutputStream out = null;

        try {
            File resource = new File("./config/rmb_template.docx").getAbsoluteFile();

            XWPFTemplate template = XWPFTemplate.compile(resource).render(xml.toWordTemplateData());
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            template.writeAndClose(byteArrayOutputStream);

            ByteArrayOutputStream asposePdfByteArrayOutputStream = new ByteArrayOutputStream();
            AsposeUtil bean = new AsposeUtil();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            bean.word2pdf(asposePdfByteArrayOutputStream, byteArrayInputStream);

            person.setPdfData(asposePdfByteArrayOutputStream.toByteArray());

            asposePdfByteArrayOutputStream.close();
            byteArrayInputStream.close();
            byteArrayOutputStream.close();
        } catch (Exception e) {
            return new ResponseBodyApi<>(false, "生成pdf出现错误：" + e.getMessage());

        } finally {
            IOUtils.closeQuietly(out);
        }
        return new ResponseBodyApi<>(person);
    }

    private void getAnalysisQueryOptions(BaseEntity baseEntity, Map<String, Object> paramMap) {
        String deptId = paramMap.get("deptId") == null ? "" : paramMap.get("deptId").toString();
        String ids = paramMap.get("ids") == null ? "" : paramMap.get("ids").toString();
        if (!deptId.equals("")) {
            String dataScope = " and (dept_id = " + deptId + " or dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET ("
                    + deptId +",ancestors) ) )";
            baseEntity.getParams().put("dataScope", dataScope);
        }else{
            if (!getSysUser().isAdmin()) {
                DataScopeUtils.dataScopeFilter(baseEntity, getSysUser(), "vd", "", "", getSysUser().getDeptId());
            }
        }

        if(StringUtils.isNotEmpty(ids)){
            baseEntity.getParams().put("personIds", ids);
        }

        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            baseEntity.getParams().put("keyword", keyword);
        }

        String name = Convert.toStr(paramMap.get("name"), "");
        if (StringUtils.isNotEmpty(name)) {
            baseEntity.getParams().put("name", name);
        }

        String sex = Convert.toStr(paramMap.get("sex"), "");
        if (StringUtils.isNotEmpty(sex)) {
            baseEntity.getParams().put("sex", sex);
        }

        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }

        if(paramMap.get("manageCategoryList") != null && paramMap.get(("manageCategoryList")) != ""){
            baseEntity.getParams().put("manageCategoryList", Convert.toStrArray(paramMap.get("manageCategoryList").toString()));
        }

        if(paramMap.get("retireLeaveFlagSearch") != null && paramMap.get(("retireLeaveFlagSearch")) != ""){
            baseEntity.getParams().put("retireLeaveFlag", paramMap.get("retireLeaveFlag").toString());
        }

        if(paramMap.get("nation") != null && paramMap.get(("nation")) != ""){
            baseEntity.getParams().put("nation", paramMap.get("nation").toString());
        }

        String personType = Convert.toStr(paramMap.get("personType"), "");

        if ("-1".equals(personType) || "1".equals(personType) || "2".equals(personType) || "3".equals(personType) || "4".equals(personType) || "5".equals(personType) || "7".equals(personType)) {
            // 未删除
            baseEntity.getParams().put("manageStatus", "0");
        }

        switch (personType) {
            // 现职人员
            case "1":
                baseEntity.getParams().put("manageStatus", "1,5");
                break;
            // 已退休
            case "2":
                baseEntity.getParams().put("manageStatus", "2");
                break;
            // 调离
            case "3":
                baseEntity.getParams().put("manageStatus", "3");
                break;
            // 已去世
            case "4":
                baseEntity.getParams().put("manageStatus", "4");
                break;
            // 其他
            case "5":
                baseEntity.getParams().put("manageStatus", "6");
                break;
            // 领导班子
            case "7":
                baseEntity.getParams().put("leaderFlag", "Y");
                break;
            // 免职（相当于辞退）
            case "8":
                baseEntity.getParams().put("manageStatus", "8");
                break;
            // 全部
            default:
                break;
        }

        int ageStart = Convert.toInt(paramMap.get("ageStart"), -1);
        if (ageStart >= 0) {
            baseEntity.getParams().put("ageStart", ageStart);
        }

        int ageEnd = Convert.toInt(paramMap.get("ageEnd"), -1);
        if (ageEnd >= 0) {
            baseEntity.getParams().put("ageEnd", ageEnd);
        }

        String education = Convert.toStr(paramMap.get("education") , "");
        if (StringUtils.isNotEmpty(education)) {

            String[] educations = education.split("-");
            if (educations.length == 2) {
                baseEntity.getParams().put("eduCategory", educations[0]);
                baseEntity.getParams().put("eduName", educations[1]);
            }
        }
        String eduCategory = Convert.toStr(paramMap.get("eduCategory") , "");
        if (StringUtils.isNotEmpty(eduCategory)) {
            baseEntity.getParams().put("eduCategory", eduCategory);
        }
    }

    @GetMapping("/persons/analysis/age")
    @ResponseBody
    public ResponseBodyApi<List<BizElement>> ageAnalysis(@RequestParam Map<String, Object> paramMap) {
        List<BizElement> ageGroup;

        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        if(baseEntity.getParams().get("personIds") != null){
            ageGroup = bizPersonReportService.selectAgeGroupByIdsNew(baseEntity.getParams().get("personIds").toString());
        }else{
            ageGroup = bizPersonReportService.selectAgeGroupNew(baseEntity);
        }
        return new ResponseBodyApi<>(ageGroup);
    }

    @GetMapping("/persons/analysis/education")
    @ResponseBody
    public ResponseBodyApi<List<BizElement>> educationAnalysis(@RequestParam Map<String, Object> paramMap) {
        List<BizElement> eduGroup;

        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        if(baseEntity.getParams().get("personIds") != null){
            eduGroup = bizPersonReportService.selectEduGroupByIdsNew(baseEntity.getParams().get("personIds").toString());
        }else{
            eduGroup = bizPersonReportService.selectEduGroupNew(baseEntity);
        }

        String type = paramMap.get("type") == null ? "" : paramMap.get("type").toString();

        // 创建一个存储元素的List
        List<BizElement> array = new ArrayList<>();

        array.addAll(eduGroup);
        if(!type.equals("") && type != null){
            if(type.equals("full_time")){
                array = eduGroup.stream()
                        .filter(element -> element.getName().contains("全日制教育"))
                        .collect(Collectors.toList());
            }else if(type.equals("in_service")){
                array = eduGroup.stream()
                        .filter(element -> element.getName().contains("在职教育"))
                        .collect(Collectors.toList());
            }
        }

        return new ResponseBodyApi<>(array);
    }

    @GetMapping("/persons/analysis/sex")
    @ResponseBody
    public ResponseBodyApi<List<BizElement>> sexAnalysis(@RequestParam Map<String, Object> paramMap) {
        List<BizElement> sexGroup;

        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        if(baseEntity.getParams().get("personIds") != null){
            sexGroup = bizPersonReportService.selectSexGroupByIds(baseEntity.getParams().get("personIds").toString());
        }else{
            sexGroup = bizPersonReportService.selectSexGroup(baseEntity);
        }

        return new ResponseBodyApi<>(sexGroup);
    }

    @GetMapping("/persons/analysis/base_data")
    @ResponseBody
    public ResponseBodyApi<List<BizElement>> baseDataAnalysis(@RequestParam Map<String, Object> paramMap) {
        List<BizElement> baseData;

        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        baseData = bizPersonReportService.BizPersonBaseDataGroup(baseEntity);

        return new ResponseBodyApi<List<BizElement>>(baseData);
    }

    @PostMapping("/persons/analysis/age_dept")
    @ResponseBody
    public ResponseBodyApi ageDeptAnalysis(@RequestParam Map<String, Object> paramMap) {

        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        List<Map<String, Object>> deptAgeGroup = bizPersonReportService.MutDeptAgeGroup(baseEntity);

        return new ResponseBodyApi(deptAgeGroup);
    }

    @PostMapping("/persons/analysis/sex_dept")
    @ResponseBody
    public ResponseBodyApi sexDeptAnalysis(@RequestParam Map<String, Object> paramMap) {

        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        List<Map<String, Object>> deptSexGroup = bizPersonReportService.MutDeptSexGroup(baseEntity);

        return new ResponseBodyApi(deptSexGroup);
    }

    @PostMapping("/persons/analysis/edu_dept")
    @ResponseBody
    public ResponseBodyApi<List<Map<String, Object>>> eduDeptAnalysis(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        List<Map<String, Object>> deptEduGroup = bizPersonReportService.MutDeptEduGroup(baseEntity);

        return new ResponseBodyApi<>(deptEduGroup);
    }

    @PostMapping("/persons/analysis/nation_dept")
    @ResponseBody
    public ResponseBodyApi<List<Map<String, Object>>> nationDeptAnalysis(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        List<Map<String, Object>> deptNationGroup = bizPersonReportService.MutDeptNationGroup(baseEntity);

        return new ResponseBodyApi<>(deptNationGroup);
    }

    @PostMapping("/persons/analysis/orgs/levels")
    @ResponseBody
    public ResponseBodyApi<Map<String, Object>> selectOrgLevelStatistic(@RequestParam Map<String, Object> paramMap) {
        BaseEntity baseEntity = new BaseEntity();
        if (paramMap.get("deptIds") != null && paramMap.get(("deptIds")) != "") {
            baseEntity.getParams().put("deptIds",
                    Convert.toStrArray(paramMap.get("deptIds").toString()));
        }
        Map<String, Object> analysisData = bizPersonReportService.selectOrgLevelStatistic(baseEntity);
        return new ResponseBodyApi<>(analysisData);
    }

    @PostMapping("/persons/analysis/base_data2")
    @ResponseBody
    public ResponseBodyApi baseDataAnalysis2(@RequestParam Map<String, Object> paramMap) {
//        List<BizElement> baseData;

        BaseEntity baseEntity = new BaseEntity();
        getAnalysisQueryOptions(baseEntity, paramMap);

        List<Map<String, Object>> baseData = bizPersonReportService.MutDeptPersonDataBaseGroup(baseEntity);

        return new ResponseBodyApi(baseData);
    }

    @GetMapping("/persons/analysis/dept_person")
    @ResponseBody
    public ResponseBodyApi<List<BizElement>> deptPersonAnalysis(@RequestParam Map<String, Object> paramMap) {
        List<BizElement> deptPerson;

        BaseEntity baseEntity = new BaseEntity();

        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(baseEntity, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }

        deptPerson = bizPersonReportService.selectDeptPersonGroup(baseEntity);

        return new ResponseBodyApi<>(deptPerson);
    }

    // 增加接口获取职务树
    @GetMapping("/post_tree")
    @ResponseBody
    public ResponseBodyApi<List<SysPersonPost>> postTree() {
        SysPersonPost personPost = new SysPersonPost();
        personPost.setParentId("0");
        List<SysPersonPost> posts = personPostService.selectPersonPostTreeList(personPost);
        return new ResponseBodyApi<>(posts);
    }

    // 根据字典类型获取字典数据,应该是从缓存中获取
    @GetMapping("/dicts/{dictType}")
    @ResponseBody
    public ResponseBodyApi<List<SysDictData>> dictData(@PathVariable("dictType") String dictType) {
        List<SysDictData> dictData = dictTypeService.selectDictDataByType(dictType);
        return new ResponseBodyApi<>(dictData);
    }

    @GetMapping("/position_adjust/proposed/list")
    @ResponseBody
    public ResponseBodyApi<List<BizPersonPositionAdjustProposed>> positionAdjustProposedList(HttpServletResponse response, @RequestParam Map<String, Object> paramMap){
        long deptId = -1;
        BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed = new BizPersonPositionAdjustProposed();
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonPositionAdjustProposed.getParams().put("deptIds", Arrays.asList(deptId));
        }

        if (paramMap.get("adjustTypeList") != null && paramMap.get("adjustTypeList") != "") {
            bizPersonPositionAdjustProposed.getParams().put("adjustTypeList", Convert.toStrArray(paramMap.get("adjustTypeList").toString()));
        }

        String meetingBeginTime = Convert.toStr(paramMap.get("meetingBeginTime"), "");
        String meetingEndTime = Convert.toStr(paramMap.get("meetingEndTime"), "");
        if (StringUtils.isNotEmpty(meetingBeginTime) && StringUtils.isNotEmpty(meetingEndTime)) {
            bizPersonPositionAdjustProposed.getParams().put("meetingBeginTime", meetingBeginTime);
            bizPersonPositionAdjustProposed.getParams().put("meetingEndTime", meetingEndTime);
        }

        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonPositionAdjustProposed, getSysUser(), "dp", "", "", getUserDeptId());
        }
        startPage();
        List<BizPersonPositionAdjustProposed> list = bizStatisticPositionAdjustService.selectBizPersonPositionAdjustProposed(bizPersonPositionAdjustProposed);
        ResponseBodyApi<List<BizPersonPositionAdjustProposed>> rsBody = new ResponseBodyApi<>(list);
        rsBody.setMessage("OK");
        return rsBody;
    }
}