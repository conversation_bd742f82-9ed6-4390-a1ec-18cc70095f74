---
description: 项目结构规则
globs: 
alwaysApply: false
---
# MetaOrg 项目结构规则

## 项目概述
MetaOrg 是一个基于 Spring Boot 2.1.1 的干部人员管理系统，采用多模块 Maven 项目结构。

## 核心模块结构
- **metaorg-admin**: 主应用模块，包含控制器和Web界面
  - 路径: [MetaOrg/metaorg-admin](mdc:MetaOrg/metaorg-admin)
  - 包含: Spring Boot 启动类、Web控制器、前端页面模板
- **metaorg-common**: 公共模块
  - 路径: [MetaOrg/metaorg-common](mdc:MetaOrg/metaorg-common)
  - 包含: 通用工具类、异常处理、常量定义
- **metaorg-framework**: 框架模块
  - 路径: [MetaOrg/metaorg-framework](mdc:MetaOrg/metaorg-framework)
  - 包含: Shiro安全框架、JWT认证、拦截器
- **metaorg-system**: 系统管理模块
  - 路径: [MetaOrg/metaorg-system](mdc:MetaOrg/metaorg-system)
  - 包含: 用户、角色、权限管理
- **metaorg-library**: 业务模块
  - 路径: [MetaOrg/metaorg-library](mdc:MetaOrg/metaorg-library)
  - 包含: 人员档案、考核、培训等业务功能
- **metaorg-quartz**: 定时任务模块
  - 路径: [MetaOrg/metaorg-quartz](mdc:MetaOrg/metaorg-quartz)
  - 包含: Quartz 定时任务管理

## 技术栈
- **后端**: Spring Boot 2.1.1, MyBatis, Shiro, Quartz
- **前端**: Thymeleaf, Bootstrap, jQuery, Layer
- **数据库**: PostgreSQL (GaussDB)
- **构建工具**: Maven 3.x
- **JDK版本**: Java 8+

## 重要配置文件
- 主POM文件: [pom.xml](mdc:MetaOrg/pom.xml)
- 应用配置: [MetaOrg/metaorg-admin/src/main/resources/application.yml](mdc:MetaOrg/metaorg-admin/src/main/resources/application.yml)
- 数据库配置在 application.yml 中

## 包命名规范
所有 Java 类都在 `com.metaorg` 包下：
- `com.metaorg.common.*` - 公共组件
- `com.metaorg.framework.*` - 框架组件  
- `com.metaorg.system.*` - 系统管理
- `com.metaorg.library.*` - 业务功能
- `com.metaorg.web.controller.*` - Web控制器
