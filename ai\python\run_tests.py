#!/usr/bin/env python3
"""
测试运行脚本
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=False,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode})")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 新疆金融监管局处罚信息爬虫 - 测试套件")
    print("=" * 60)
    
    # 测试列表
    tests = [
        {
            "cmd": "uv run python -c \"import sys; sys.path.insert(0, 'src'); from nfra_crawler.models.database import CrawlRequest; print('✅ 模块导入成功')\"",
            "desc": "模块导入测试"
        },
        {
            "cmd": "uv run python -m pytest tests/test_integration_simple.py::TestSimpleIntegration::test_environment_setup -v -s",
            "desc": "环境设置测试"
        },
        {
            "cmd": "uv run python -m pytest tests/test_integration_simple.py::TestSimpleIntegration::test_data_models -v -s",
            "desc": "数据模型测试"
        },
        {
            "cmd": "uv run python -m pytest tests/test_integration_simple.py::TestSimpleIntegration::test_json_processing -v -s",
            "desc": "JSON处理测试"
        },
        {
            "cmd": "uv run python -m pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_database_connection -v -s",
            "desc": "数据库连接测试（可能跳过）"
        },
        {
            "cmd": "uv run python -m pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_rabbitmq_connection -v -s",
            "desc": "RabbitMQ连接测试（可能跳过）"
        }
    ]
    
    # 运行测试
    success_count = 0
    total_count = len(tests)
    
    for test in tests:
        if run_command(test["cmd"], test["desc"]):
            success_count += 1
    
    # 显示总结
    print(f"\n{'='*60}")
    print(f"📊 测试总结")
    print(f"{'='*60}")
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！")
    else:
        print(f"\n⚠️ 有 {total_count - success_count} 个测试失败或跳过")
    
    print("\n📝 说明:")
    print("- 数据库和RabbitMQ连接测试可能会跳过（如果服务未运行）")
    print("- 这是正常的，表示测试框架工作正常")
    print("- 要运行完整的集成测试，请启动PostgreSQL和RabbitMQ服务")
    
    return success_count >= 4  # 至少4个基础测试通过

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
