package com.metaorg.web.controller.library;

import java.util.List;

import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.system.service.ISysDeptService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.library.domain.BizLeadership;
import com.metaorg.library.service.IBizLeadershipService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;

/**
 * 领导班子Controller
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Controller
@RequestMapping("/library/leadership")
public class BizLeadershipController extends BaseController {
    private String prefix = "library/leadership";

    @Autowired
    private IBizLeadershipService bizLeadershipService;

    @Autowired
    private ISysDeptService deptService;

    // @RequiresPermissions("library:leadership:view")
    @GetMapping()
    public String leadership(ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if (StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        return prefix + "/leadership";
    }

    /**
     * 查询领导班子列表
     */
    // @RequiresPermissions("library:leadership:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BizLeadership bizLeadership) {
        startPage();
        List<BizLeadership> list = bizLeadershipService.selectBizLeadershipList(bizLeadership);
        return getDataTable(list);
    }

    /**
     * 导出领导班子列表
     */
    @RequiresPermissions("library:leadership:export")
    @Log(title = "领导班子", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizLeadership bizLeadership) {
        List<BizLeadership> list = bizLeadershipService.selectBizLeadershipList(bizLeadership);
        ExcelUtil<BizLeadership> util = new ExcelUtil<BizLeadership>(BizLeadership.class);
        return util.exportExcel(list, "领导班子数据");
    }

    /**
     * 新增领导班子
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存领导班子
     */
    @RequiresPermissions("library:leadership:add")
    @Log(title = "领导班子", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizLeadership bizLeadership) {
        return toAjax(bizLeadershipService.insertBizLeadership(bizLeadership));
    }

    @RequiresPermissions("library:leadership:add")
    @Log(title = "领导班子", businessType = BusinessType.INSERT)
    @PostMapping("/addPersons")
    @ResponseBody
    public AjaxResult addPersons(Long deptId, String personIds) throws Exception {
        return toAjax(bizLeadershipService.insertBizLeaderships(deptId, personIds));
    }

    /**
     * 修改领导班子
     */
    @RequiresPermissions("library:leadership:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizLeadership bizLeadership = bizLeadershipService.selectBizLeadershipById(id);
        mmap.put("bizLeadership", bizLeadership);
        return prefix + "/edit";
    }

    /**
     * 修改保存领导班子
     */
    @RequiresPermissions("library:leadership:edit")
    @Log(title = "领导班子", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizLeadership bizLeadership) {
        return toAjax(bizLeadershipService.updateBizLeadership(bizLeadership));
    }

    /**
     * 排序地址与通信
     */
    @RequiresPermissions("library:leadership:edit")
    @Log(title = "领导班子", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++) {
            BizLeadership bizLeadership = bizLeadershipService.selectBizLeadershipById(idsArray[i]);
            if (bizLeadership == null) {
                continue;
            }

            bizLeadership.setOrderNum(Convert.toLong(i + 1));
            bizLeadershipService.updateBizLeadership(bizLeadership);
        }
        return success();
    }

    /**
     * 删除领导班子
     */
    @RequiresPermissions("library:leadership:remove")
    @Log(title = "领导班子", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizLeadershipService.deleteBizLeadershipByIds(ids));
    }
}
