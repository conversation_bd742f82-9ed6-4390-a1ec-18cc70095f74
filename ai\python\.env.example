# RabbitMQ 配置
RABBITMQ_HOST=***********
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=
RABBITMQ_PWD=
RABBITMQ_VIRTUAL_HOST=/
RABBITMQ_QUEUE_NAME=metaorg.sync.punish.crawler.request.queue

# 数据库配置 (GaussDB/PostgreSQL)
DATABASE_HOST=***********
DATABASE_PORT=15433
DATABASE_NAME=
DATABASE_USER=
DATABASE_PWD=
DATABASE_SCHEMA=public

# AI模型配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.deepseek.com
OPENAI_MODEL_NAME=deepseek-ai/DeepSeek-V3-0324

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/nfra_crawler.log

# 爬虫配置
CRAWLER_TARGET_URL=
CRAWLER_HEADLESS=true
CRAWLER_TIMEOUT=30
CRAWLER_MAX_RETRIES=3
