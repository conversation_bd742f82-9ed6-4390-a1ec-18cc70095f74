package com.metaorg.web.controller.api;

import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.ResponseBodyApi;
import com.metaorg.common.utils.AgeUtils;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = {"*"})
@RestController
@RequestMapping("/test")
public class TestController  extends BaseController {

    @GetMapping("/list")
    @ResponseBody
    public ResponseBodyApi<String> list()
    {
        AgeUtils.getAgeFromCurrentFirstDay("1979-7");
        return new ResponseBodyApi<>("list success");
//        return AjaxResult.success("list success");
    }
}
