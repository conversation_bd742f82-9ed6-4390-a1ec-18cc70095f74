package com.metaorg.web.controller.library;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.exception.ServiceException;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.utils.file.FileUtils;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.domain.BizPersonEducation;
import com.metaorg.library.domain.BizPersonPolitics;
import com.metaorg.library.service.IBizPersonEducationService;
import com.metaorg.library.service.IBizPersonPoliticsService;
import com.metaorg.library.service.IBizPersonService;
import com.metaorg.system.domain.SysPersonPost;
import com.metaorg.system.domain.SysRank;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.system.service.ISysDictDataService;
import com.metaorg.system.service.ISysPersonPostService;
import com.metaorg.system.service.ISysRankService;
import com.metaorg.utils.DataScopeUtils;
import com.metaorg.web.controller.tool.POIUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.shiro.authz.annotation.Logical;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonPosition;
import com.metaorg.library.domain.BizPersonPositionTplVM;
import com.metaorg.library.service.IBizPersonPositionService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.common.utils.AgeUtils;
import org.springframework.web.multipart.MultipartFile;
import com.metaorg.library.service.RabbitService;

/**
 * 职务职级Controller
 * 
 * <AUTHOR>
 * @date 2023-05-19
 */
@Controller
@RequestMapping("/library/position")
public class BizPersonPositionController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(BizPersonPositionController.class);

    @Autowired
    private RabbitService rabbitService;

    private String prefix = "library/position";

    @Autowired
    private IBizPersonPositionService bizPersonPositionService;

    @Autowired
    private IBizPersonService bizPersonService;

    @Autowired
    private  ISysPersonPostService personPostService;

    @Autowired
    private IBizPersonEducationService educationService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private IBizPersonPoliticsService politicsService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysRankService rankService;

    @RequiresPermissions("library:position:view")
    @GetMapping()
    public String position(ModelMap mmap)
    {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if(StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        //获取职务树列表
        SysPersonPost personPost = new SysPersonPost();
        personPost.setParentId("0");
        List<SysPersonPost> posts = personPostService.selectPersonPostTreeList(personPost);
        mmap.put("posts", posts);
        return prefix + "/position";
    }

    /**
     * 职务预警
     */
    @RequiresPermissions("library:position_warning:view")
    @GetMapping(("/position_warning"))
    public String position_warning(ModelMap mmap)
    {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if(StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        //获取职务树列表
        SysPersonPost personPost = new SysPersonPost();
        personPost.setParentId("0");
        List<SysPersonPost> posts = personPostService.selectPersonPostTreeList(personPost);
        mmap.put("posts", posts);
        return prefix + "/position_warning";
    }

    /**
     * 查询职务预警列表
     */
    @RequiresPermissions("library:position_warning:list")
    @PostMapping("/warninglist")
    @ResponseBody
    public TableDataInfo warningList(BizPerson bizPerson, @RequestParam Map<String, Object> paramMap)
    {
        long deptId = -1;
        if(paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPerson.setDeptId(deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "d", "", "", deptId);
        }
        if(bizPerson.getParams().containsKey("postids")) {
            bizPerson.getParams().put("postids", Convert.toStrArray(bizPerson.getParams().get("postids").toString()));
        }
        startPage();
        List<BizPersonPosition> list = bizPersonService.selectPositionWarningList(bizPerson);
        return getDataTable(list);
    }

    /**
     * 人员职务调动
     */
    @RequiresPermissions("library:position_transfer:view")
    @GetMapping(("/position_transfer"))
    public String position_transfer(ModelMap mmap)
    {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if(StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        //获取职务列表
        SysPersonPost post = new SysPersonPost();
        List<SysPersonPost> posts = personPostService.selectPersonPostList(post);
        List<SysPersonPost> pposts = posts.stream().filter(r -> r.getParentId().equals("0")).collect(Collectors.toList());
        List<SysPersonPost> nposts = new ArrayList<SysPersonPost>();
        for(SysPersonPost ppost : pposts){
            nposts.add(ppost);
            List<SysPersonPost> subPosts = posts.stream().filter(r -> r.getParentId().equals(ppost.getPersonPostId())).collect(Collectors.toList());
            if(subPosts.size() > 0) nposts.addAll(subPosts);
        }
        mmap.put("posts", nposts);
        return prefix + "/position_transfer";
    }

    /**
     * 查询人员职务调动列表
     */
    @RequiresPermissions("library:position_transfer:list")
    @PostMapping("/transfer_list")
    @ResponseBody
    public TableDataInfo transferList(BizPersonPosition bizPersonPosition, @RequestParam Map<String, Object> paramMap)
    {
        long deptId = -1;
        if(paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonPosition.getParams().put("deptId", deptId);
        }
        // 加个注释
        if(paramMap.get("transferDirections") != null && paramMap.get("transferDirections") != ""){
            bizPersonPosition.getParams().put("transferDirections", Convert.toStrArray(paramMap.get("transferDirections").toString()));
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonPosition, getSysUser(), "d", "", "", deptId);
        }
        startPage();
        List<BizPersonPosition> list = bizPersonPositionService.selectBizPersonPositionTransferList(bizPersonPosition);
        return getDataTable(list);
    }

    /**
     * 查询职务职级列表
     */
    @RequiresPermissions(value={"library:position:list","library:person:view"},logical= Logical.OR)
    @PostMapping({"/list/{personId}","/list"})
    @ResponseBody
    public TableDataInfo list(BizPersonPosition bizPersonPosition, @RequestParam Map<String, Object> paramMap)
    {
        long deptId = -1;
        if(paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonPosition.getParams().put("deptId", deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonPosition, getSysUser(), "vpd", "", "", deptId);
        }
        if(bizPersonPosition.getParams().containsKey("postids")) {
            bizPersonPosition.getParams().put("postids", Convert.toStrArray(bizPersonPosition.getParams().get("postids").toString()));
        }
        startPage();
        List<BizPersonPosition> list = bizPersonPositionService.selectBizPersonPositionList(bizPersonPosition);
        return getDataTable(list);
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename)
    {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists())
        {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

    /**
     * 导出职务职级列表
     */
    @RequiresPermissions("library:position:export")
    @Log(title = "职务（职级）信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonPosition bizPersonPosition)
    {
        List<BizPersonPosition> list = bizPersonPositionService.selectBizPersonPositionList(bizPersonPosition);
        ExcelUtil<BizPersonPosition> util = new ExcelUtil<BizPersonPosition>(BizPersonPosition.class);
        return util.exportExcel(list, "职务职级数据");
    }

    /**
     * 导出任职预警列表
     */
    @RequiresPermissions("library:position_warning:export")
    @Log(title = "职务（职级）信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export/warning")
    @ResponseBody
    public AjaxResult exportWarning(BizPerson bizPerson, @RequestParam Map<String, Object> paramMap)
    {
        long deptId = -1;
        if(paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPerson.setDeptId(deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPerson, getSysUser(), "d", "", "", deptId);
        }
        if(bizPerson.getParams().containsKey("postids")) {
            bizPerson.getParams().put("postids", Convert.toStrArray(bizPerson.getParams().get("postids").toString()));
        }
        bizPerson.getParams().put("orderBy", "a.position_day asc,a.id desc");
        List<BizPersonPosition> list = bizPersonService.selectPositionWarningList(bizPerson);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "超任期人员_" + System.currentTimeMillis() +".xls";

        try
        {
            File file = new File("./config/template/rzyujing_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            //通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            for (int i = 0; i < list.size(); i++) {
                BizPersonPosition personPosition = list.get(i);
                BizPerson person = personPosition.getPerson();
                SysDept dept = person.getDept();

                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                row.getCell(0).setCellValue(i + 1);  //序号

                String deptLevelName = "";
                String deptLevelCode = dept.getDeptLevel();
                if(StringUtils.isNotEmpty(deptLevelCode)){
                    deptLevelName = dictDataService.selectDictLabel("sys_dept_level", deptLevelCode, "");
                }
                row.getCell(1).setCellValue(deptLevelName);  //行社分级
                row.getCell(2).setCellValue(dept.getDeptName());  //行社名称
                row.getCell(3).setCellValue(person.getName());   //姓名

                String xzpostName = personPosition.getPositionName();
                String dnpostName = personPosition.getPartyPositionName();
                String postRankName = personPosition.getPostRankName();
                String postFullName = StringUtils.isNotEmpty(xzpostName) ? xzpostName : "";
                if(StringUtils.isNotEmpty(dnpostName)){
                    postFullName += StringUtils.isNotEmpty(postFullName)? "、" + dnpostName : dnpostName;
                }
                if(StringUtils.isNotEmpty(postRankName)){
                    postFullName += "（" + postRankName + "）";
                }
                postFullName.replace(",", "、");
                row.getCell(4).setCellValue(postFullName);   //职务
                row.getCell(5).setCellValue(person.getSex());   //性别
                row.getCell(6).setCellValue(person.getNation());   ///民族
                //出生年月
                String bitthDay = person.getBirthday();
                row.getCell(7).setCellValue(StringUtils.isEmpty(bitthDay) ? "" : bitthDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(8).setCellValue(person.getAge().replace("岁", ""));   //年龄
                row.getCell(9).setCellValue(person.getNativePlace());   //籍贯
                row.getCell(10).setCellValue(person.getBirthPlace());   //出生地

                BizPersonEducation education = new BizPersonEducation();  //学历对象
                education.setPersonId(person.getId());
                List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);
                BizPersonEducation highestEducation = null; //最高学历
                String ZuiGaoXueLi = "";  //最高学历
                // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum)); //正序
                if(educations != null) {
                    for (BizPersonEducation item : educations) {
                        //根据是否最高学历标识获取最高学历对象
                        if(item.getHighestDegreeFlag().equalsIgnoreCase("Y")) {
                            highestEducation = item;
                        }
                    }
                    //如果没有标识最高学历获取最后一个
                    if(highestEducation == null && educations.size() > 0) {
                        highestEducation = educations.get(educations.size() - 1);
                    }
                    if(highestEducation != null) {
                        ZuiGaoXueLi = highestEducation.getEduName();
                    }
                }
                row.getCell(11).setCellValue(ZuiGaoXueLi);   //最高学历
                //通过政治情况获取入党时间
                BizPersonPolitics politics = new BizPersonPolitics();
                politics.setPersonId(person.getId());
                List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);
                BizPersonPolitics bizPersonPolitics = null;
                if(politicsList != null) {
                    bizPersonPolitics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
                        @Override
                        public boolean test(BizPersonPolitics bizPersonPolitics) {
                            return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
                        }
                    }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
                        @Override
                        public BizPersonPolitics get() {
                            return new BizPersonPolitics();
                        }
                    });
                }
                //入党时间
                if(bizPersonPolitics != null && bizPersonPolitics.getJoinPartyDay() != null){
                    row.getCell(12).setCellValue(bizPersonPolitics.getJoinPartyDay().replace("-", ".").replace("Invalid date", ""));
                }else{
                    row.getCell(12).setCellValue("");
                }
                //参加工作时间
                row.getCell(13).setCellValue(person.getJobDay() == null ? "" : person.getJobDay().replace("-", ".").replace("Invalid date", ""));
                //任职时间
                String positionDay = personPosition.getPositionDay();
                row.getCell(14).setCellValue(positionDay.replace("-", "."));
                //任同职级时间
                String sameRankDay = personPosition.getSameRankDay();
                row.getCell(15).setCellValue(StringUtils.isEmpty(sameRankDay) ? "" : sameRankDay.replace("-", ".").replace("Invalid date", ""));
                //任同岗位时间
                String samePositionDay = personPosition.getSamePositionDay();
                row.getCell(16).setCellValue(StringUtils.isEmpty(samePositionDay) ? "" : samePositionDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(17).setCellValue(person.getQualificationJob());  //专业技术职务
                if(highestEducation != null){
                    //毕业日期
                    String BiYeShiJian = highestEducation.getGraduationDay().replace("-", ".").replace("Invalid date", "");
                    row.getCell(18).setCellValue(BiYeShiJian);  //毕业时间
                    row.getCell(19).setCellValue(highestEducation.getEduSchool());  //毕业院校
                    row.getCell(20).setCellValue(highestEducation.getMajorName());  //专业
                    row.getCell(21).setCellValue(highestEducation.getDegreeCode());  //学位
                }else{
                    row.getCell(18).setCellValue("");  //毕业时间
                    row.getCell(19).setCellValue("");  //毕业院校
                    row.getCell(20).setCellValue("");  //专业
                    row.getCell(21).setCellValue("");  //学位
                }
                row.getCell(22).setCellValue(person.getCitizenId());  //身份证号码
                String samePositionYear = AgeUtils.getAgeFromCurrentFirstDay(samePositionDay);
                row.getCell(23).setCellValue(samePositionYear.replace("岁", ""));  //同岗位任职时间(年)
            }

            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        }
        catch (Exception e)
        {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        }
        finally
        {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导出人员职务调动列表
     */
    @RequiresPermissions("library:position_transfer:export")
    @Log(title = "职务（职级）信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export/transfer")
    @ResponseBody
    public AjaxResult exportTransfer(BizPersonPosition bizPersonPosition, @RequestParam Map<String, Object> paramMap)
    {
        long deptId = -1;
        if(paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonPosition.getParams().put("deptId", deptId);
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonPosition, getSysUser(), "d", "", "", deptId);
        }
        bizPersonPosition.getParams().put("orderBy", "a.position_day desc,a.id desc");
        List<BizPersonPosition> list = bizPersonPositionService.selectBizPersonPositionTransferList(bizPersonPosition);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "人员调动_" + System.currentTimeMillis() +".xls";

        try
        {
            File file = new File("./config/template/rzdiaodong_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            //通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            for(int i = 0; i < list.size(); i++) {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                row.getCell(0).setCellValue(i + 1);  //序号
                row.getCell(1).setCellValue(list.get(i).getPerson().getName());  //姓名
                row.getCell(2).setCellValue(list.get(i).getOrganizationName());  //行社/部门
                row.getCell(3).setCellValue(list.get(i).getFullPositionName());   //职务
                row.getCell(4).setCellValue(list.get(i).getTransferDirection());   //调动方向
                row.getCell(5).setCellValue(list.get(i).getPerson().getSex());   //性别
                row.getCell(6).setCellValue(list.get(i).getPerson().getNation());   ///民族
                //出生年月
                String bitthDay = list.get(i).getPerson().getBirthday();
                row.getCell(7).setCellValue(StringUtils.isEmpty(bitthDay) ? "" : bitthDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(8).setCellValue(list.get(i).getPerson().getNativePlace());   //籍贯

                BizPersonEducation education = new BizPersonEducation();  //学历对象
                education.setPersonId(list.get(i).getPersonId());
                List<BizPersonEducation> educations = educationService.selectBizPersonEducationList(education);
                BizPersonEducation highestEducation = null; //最高学历
                String ZuiGaoXueLi = "";  //最高学历
                // educations.sort(Comparator.comparing(BizPersonEducation::getOrderNum)); //正序
                if(educations != null) {
                    for (BizPersonEducation item : educations) {
                        //根据是否最高学历标识获取最高学历对象
                        if(item.getHighestDegreeFlag().equalsIgnoreCase("Y")) {
                            highestEducation = item;
                        }
                    }
                    //如果没有标识最高学历获取最后一个
                    if(highestEducation == null && educations.size() > 0) {
                        highestEducation = educations.get(educations.size() - 1);
                    }
                    if(highestEducation != null) {
                        ZuiGaoXueLi = highestEducation.getEduName();
                    }
                }
                row.getCell(9).setCellValue(ZuiGaoXueLi);   //学历
                //通过政治情况获取入党时间
                BizPersonPolitics politics = new BizPersonPolitics();
                politics.setPersonId(list.get(i).getPersonId());
                List<BizPersonPolitics> politicsList = politicsService.selectBizPersonPoliticsList(politics);
                BizPersonPolitics bizPersonPolitics = null;
                if(politicsList != null) {
                    bizPersonPolitics = politicsList.stream().filter(new Predicate<BizPersonPolitics>() {
                        @Override
                        public boolean test(BizPersonPolitics bizPersonPolitics) {
                            return bizPersonPolitics.getPoliticsCategory().equals("中共党员");
                        }
                    }).findFirst().orElseGet(new Supplier<BizPersonPolitics>() {
                        @Override
                        public BizPersonPolitics get() {
                            return new BizPersonPolitics();
                        }
                    });
                }
                //入党时间
                if(bizPersonPolitics != null && bizPersonPolitics.getJoinPartyDay() != null){
                    row.getCell(10).setCellValue(bizPersonPolitics.getJoinPartyDay().replace("-", ".").replace("Invalid date", ""));
                }else{
                    row.getCell(10).setCellValue("");
                }
                //参加工作时间
                row.getCell(11).setCellValue(list.get(i).getPerson().getJobDay() == null ? "" : list.get(i).getPerson().getJobDay().replace("-", ".").replace("Invalid date", ""));
                //任职时间
                String positionDay = list.get(i).getPositionDay();
                row.getCell(12).setCellValue(positionDay.replace("-", "."));
                //任同职级时间
                String sameRankDay = list.get(i).getSameRankDay();
                row.getCell(13).setCellValue(StringUtils.isEmpty(sameRankDay) ? "" : sameRankDay.replace("-", ".").replace("Invalid date", ""));
                row.getCell(14).setCellValue(list.get(i).getPerson().getQualificationJob());  //专业技术职务
                if(highestEducation != null){
                    //毕业日期
                    String BiYeShiJian = highestEducation.getGraduationDay().replace("-", ".").replace("Invalid date", "");
                    row.getCell(15).setCellValue(BiYeShiJian);  //毕业时间
                    row.getCell(16).setCellValue(highestEducation.getEduSchool());  //毕业院校
                    row.getCell(17).setCellValue(highestEducation.getMajorName());  //专业
                    row.getCell(18).setCellValue(highestEducation.getDegreeCode());  //学位
                }else{
                    row.getCell(15).setCellValue("");  //毕业时间
                    row.getCell(16).setCellValue("");  //毕业院校
                    row.getCell(17).setCellValue("");  //专业
                    row.getCell(18).setCellValue("");  //学位
                }
                row.getCell(19).setCellValue(list.get(i).getOldOrganizationName());  //原行社/部门
                row.getCell(20).setCellValue(list.get(i).getOldFullPositionName());  //原职务
            }

            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        }
        catch (Exception e)
        {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        }
        finally
        {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 新增职务职级
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        BizPerson bizPerson = bizPersonService.selectBizPersonById(personId);
        List<SysPersonPost> xzposts = new ArrayList<SysPersonPost>();
        List<SysPersonPost> dnposts = new ArrayList<SysPersonPost>();
        //获取行政职务、党内职务列表
        SysPersonPost personPost = new SysPersonPost();
        personPost.setParentId("0");
        List<SysPersonPost> posts = personPostService.selectPersonPostTreeList(personPost);
        for(SysPersonPost post : posts){
            if(post.getPostCode().equals("xingzheng")){
                xzposts.addAll(post.getChilds());
            }else if(post.getPostCode().equals("dangnei")){
                dnposts.addAll(post.getChilds());
            }
        }

        //获取上一条任职记录
        BizPersonPosition searchBizPersonPosition = new BizPersonPosition();
        searchBizPersonPosition.setPersonId(personId);
        BizPersonPosition preBizPosition = bizPersonPositionService.selectBizPersonPositionPreOne(searchBizPersonPosition);

        mmap.put("personId", personId);
        mmap.put("bizPerson", bizPerson);
        mmap.put("preBizPosition", preBizPosition);
        mmap.put("xzposts", xzposts);
        mmap.put("dnposts", dnposts);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存职务职级
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "职务（职级）信息", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "职务（职级）信息", tableName = PersonTable.BIZ_PERSON_POSITION, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonPosition bizPersonPosition)
    {
        bizPersonPosition.setCreateBy(getLoginName());
        if(StringUtils.isEmpty(bizPersonPosition.getOldOrganizationCode()) || StringUtils.isEmpty(bizPersonPosition.getOldOrganizationName())) {
            bizPersonPosition.setOldFullPositionName("");
            bizPersonPosition.setOldOrganizationCode("");
        }

        return toAjax(bizPersonPositionService.insertBizPersonPosition(bizPersonPosition, true));
    }

    /**
     * 修改职务职级
     */
    @RequiresPermissions(value={"library:position:edit","library:person:edit"},logical= Logical.OR)
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonPosition bizPersonPosition = bizPersonPositionService.selectBizPersonPositionById(id);

        List<SysPersonPost> posts = bizPersonPositionService.selectPostByPersonPositionId(id);
        List<SysPersonPost> xzposts = new ArrayList<SysPersonPost>();
        List<SysPersonPost> dnposts = new ArrayList<SysPersonPost>();

        for(SysPersonPost post : posts){
            if(post.getPostCode().equals("xingzheng")){
                xzposts.addAll(post.getChilds());
            }else if(post.getPostCode().equals("dangnei")){
                dnposts.addAll(post.getChilds());
            }
        }

        mmap.put("bizPersonPosition", bizPersonPosition);
        mmap.put("xzposts", xzposts);
        mmap.put("dnposts", dnposts);

        return prefix + "/edit";
    }

    /**
     * 修改保存职务职级
     */
    @RequiresPermissions(value={"library:position:edit","library:person:edit"},logical= Logical.OR)
    @Log(title = "职务（职级）信息", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "职务（职级）信息", tableName = PersonTable.BIZ_PERSON_POSITION, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonPosition bizPersonPosition)
    {
        bizPersonPosition.setUpdateBy(getLoginName());
        if(StringUtils.isEmpty(bizPersonPosition.getOldOrganizationCode()) || StringUtils.isEmpty(bizPersonPosition.getOldOrganizationName())){
            bizPersonPosition.setOldFullPositionName("");
            bizPersonPosition.setOldOrganizationCode("");
        }

        return toAjax(bizPersonPositionService.updateBizPersonPosition(bizPersonPosition));
    }

    /**
     * 下载模板
     */
    @SuppressWarnings("resource")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = System.currentTimeMillis() + "_人员职务职级导入模板" + ".xls";
        try {
            File file = new File("./config/template/ryzwzj_dtpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            //通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
//            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);//.getSheet("Sheet1");
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        }catch (Exception e)
        {
            log.error("导出Excel模板异常{}", e.getMessage());
            throw new UtilException("导出Excel模板失败，请联系网站管理员！");
        }
        finally
        {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 导入数据
     */
    @Log(title = "职务（职级）信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions("library:position:import")
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BizPersonPositionTplVM> util = new ExcelUtil<BizPersonPositionTplVM>(BizPersonPositionTplVM.class);
        List<BizPersonPositionTplVM> positionList = util.importExcel(file.getInputStream());
        String message = importPosition(positionList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 删除职务职级
     */
    @RequiresPermissions(value={"library:position:remove","library:person:edit"},logical= Logical.OR)
    @Log(title = "职务（职级）信息", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "职务（职级）信息", tableName = PersonTable.BIZ_PERSON_POSITION, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonPositionService.deleteBizPersonPositionByIds(ids));
    }

    /**
     * 排序职务职级
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "职务（职级）信息", businessType = BusinessType.UPDATE)
    @PostMapping( "/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids)
    {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++)
        {
            BizPersonPosition bizPersonPosition = bizPersonPositionService.selectBizPersonPositionById(idsArray[i]);
            bizPersonPosition.setOrderNum(Convert.toLong(i + 1));
            bizPersonPositionService.updateBizPersonPosition(bizPersonPosition);
        }
        return success();
    }

    private long getMaxOrderNum(String personId)
    {
        BizPersonPosition bizPersonPosition = new BizPersonPosition();
        bizPersonPosition.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonPosition> list = bizPersonPositionService.selectBizPersonPositionList(bizPersonPosition);
        if(!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }

    // 导入职务职级
    private String importPosition(List<BizPersonPositionTplVM> positionList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(positionList) || positionList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 导入数据检查，获取组织、职务、职级信息
        Boolean isValid = checkImportData(positionList);
        HashSet<String> personIdSet = new HashSet<>();  // 人员ID
        HashMap<String, Long> maxOrderNumMap = new HashMap<>();  // 人员职务职级最大序号
        if(isValid){
            for (BizPersonPositionTplVM pvm : positionList) {
                personIdSet.add(pvm.getPerson().getId());
            }
            // 根据人员ID集团获取每一个人的最大职务职级序号
            List<Map<String, Object>> postMaxOrderNumList = bizPersonPositionService.selectMaxOrderNumGroupPerson(personIdSet.toArray(new String[0]));
            for (Map<String, Object> maxOrderNum : postMaxOrderNumList) {
                maxOrderNumMap.put(maxOrderNum.get("person_id").toString(), Long.valueOf(maxOrderNum.get("order_num").toString()));
            }
        }

        // 需要更新最新职务标志的记录，逐个更新太慢，后面批量更新
        List<BizPersonPosition> needUpdateCurrentPostFlag = new ArrayList<>();

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        Map<String, Integer> successPersonMap = new HashMap<>();

        for (BizPersonPositionTplVM pvm : positionList) {
            if(!isValid){ //数据格式不正确，全部不入库
                if(StringUtils.isNotEmpty(pvm.getErrMsg())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
            }else{
                try{
                    BizPersonPosition position = new BizPersonPosition();
                    position.setCreateBy(createUserName);
                    position.setCreateById(createUserId);
                    position.setUpdateBy(createUserName);
                    position.setUpdateById(createUserId);
                    position.setCreateTime(currentDate);
                    position.setUpdateTime(currentDate);

                    // 历史任职情况处理
                    if(StringUtils.isNotEmpty(pvm.getOldOrganizationCode())){
                        position.setOldFullPositionName(pvm.getOldFullPositionName());
                        position.setOldOrganizationCode(pvm.getOldOrganizationCode());
                        position.setOldOrganizationName(pvm.getOldOrganizationName());
                    }else{
                        position.setOldFullPositionName("");
                        position.setOldOrganizationCode("");
                        position.setOldOrganizationName("");
                        if(StringUtils.isNotEmpty(pvm.getOldFullPositionName())){
                            position.setOldFullPositionName(pvm.getOldFullPositionName());
                        }
                    }

                    position.setPersonId(pvm.getPerson().getId());  // 人员ID

                    // 任职记录序号
                    if(!maxOrderNumMap.containsKey(position.getPersonId())){
                        maxOrderNumMap.put(position.getPersonId(), 1L);
                    }
                    position.setOrderNum(maxOrderNumMap.get(position.getPersonId()));
                    maxOrderNumMap.put(position.getPersonId(), maxOrderNumMap.get(position.getPersonId()) + 1);

                    position.setOrganizationCode(pvm.getDept().getDeptId().toString());  // 任职机构ID
                    position.setOrganizationName(pvm.getDeptName());  // 任职机构名称
                    position.setPositionDay(pvm.getPositionDay());
                    position.setPositionCode(pvm.getPositionCode());
                    position.setPositionName(pvm.getPositionName());
                    position.setPartyPositionCode(pvm.getPartyPositionCode());
                    position.setPartyPositionName(pvm.getPartyPositionName());
                    position.setPostRankCode(pvm.getPostRankCode());
                    position.setPostRankName(pvm.getPostRankName());
                    position.setSamePositionDay(pvm.getSamePositionDay());
                    position.setsameRankDay(pvm.getSameRankDay());
                    if(!pvm.getCurrentPost().equals("Y")){
                        position.setCurrentPost("N");
                    }else{
                        position.setCurrentPost("Y");
                    }

                    String adjustTags = pvm.getAdjustTags();
                    if (StringUtils.isNotEmpty(adjustTags)) {
                        adjustTags = adjustTags.replaceAll("，", ",");
                        String[] adjustTagArray = Arrays.stream(adjustTags.split(",")).map(String::trim).toArray(String[]::new);
                        position.setAdjustTags(adjustTagArray);
                    }

                    // 插入职务职级信息
                    int result = bizPersonPositionService.insertBizPersonPosition(position, false);
                    if(result > 0){
                        if(position.getCurrentPost().equals("Y")){
                            needUpdateCurrentPostFlag.add(position);
                        }
                        successNum++;
                        successPersonMap.put(position.getPersonId(), successPersonMap.getOrDefault(position.getPersonId(), 0) + 1);
                    }else{
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、姓名：" + pvm.getPersonName()  + "导入失败：";
                        failureMsg.append(msg + "数据入库失败");
                    }
                }catch (Exception e) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、姓名：" + pvm.getPersonName()  + "导入失败：";
                    String exMsg = e.getMessage();
                    if(StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException){
                        exMsg = ((InvocationTargetException)e).getTargetException().getMessage();
                    }
                    if(StringUtils.isEmpty(exMsg)){
                        exMsg = "数据入库失败";
                    }
                    failureMsg.append(msg + exMsg);
                }
            }
        }

        // 更新当前最新标识
        if(needUpdateCurrentPostFlag.size() > 0){
            importDataCurrentPostFlagUpdate(needUpdateCurrentPostFlag);
        }

        if (failureNum > 0)
        {
            if(!isValid){
                failureMsg.insert(0, "很抱歉，数据校验失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            }else{
                failureMsg.insert(0, "总共：" + positionList.size() + "，成功：" + successNum + "，失败：" + failureNum + "，错误如下：");
            }
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条。");
        }

        successPersonMap.forEach((personId, count) -> {
            String operationModule = "职务（职级）信息";
            String operationType = PersonOperationType.IMPORT.getValue();
            String tableName = PersonTable.BIZ_PERSON_POSITION.getValue();
            String remark = createUserName + "导入了" + count + "条" + "\"" + operationModule + "\"信息。";
            rabbitService.sendPersonOperationLog(personId, operationType, operationModule, tableName, null, remark, createUserId, createUserName, null);
        });

        return successMsg.toString();
    }

    // 导入数据检查
    private Boolean checkImportData(List<BizPersonPositionTplVM> positionList){
        Boolean isOk = true;
        //获取当前用户有权限的部门（有数据权限控制，用于任职部门）
        HashMap<String, SysDept> deptMap = new HashMap<>();
        SysDept deptSearch = new SysDept();
        List<SysDept> deptList = deptService.selectDeptList(deptSearch);
        for(SysDept dept : deptList){
            deptMap.put(dept.getDeptName(), dept);
        }

        //获取所有的的部门（无数据权限控制，用于原任职部门）
        HashMap<String, SysDept> allDeptMap = new HashMap<>();
        SysDept allDeptSearch = new SysDept();
        List<SysDept> allDeptList = deptService.selectDeptListNoDataScope(allDeptSearch);
        for(SysDept dept : allDeptList){
            allDeptMap.put(dept.getDeptName(), dept);
        }

        // 所有行政职务及党内职务
        HashMap<String, SysPersonPost> xzpostMap = new HashMap<>();
        HashMap<String, SysPersonPost> dnpostMap = new HashMap<>();
        //获取职务树列表
        SysPersonPost personPostSearch = new SysPersonPost();
        personPostSearch.setParentId("0");
        List<SysPersonPost> posts = personPostService.selectPersonPostTreeList(personPostSearch);
        for(SysPersonPost post : posts){
            if(post.getPostCode().equals("xingzheng")){
                for(SysPersonPost subPost : post.getChilds()){
                    xzpostMap.put(subPost.getPostName(), subPost);
                }
            }else if(post.getPostCode().equals("dangnei")){
                for(SysPersonPost subPost : post.getChilds()){
                    dnpostMap.put(subPost.getPostName(), subPost);
                }
            }
        }

        // 获取所有职级
        HashMap<String, SysRank> rankMap = new HashMap<>();
        SysRank rankSearch = new SysRank();
        List<SysRank> listRank = rankService.selectRankList(rankSearch);
        for(SysRank rank : listRank){
            rankMap.put(rank.getRankName(), rank);
        }

        //根据身份证获取人员信息
        HashMap<String, BizPerson> personMap = new HashMap<>();
        BizPerson bizPersonSearch = new BizPerson();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonSearch, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }
        HashSet<String> citizenIdSet = new HashSet<>();
        for (BizPersonPositionTplVM pvm : positionList) {
            if(StringUtils.isNotEmpty(pvm.getCitizenId())){
                citizenIdSet.add(pvm.getCitizenId());
            }
        }
        bizPersonSearch.getParams().put("citizenIds", citizenIdSet.toArray());
        // 数据范围需要加上现任机构的
        List<BizPerson> personList = bizPersonService.selectBizPersonListByCitizenIds(bizPersonSearch);
        for (BizPerson person : personList) {
            personMap.put(person.getCitizenId(), person);
        }

        for (BizPersonPositionTplVM pvm : positionList) {
            String preMsg = "姓名：" + pvm.getPersonName() + " ";
            try {
                ArrayList<String> emptyMsgList = new ArrayList<>();
                if (StringUtils.isEmpty(pvm.getDeptName())) emptyMsgList.add("任职行社/部门");
                if (StringUtils.isEmpty(pvm.getPersonName())) emptyMsgList.add("姓名");
                if (StringUtils.isEmpty(pvm.getCitizenId())) emptyMsgList.add("身份证");
                if (StringUtils.isEmpty(pvm.getCurrentPost())) emptyMsgList.add("是否现任职务");
                if (StringUtils.isEmpty(pvm.getPostRankName())) emptyMsgList.add("级别");
                if (StringUtils.isEmpty(pvm.getPositionDay())) emptyMsgList.add("任职时间");
                if (StringUtils.isEmpty(pvm.getSameRankDay())) emptyMsgList.add("任同职级时间");
                if (StringUtils.isEmpty(pvm.getSamePositionDay())) emptyMsgList.add("任同岗位时间");
                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    pvm.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }
                //任职时间、任同职级时间、任同岗位时间校验
                Date positionDay = DateUtils.dateTime("yyyy-MM", pvm.getPositionDay());
                Date sameRankDay = DateUtils.dateTime("yyyy-MM", pvm.getSameRankDay());
                Date samePositionDay = DateUtils.dateTime("yyyy-MM", pvm.getSamePositionDay());
                if(positionDay == null){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "任职时间格式不正确");
                    continue;
                }
                if(sameRankDay == null){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "任同职级时间格式不正确");
                    continue;
                }
                if(samePositionDay == null){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "任同岗位时间格式不正确");
                    continue;
                }
                pvm.setPositionDay(DateUtils.parseDateToStr("yyyy-MM", positionDay));
                pvm.setSameRankDay(DateUtils.parseDateToStr("yyyy-MM", sameRankDay));
                pvm.setSamePositionDay(DateUtils.parseDateToStr("yyyy-MM", samePositionDay));

                //任职行社/部门检查
                if(!deptMap.containsKey(pvm.getDeptName())){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "任职行社/部门\"" + pvm.getDeptName() + "\"不存在");
                    continue;
                }
                pvm.setDept(deptMap.get(pvm.getDeptName()));
                /*
                if(StringUtils.isEmpty(pvm.getPositionName()) && StringUtils.isEmpty(pvm.getPartyPositionName())){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "行政职务、党内职务至少填写一项");
                    continue;
                }*/
                //党内职务检查
                String[] partyNames = pvm.getPartyPositionName().split("、");
                String partyPositionIds = "";
                Boolean isPartyPosition = true;
                for(int i=0; i < partyNames.length; i++){
                    if(!dnpostMap.containsKey(partyNames[i])){
                        isPartyPosition = false;
                        break;
                    }
                    partyPositionIds += dnpostMap.get(partyNames[i]).getPersonPostId() + ",";
                }
                if(StringUtils.isNotEmpty(pvm.getPartyPositionName()) && !isPartyPosition){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "党内职务\"" + pvm.getPartyPositionName() + "\"不存在");
                    continue;
                }
                if(isPartyPosition) {
                    pvm.setPartyPositionCode(StringUtils.lastStringDel(partyPositionIds, ","));
                }else{
                    pvm.setPartyPositionCode("");
                }
                pvm.setPartyPositionName(pvm.getPartyPositionName().replaceAll("、", ","));
                //行政职务检查
                String[] positionNames = pvm.getPositionName().split("、");

                String positionIds = "";
                Boolean isPosition = true;
                for(int i=0; i < positionNames.length; i++){
                    if(!xzpostMap.containsKey(positionNames[i])){
                        isPosition = false;
                        break;
                    }
                    positionIds += xzpostMap.get(positionNames[i]).getPersonPostId() + ",";
                }
                if(StringUtils.isNotEmpty(pvm.getPositionName()) && !isPosition){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "行政职务\"" + pvm.getPositionName() + "\"不存在");
                    continue;
                }
                if(isPosition) {
                    pvm.setPositionCode(StringUtils.lastStringDel(positionIds, ","));
                }else{
                    pvm.setPositionCode("");
                }

                //职级检查
                if(!rankMap.containsKey(pvm.getPostRankName())){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "级别\"" + pvm.getPostRankName() + "\"不存在");
                    continue;
                }
                pvm.setPostRankCode(rankMap.get(pvm.getPostRankName()).getRankId());
                //原任职行社/部门检查
                if(StringUtils.isNotEmpty(pvm.getOldOrganizationName())){
                    if(!allDeptMap.containsKey(pvm.getOldOrganizationName())){
                        isOk = false;
                        pvm.setErrMsg(preMsg + "原任职行社/部门\"" + pvm.getOldOrganizationName() + "\"不存在");
                        continue;
                    }
                    pvm.setOldOrganizationCode(allDeptMap.get(pvm.getOldOrganizationName()).getDeptId().toString());
                }
                //检查人员信息
                if(!personMap.containsKey(pvm.getCitizenId())){
                    isOk = false;
                    pvm.setErrMsg(preMsg + "人员信息不存在");
                    continue;
                }
                pvm.setPerson(personMap.get(pvm.getCitizenId()));
            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if(StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException){
                    exMsg = ((InvocationTargetException)e).getTargetException().getMessage();
                }
                if(StringUtils.isEmpty(exMsg)){
                    exMsg = "数据校验失败";
                }
                pvm.setErrMsg(msg + exMsg);
            }
        }
        return isOk;
    }

    // 导入数据最新职务标志更新处理
    private void importDataCurrentPostFlagUpdate(List<BizPersonPosition> list){
        //遍历list，然后根据BizPersonPosition、getOrganizationCode的值对list进行分类存储
        Map<String, List<String>> mapPeronIds = new HashMap<>();
        Map<String, String> mapPositionIds = new HashMap<>();
        try{
            if(list == null || list.size() == 0){
                return;
            }
            for(BizPersonPosition p : list){
                String orgCode = p.getOrganizationCode();
                if(!mapPeronIds.containsKey(orgCode)){
                    mapPeronIds.put(orgCode, new ArrayList<String>());
                }
                if(!mapPeronIds.get(orgCode).contains(p.getPersonId())){
                    mapPeronIds.get(orgCode).add(p.getPersonId());
                }
    
                //如果旧任职机构与新任职机构不同，那么需要将旧任职机构的最新职务标志更新为N
                String oldOrgCode = p.getOldOrganizationCode(); 
                if(StringUtils.isNotEmpty(oldOrgCode) && !oldOrgCode.equals(orgCode)){
                    if(!mapPeronIds.containsKey(oldOrgCode)){
                        mapPeronIds.put(oldOrgCode, new ArrayList<String>());
                    }
                    if(!mapPeronIds.get(oldOrgCode).contains(p.getPersonId())){
                        mapPeronIds.get(oldOrgCode).add(p.getPersonId());
                    }
                }
                // 一个人在一个组织下有而只能有一个最新职务
                String orgPostKey = orgCode + "_" + p.getPersonId();
                if(!mapPositionIds.containsKey(orgPostKey)){
                    mapPositionIds.put(orgPostKey, p.getId());
                }
            }
            //遍历mapPeronIds，按照组织依次将最新职务标志更新为N
            for(String orgCode : mapPeronIds.keySet()){
                bizPersonPositionService.updateCurrentPostNByPersonIds(mapPeronIds.get(orgCode).toArray(new String[0]), orgCode);
            }
            // 将mapPositionIds中的最新职务标志更新为Y
            if(mapPositionIds.size() > 0){
                bizPersonPositionService.updateCurrentPostYByIds(mapPositionIds.values().toArray(new String[0]));
            }
        }catch (Exception e){
            return;
        }
    }
}
