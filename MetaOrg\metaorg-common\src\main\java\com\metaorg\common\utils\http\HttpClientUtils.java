package com.metaorg.common.utils.http;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 统一HTTP工具类 - 基于Apache HttpClient
 * 
 * 功能特性:
 * - 支持GET/POST请求（表单/JSON）
 * - 内置SSRF防护和安全验证
 * - 支持MD5签名计算
 * - 支持Builder模式构建复杂请求
 * - 统一的错误处理和日志记录
 * - 替代MbsUtils、IdpOAuthUtils、HttpUtils
 * 
 * 安全特性:
 * - SSRF攻击防护
 * - 敏感信息脱敏日志
 * - 默认安全的SSL配置
 * - 请求参数验证
 * 
 * <AUTHOR>
 */
public final class HttpClientUtils {
    
    private static final Logger log = LoggerFactory.getLogger(HttpClientUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 默认超时配置
    private static final int DEFAULT_CONNECT_TIMEOUT = 10000;
    private static final int DEFAULT_SOCKET_TIMEOUT = 30000;
    
    // SSRF防护正则
    private static final String SSRF_PROTOCOL_REGEX = "file:///|gopher://|ftp://|ftps://|jar://|mailto://|ssh2://|telnet://|expect://";
    private static final Pattern SSRF_PATTERN = Pattern.compile(SSRF_PROTOCOL_REGEX);
    
    // 敏感字段列表（用于日志脱敏）
    private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
        "password", "secret", "token", "accessToken", "access_token", "authorization", "client_secret", "secretkey", "sign", "dlmm"
    ));

    private HttpClientUtils() {
        // 工具类不允许实例化
    }
    
    // ==================== 核心HTTP客户端创建 ====================
    
    /**
     * 创建默认的HttpClient实例
     */
    public static HttpClient createHttpClient() {
        return HttpClients.createDefault();
    }
    
    /**
     * 创建带自定义配置的HttpClient实例
     */
    public static HttpClient createHttpClient(int connectTimeout, int socketTimeout) {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(connectTimeout)
                .setSocketTimeout(socketTimeout)
                .build();
        
        return HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();
    }
    
    // ==================== 安全验证功能 ====================
    
    /**
     * SSRF攻击防护验证
     * 
     * @param uri 待验证的URI
     * @return true-安全, false-存在SSRF风险
     */
    public static boolean validateUriSsrf(String uri) {
        if (uri == null || uri.trim().isEmpty()) {
            return false;
        }
        
        if (!uri.startsWith("http")) {
            return false;
        }
        
        // 检查危险协议
        Matcher matcher = SSRF_PATTERN.matcher(uri.toLowerCase());
        if (matcher.find()) {
            log.warn("检测到SSRF攻击尝试: {}", maskSensitiveUrl(uri));
            return false;
        }
        
        // 禁止多个http（简单检查）
        if (uri.toLowerCase().indexOf("http", 4) > 0) {
            log.warn("检测到可疑的多重HTTP协议: {}", maskSensitiveUrl(uri));
            return false;
        }
        
        return true;
    }
    
    /**
     * MD5签名计算
     * 
     * @param params
     * @param separator 参数间的分隔符
     * @return MD5签名字符串（小写）
     */
    public static String calculateMd5Sign(LinkedHashMap<String, String> params, String separator) {
        
        StringBuilder dataBuilder = new StringBuilder();
        
        // LinkedHashMap保证插入顺序
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (dataBuilder.length() > 0 && separator != null) {
                dataBuilder.append(separator);
            }
            dataBuilder.append(entry.getValue());
        }
        
        return md5(dataBuilder.toString());
    }
    
    /**
     * 通用MD5计算
     */
    public static String md5(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }
    
    // ==================== HTTP请求方法 ====================
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException 网络异常
     * @throws SecurityException SSRF安全验证失败
     */
    public static String get(String url, Map<String, String> headers) throws IOException {
        return get(url, headers, DEFAULT_CONNECT_TIMEOUT, DEFAULT_SOCKET_TIMEOUT);
    }
    
    /**
     * 发送GET请求（带超时配置）
     */
    public static String get(String url, Map<String, String> headers, int connectTimeout, int socketTimeout) throws IOException {
        // SSRF安全验证
        if (!validateUriSsrf(url)) {
            throw new SecurityException("URL安全验证失败: " + maskSensitiveUrl(url));
        }
        
        HttpClient httpClient = createHttpClient(connectTimeout, socketTimeout);
        HttpGet httpGet = new HttpGet(url);
        
        // 设置请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        
        // 设置默认请求头
        setDefaultHeaders(httpGet);
        
        log.debug("发送GET请求: {}", maskSensitiveUrl(url));
        HttpResponse response = httpClient.execute(httpGet);
        
        return handleResponse(response, url);
    }



    /**
     * 发送POST请求（表单数据）
     * 
     * @param url 请求URL
     * @param params 表单参数
     * @param headers 请求头
     * @return 响应内容
     */
    public static String postForm(String url, Map<String, String> params, Map<String, String> headers) throws IOException {
        return postForm(url, params, headers, DEFAULT_CONNECT_TIMEOUT, DEFAULT_SOCKET_TIMEOUT);
    }
    
    /**
     * 发送POST请求（表单数据，带超时配置）
     */
    public static String postForm(String url, Map<String, String> params, Map<String, String> headers, 
                                 int connectTimeout, int socketTimeout) throws IOException {
        // SSRF安全验证
        if (!validateUriSsrf(url)) {
            throw new SecurityException("URL安全验证失败: " + maskSensitiveUrl(url));
        }
        
        HttpClient httpClient = createHttpClient(connectTimeout, socketTimeout);
        HttpPost httpPost = new HttpPost(url);
        
        // 设置表单参数
        if (params != null && !params.isEmpty()) {
            List<NameValuePair> formParams = new ArrayList<>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                formParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            httpPost.setEntity(new UrlEncodedFormEntity(formParams, StandardCharsets.UTF_8));
        }
        
        // 设置请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        
        // 设置默认请求头
        setDefaultHeaders(httpPost);
        
        log.debug("发送POST表单请求: {} 参数: {}", maskSensitiveUrl(url), maskSensitiveParams(params));
        HttpResponse response = httpClient.execute(httpPost);
        
        return handleResponse(response, url);
    }

    public static String simplePost(String url, Map<String, String> headers) throws IOException {
        // SSRF安全验证
        if (!validateUriSsrf(url)) {
            throw new SecurityException("URL安全验证失败: " + maskSensitiveUrl(url));
        }

        HttpClient httpClient = createHttpClient();
        HttpPost httpPost = new HttpPost(url);
        // 设置请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        HttpResponse response = httpClient.execute(httpPost);
        return handleResponse(response, url);
    }

    public static String simpleGet(String url, Map<String, String> headers) throws IOException {
        // SSRF安全验证
        if (!validateUriSsrf(url)) {
            throw new SecurityException("URL安全验证失败: " + maskSensitiveUrl(url));
        }

        HttpClient httpClient = createHttpClient();
        HttpGet httpGet = new HttpGet(url);
        // 设置请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        HttpResponse response = httpClient.execute(httpGet);
        return handleResponse(response, url);
    }
    
    /**
     * 发送POST请求（JSON数据）
     * 
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return 响应内容
     */
    public static String postJson(String url, String jsonBody, Map<String, String> headers) throws IOException {
        return postJson(url, jsonBody, headers, DEFAULT_CONNECT_TIMEOUT, DEFAULT_SOCKET_TIMEOUT);
    }
    
    /**
     * 发送POST请求（JSON数据，带超时配置）
     */
    public static String postJson(String url, String jsonBody, Map<String, String> headers, 
                                 int connectTimeout, int socketTimeout) throws IOException {
        HttpClient httpClient = createHttpClient(connectTimeout, socketTimeout);
        HttpPost httpPost = new HttpPost(url);
        
        // 设置JSON请求体
        if (jsonBody != null) {
            StringEntity entity = new StringEntity(jsonBody, StandardCharsets.UTF_8);
            entity.setContentType("application/json; charset=utf-8");
            httpPost.setEntity(entity);
        }
        
        // 设置请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        
        // 设置默认请求头
        setDefaultHeaders(httpPost);
        
        log.debug("发送POST JSON请求: {}", url);
        HttpResponse response = httpClient.execute(httpPost);
        
        return handleResponse(response, url);
    }
    
    /**
     * 智能HTTP请求（根据URL和参数自动选择GET或POST）
     * 
     * @param url 请求URL（可以包含查询参数）
     * @param params 表单参数（可选）
     * @param headers 请求头
     * @return 响应内容
     */
    public static String request(String url, Map<String, String> params, Map<String, String> headers) throws IOException {
        // 检查URL是否包含查询参数
        if (url.contains("?") && (params == null || params.isEmpty())) {
            // URL包含参数且没有表单参数，使用GET方式
            log.debug("URL包含查询参数，使用GET请求: {}", url);
            return get(url, headers);
        } else {
            // 有表单参数，使用POST方式
            log.debug("有表单参数，使用POST请求: {}", url);
            return postForm(url, params, headers);
        }
    }
    
    /**
     * 处理HTTP响应
     */
    private static String handleResponse(HttpResponse response, String url) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity entity = response.getEntity();
        String responseBody = entity != null ? EntityUtils.toString(entity, StandardCharsets.UTF_8) : "";
        
        log.debug("收到响应 (status={}): {}", statusCode, truncate(responseBody));
        
        if (statusCode >= 200 && statusCode < 300) {
            return responseBody;
        } else {
            throw new IOException("HTTP " + statusCode + " error : " + responseBody);
        }
    }
    
    /**
     * 设置默认请求头
     */
    private static void setDefaultHeaders(HttpRequestBase request) {
        if (request.getFirstHeader("User-Agent") == null) {
            request.setHeader("User-Agent", "MetaOrg-HttpClient/1.0");
        }
        if (request.getFirstHeader("Accept") == null) {
            request.setHeader("Accept", "application/json, text/plain, */*");
        }
    }
    
    /**
     * 截断长字符串用于日志
     */
    private static String truncate(String s) {
        if (s == null) return null;
        return s.length() > 512 ? s.substring(0, 512) + "..." : s;
    }
    
    /**
     * 将响应JSON转换为Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> parseJsonResponse(String jsonResponse) throws IOException {
        return objectMapper.readValue(jsonResponse, Map.class);
    }
    
    /**
     * 将对象转换为JSON字符串
     */
    public static String toJson(Object obj) throws IOException {
        return objectMapper.writeValueAsString(obj);
    }
    
    // ==================== 安全工具方法 ====================
    
    /**
     * 脱敏URL（隐藏敏感参数）
     */
    public static String maskSensitiveUrl(String url) {
        if (url == null) return null;
        
        String maskedUrl = url;
        for (String sensitiveField : SENSITIVE_FIELDS) {
            // 匹配 field=value& 或 field=value$ 的模式
            String pattern = "(" + sensitiveField + "=)[^&]*";
            maskedUrl = maskedUrl.replaceAll("(?i)" + pattern, "$1***");
        }
        return maskedUrl;
    }
    
    /**
     * 通用脱敏参数Map（隐藏敏感参数值）
     * 支持Map<String, ?>类型，自动处理不同类型的值
     */
    public static <T> Map<String, T> maskSensitiveParams(Map<String, T> params) {
        if (params == null || params.isEmpty()) {
            return params;
        }
        
        Map<String, T> maskedParams = new HashMap<>();
        for (Map.Entry<String, T> entry : params.entrySet()) {
            String key = entry.getKey();
            T value = entry.getValue();
            
            // 检查是否为敏感字段
            boolean isSensitive = false;
            for (String sensitiveField : SENSITIVE_FIELDS) {
                if (key.toLowerCase().contains(sensitiveField.toLowerCase())) {
                    isSensitive = true;
                    break;
                }
            }
            
            // 如果是敏感字段，替换为"***"
            if (isSensitive) {
                @SuppressWarnings("unchecked")
                T maskedValue = (T) "***";
                maskedParams.put(key, maskedValue);
            } else {
                // 如果不是敏感字段，保持原值
                maskedParams.put(key, value);
            }
        }
        return maskedParams;
    }
    
    /**
     * 脱敏String参数Map（隐藏敏感参数值）
     * 便捷方法，用于处理Map<String, String>类型
     */
    public static Map<String, String> maskSensitiveStringParams(Map<String, String> params) {
        return maskSensitiveParams(params);
    }
    
    /**
     * 脱敏Object参数Map（隐藏敏感参数值）
     * 便捷方法，用于处理Map<String, Object>类型
     */
    public static Map<String, Object> maskSensitiveObjectParams(Map<String, Object> params) {
        return maskSensitiveParams(params);
    }
    
    // ==================== Builder模式支持 ====================
    
    /**
     * HTTP请求构建器
     */
    public static class RequestBuilder {
        private String url;
        private String method = "GET";
        private Map<String, String> headers = new HashMap<>();
        private Map<String, String> params = new HashMap<>();
        private String jsonBody;
        private int connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        private int socketTimeout = DEFAULT_SOCKET_TIMEOUT;
        
        private RequestBuilder(String url) {
            this.url = Objects.requireNonNull(url, "URL不能为空");
        }
        
        public static RequestBuilder create(String url) {
            return new RequestBuilder(url);
        }
        
        public RequestBuilder method(String method) {
            this.method = method;
            return this;
        }
        
        public RequestBuilder header(String name, String value) {
            this.headers.put(name, value);
            return this;
        }
        
        public RequestBuilder headers(Map<String, String> headers) {
            if (headers != null) {
                this.headers.putAll(headers);
            }
            return this;
        }
        
        public RequestBuilder param(String name, String value) {
            this.params.put(name, value);
            return this;
        }
        
        public RequestBuilder params(Map<String, String> params) {
            if (params != null) {
                this.params.putAll(params);
            }
            return this;
        }
        
        public RequestBuilder jsonBody(String jsonBody) {
            this.jsonBody = jsonBody;
            return this;
        }
        
        public RequestBuilder timeout(int connectTimeout, int socketTimeout) {
            this.connectTimeout = connectTimeout;
            this.socketTimeout = socketTimeout;
            return this;
        }
        
        /**
         * 构建OAuth2 Token请求（兼容IdpOAuthUtils）
         */
        public static RequestBuilder oauthToken(String url, String clientId, String clientSecret, 
                                              String code, String redirectUri) {
            return create(url)
                .method("POST")
                .param("grant_type", "authorization_code")
                .param("client_id", clientId)
                .param("client_secret", clientSecret)
                .param("code", code)
                .param("redirect_uri", redirectUri);
        }
        
        /**
         * 构建MBS token校验请求（兼容MbsUtils）
         */
        public static RequestBuilder mbsTokenCheck(String url, String orgCode, String token, 
                                                 String appkey, String[] extendAttr, String sign) {
            RequestBuilder builder = create(url)
                .method("POST")
                .param("orgCode", orgCode)
                .param("token", token)
                .param("appkey", appkey)
                .param("sign", sign);
                
            if (extendAttr != null && extendAttr.length > 0) {
                builder.param("extendAttr", String.join(",", extendAttr));
            }
            
            return builder;
        }
        
        /**
         * 执行请求
         */
        public String execute() throws IOException {
            if ("GET".equalsIgnoreCase(method)) {
                // GET请求：将参数添加到URL
                if (!params.isEmpty()) {
                    StringBuilder urlBuilder = new StringBuilder(url);
                    urlBuilder.append(url.contains("?") ? "&" : "?");
                    
                    boolean first = true;
                    for (Map.Entry<String, String> entry : params.entrySet()) {
                        if (!first) urlBuilder.append("&");
                        urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                        first = false;
                    }
                    url = urlBuilder.toString();
                }
                return get(url, headers, connectTimeout, socketTimeout);
                
            } else if ("POST".equalsIgnoreCase(method)) {
                if (jsonBody != null) {
                    return postJson(url, jsonBody, headers, connectTimeout, socketTimeout);
                } else {
                    return postForm(url, params, headers, connectTimeout, socketTimeout);
                }
            } else {
                throw new IllegalArgumentException("不支持的HTTP方法: " + method);
            }
        }
        
        /**
         * 执行请求并解析JSON响应
         */
        public Map<String, Object> executeForJson() throws IOException {
            String response = execute();
            return parseJsonResponse(response);
        }
    }
}
