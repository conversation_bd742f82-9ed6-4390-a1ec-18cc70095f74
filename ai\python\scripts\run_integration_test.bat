@echo off
REM 全流程集成测试运行脚本 (Windows)

echo 🎯 新疆金融监管局处罚信息爬虫 - 全流程集成测试
echo ============================================================

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

REM 检查uv
uv --version >nul 2>&1
if errorlevel 1 (
    echo ❌ uv未安装，请先安装uv
    pause
    exit /b 1
)

REM 设置测试环境
echo 🔧 设置测试环境...
if exist ".env.test" (
    copy ".env.test" ".env" >nul
    echo ✅ 测试配置文件已复制
)

REM 安装依赖
echo 📦 安装依赖...
uv sync

REM 运行集成测试脚本
echo 🧪 运行集成测试...
uv run python scripts/run_integration_test.py

pause
