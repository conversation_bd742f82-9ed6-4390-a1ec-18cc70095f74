package com.metaorg.framework.shiro.cache;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import com.metaorg.framework.redis.NamespacedRedisTemplate;

/**
 * 使用redis实现Cache缓存
 * 
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class RedisCache<K, V> implements Cache<K, V>
{
    private long expireTime;// 缓存的超时时间，单位为s

    /**
     * 用于shiro的cache的名字
     */
    private String cacheName = "shiro_session";

    private NamespacedRedisTemplate<K, V> namespacedRedisTemplate; // 通过构造方法注入该对象

    public RedisCache()
    {
        super();
    }

    public RedisCache(long expireTime, NamespacedRedisTemplate<K, V> namespacedRedisTemplate)
    {
        super();
        this.expireTime = expireTime;
        this.namespacedRedisTemplate = namespacedRedisTemplate;
    }

    public RedisCache(String cacheName, long expireTime, NamespacedRedisTemplate<K, V> namespacedRedisTemplate)
    {
        super();
        this.cacheName = cacheName;
        this.expireTime = expireTime;
        this.namespacedRedisTemplate = namespacedRedisTemplate;
    }

    public String cacheKey(K key)
    {
        // 如果是PrincipalCollection类型，生成稳定的缓存键
        if (key instanceof org.apache.shiro.subject.PrincipalCollection) {
            org.apache.shiro.subject.PrincipalCollection principals = (org.apache.shiro.subject.PrincipalCollection) key;
            Object primaryPrincipal = principals.getPrimaryPrincipal();
            
            // 使用反射获取用户ID和登录名，避免导入问题
            try {
                if (primaryPrincipal != null) {
                    Class<?> userClass = primaryPrincipal.getClass();
                    if (userClass.getName().contains("SysUser")) {
                        // 获取userId
                        Object userId = userClass.getMethod("getUserId").invoke(primaryPrincipal);
                        // 获取loginName
                        Object loginName = userClass.getMethod("getLoginName").invoke(primaryPrincipal);
                        
                        // 生成稳定的缓存键，包含用户ID和登录名
                        // 这样当用户信息更新时，可以通过用户ID清理对应的缓存
                        String stableKey = "user_" + userId + "_" + loginName;
                        return keyPrefix() + stableKey;
                    }
                }
            } catch (Exception e) {
                // 如果反射失败，使用默认方式
                System.err.println("Failed to generate stable cache key: " + e.getMessage());
            }
        }
        
        return keyPrefix() + key;
    }

    public String keyPrefix()
    {
        // 根据EhCache配置，为不同的缓存类型使用不同的前缀
        // 这样可以更好地组织Redis中的缓存结构，便于管理和监控
        switch (cacheName) {
            case "sys-config":
                return "sys_config:";
            case "sys-dict":
                return "sys_dict:";
            case "sys-cache":
                return "sys_cache:";
            case "sys-authCache":
                return "sys_auth:";
            case "sys-userCache":
                return "sys_user:";
            case "loginRecordCache":
                return "login_record:";
            case "appValidateCodeCache":
                return "app_validate_code:";
            case "shiro-activeSessionCache":
                return "shiro_session:";
            default:
                return cacheName + ":";
        }
    }

    /**
     * 通过key来获取对应的缓存对象
     */
    @Override
    public V get(K key) throws CacheException
    {
        String cacheKey = cacheKey(key);
        V value = namespacedRedisTemplate.get((K) cacheKey);
        return value;
    }

    /**
     * 将权限信息加入缓存中
     */
    @Override
    public V put(K key, V value) throws CacheException
    {
        String cacheKey = cacheKey(key);
        
        if ("loginRecordCache".equals(cacheName))
        {
            namespacedRedisTemplate.set((K) cacheKey, value, 600, TimeUnit.SECONDS);
        }
        else if (expireTime > 0)
        {
            // 设置过期时间
            namespacedRedisTemplate.set((K) cacheKey, value, expireTime, TimeUnit.SECONDS);
        }
        else
        {
            // 永不过期（expireTime = 0）
            namespacedRedisTemplate.set((K) cacheKey, value);
        }
        return value;
    }

    /**
     * 将权限信息从缓存中删除
     */
    @Override
    public V remove(K key) throws CacheException
    {
        V v = namespacedRedisTemplate.get((K) cacheKey(key));
        namespacedRedisTemplate.delete((K) cacheKey(key));
        return v;
    }

    @Override
    public void clear() throws CacheException
    {
        // 由于Redis没有直接的方法获取所有键，我们无法清理所有缓存
        // 但我们可以通过删除特定的缓存键来清理
        // 这里我们暂时不做任何操作，因为keys()方法返回空Set
        // 如果需要清理所有缓存，建议使用Redis的FLUSHDB命令或通过其他方式
    }

    @Override
    public int size()
    {
        return 0;
    }

    @Override
    public Set<K> keys()
    {
        // 由于Redis没有直接的方法获取所有键，我们返回一个空的Set
        // 这样可以避免NullPointerException，同时保持接口的兼容性
        return new java.util.HashSet<>();
    }

    @Override
    public Collection<V> values()
    {
        return null;
    }

    /**
     * 获取底层的NamespacedRedisTemplate实例
     * @return NamespacedRedisTemplate实例
     */
    public NamespacedRedisTemplate<K, V> getNamespacedRedisTemplate()
    {
        return namespacedRedisTemplate;
    }

}
