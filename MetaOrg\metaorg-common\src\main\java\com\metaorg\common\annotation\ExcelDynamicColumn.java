package com.metaorg.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记一个字段为Excel动态列映射
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelDynamicColumn
{
    /**
     * 动态列的起始列名（该列本身不包含在Map中，其后的列才开始映射）。
     * 这是识别动态列范围的关键。
     */
    String startColumnName();

    /**
     * Map中Value的数据类型，默认为Integer。
     * 支持：Integer.class, Long.class, Double.class, String.class
     */
    Class<?> valueType() default String.class;

    /**
     * 当单元格为空或值无法转换为指定valueType时的默认值。
     * 该字符串将被尝试转换为valueType。
     */
    String defaultValue() default "";
}