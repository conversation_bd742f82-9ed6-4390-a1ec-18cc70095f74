package com.metaorg.web.controller.library;

import java.util.List;

import com.metaorg.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.library.domain.BizPersonFavorite;
import com.metaorg.library.service.IBizPersonFavoriteService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import com.metaorg.utils.DataScopeUtils;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.Map;

/**
 * 人员收藏Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Controller
@RequestMapping("/library/favorite")
public class BizPersonFavoriteController extends BaseController {
    private String prefix = "library/favorite";

    @Autowired
    private IBizPersonFavoriteService bizPersonFavoriteService;

    /**
     * 我的收藏夹
     */
    @GetMapping("/my_favorite")
    public String myFavorite(ModelMap mmap)
    {
        return prefix + "/my_favorite";
    }

    @PostMapping("/my_favorite/list")
    @ResponseBody
    public TableDataInfo myFavoriteList(BizPersonFavorite bizPersonFavorite, @RequestParam Map<String, Object> paramMap)
    {
        long userId = getUserId();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonFavorite, getSysUser(), "vpd", "", "", getUserDeptId());
        }
        bizPersonFavorite.setUserId(userId);
        startPage();
        
        List<BizPersonFavorite> list = bizPersonFavoriteService.selectBizPersonFavoriteList(bizPersonFavorite);
        return getDataTable(list);
    }

    /**
     * 收藏人员
     */
    @RequiresPermissions(value = { "library:person:edit" })
    @Log(title = "人员收藏", businessType = BusinessType.INSERT)
    @PostMapping("/add/{personId}")
    @ResponseBody
    public AjaxResult addSave(@PathVariable("personId") String personId) {
        Long userId = getUserId();
        String[] personIds = { personId };
        return toAjax(bizPersonFavoriteService.insertBizPersonFavorite(userId, personIds));
    }

    /**
     * 取消收藏人员
     */
    @Log(title = "人员收藏", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{personId}")
    @ResponseBody
    public AjaxResult remove(@PathVariable("personId") String personId) {   
        Long userId = getUserId();
        String[] personIds = { personId };
        return toAjax(bizPersonFavoriteService.deleteBizPersonFavoriteByUserId(userId, personIds));
    }

    /**
     * 批量取消收藏人员
     */
    @Log(title = "人员收藏", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult batchRemove(String ids) {
        Long userId = getUserId();
        String[] idsArray = Convert.toStrArray(ids);
        return toAjax(bizPersonFavoriteService.deleteBizPersonFavoriteByIds(userId, idsArray));
    }
}
