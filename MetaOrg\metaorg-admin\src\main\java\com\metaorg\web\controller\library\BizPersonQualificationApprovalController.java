package com.metaorg.web.controller.library;

import com.metaorg.common.annotation.Log;
import com.metaorg.common.config.RuoYiConfig;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.exception.UtilException;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.utils.file.FileUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import com.metaorg.library.domain.BizPersonQualificationApproval;
import com.metaorg.library.service.IBizPersonQualificationApprovalService;
import com.metaorg.system.service.ISysDeptService;
import com.metaorg.utils.DataScopeUtils;
import com.metaorg.web.controller.tool.POIUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.core.text.Convert;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 任职资格核准Controller
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Controller
@RequestMapping("/library/qualification_approval")
public class BizPersonQualificationApprovalController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(BizPersonQualificationApprovalController.class);
    private String prefix = "library/qualification_approval";

    @Autowired
    private IBizPersonQualificationApprovalService bizPersonQualificationApprovalService;

    @Autowired
    private ISysDeptService deptService;

    @RequiresPermissions("library:qualification_approval:view")
    @GetMapping()
    public String qualification_approval(ModelMap mmap)
    {
        SysDept dept = deptService.selectDeptById(getSysUser().getDeptId());
        if(StringUtils.isNotNull(dept)) {
            mmap.put("deptId", dept.getDeptId());
            mmap.put("deptName", dept.getDeptName());
        }
        return prefix + "/qualification_approval";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizPersonQualificationApproval bizPersonQualificationApproval, Map<String, Object> paramMap){
        long deptId = -1;
        if(paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonQualificationApproval.getParams().put("deptId", deptId);
        }
        if (paramMap.get("approveStatusList") != null && paramMap.get(("approveStatusList")) != "") {
            bizPersonQualificationApproval.getParams().put("approveStatusList", Convert.toStrArray(paramMap.get("approveStatusList").toString()));
        }
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonQualificationApproval, getSysUser(), "d", "", "", deptId);
        }
    }

    /**
     * 查询任职资格核准列表
     */
    @RequiresPermissions(value={"library:qualification_approval:list","library:person:view"},logical= Logical.OR)
    @PostMapping({"/list/{personId}", "/list"})
    @ResponseBody
    public TableDataInfo list(@PathVariable(value = "personId", required = false) String personId, @RequestParam Map<String, Object> paramMap, BizPersonQualificationApproval bizPersonQualificationApproval)
    {
        getQueryOptions(bizPersonQualificationApproval, paramMap);
        startPage();
        List<BizPersonQualificationApproval> list = bizPersonQualificationApprovalService.selectBizPersonQualificationApprovalList(bizPersonQualificationApproval);
        return getDataTable(list);
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename)
    {
        Path downloadPath = Paths.get(RuoYiConfig.getDownloadPath(), filename);
        File file = downloadPath.toFile();
        if (!file.getParentFile().exists())
        {
            file.getParentFile().mkdirs();
        }
        return file.getPath();
    }

    /**
     * 导出任职资格核准列表
     */
    @RequiresPermissions("library:qualification_approval:export")
    @Log(title = "任职资格核准", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestParam Map<String, Object> paramMap, BizPersonQualificationApproval bizPersonQualificationApproval, HttpServletResponse resp)
    {
        getQueryOptions(bizPersonQualificationApproval, paramMap);
        bizPersonQualificationApproval.getParams().put("orderBy", "prompt_day desc,qa.id desc");
        List<BizPersonQualificationApproval> list = bizPersonQualificationApprovalService.selectBizPersonQualificationApprovalList(bizPersonQualificationApproval);

        OutputStream out = null;
        FileInputStream fileInputStream = null;
        String filename = "任职资格核准_" + System.currentTimeMillis() +".xls";
        try
        {
            File file = new File("./config/template/zghz_tpl.xls").getAbsoluteFile();
            fileInputStream = new FileInputStream(file);
            //通过流的方式读取文件
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            for(int i = 0; i < list.size(); i++) {
                POIUtil.copyRows(hssfWorkbook, 0, 0, 2, 2, 3 + i);
                HSSFRow row = hssfSheet.getRow(3 + i);
                row.getCell(0).setCellValue(i + 1);  //序号
                row.getCell(1).setCellValue(list.get(i).getPerson().getName());  //姓名
                row.getCell(2).setCellValue(list.get(i).getPositionName());  //现任职务
                row.getCell(3).setCellValue(list.get(i).getPositionDay() == null ? "" : list.get(i).getPositionDay().replace("-", ".").replace("Invalid date", ""));  //任职时间
                row.getCell(4).setCellValue(list.get(i).getApprovePaperNo());  //文号
                row.getCell(5).setCellValue(list.get(i).getPromptDay() == null ? "" : list.get(i).getPromptDay().replace("-", ".").replace("Invalid date", ""));  //提示时间
                row.getCell(6).setCellValue(list.get(i).getApproveStatus());  //状态
                row.getCell(7).setCellValue(list.get(i).getPromptContent());  //提示内容
                row.getCell(8).setCellValue(list.get(i).getNote());  //备注
            }
            POIUtil.removeRow(hssfSheet, 2);
            out = new FileOutputStream(getAbsoluteFile(filename));
            hssfWorkbook.write(out);
            return AjaxResult.success(filename);
        }catch (Exception e)
        {
            log.error("导出Excel异常{}", e.getMessage());
            throw new UtilException("导出Excel失败，请联系网站管理员！");
        }
        finally
        {
            FileUtils.closeFile(fileInputStream);
            FileUtils.closeFile(out);
        }
    }

    /**
     * 新增任职资格核准
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        return prefix + "/add";
    }

    /**
     * 新增保存任职资格核准
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "任职资格核准", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonQualificationApproval bizPersonQualificationApproval)
    {
        bizPersonQualificationApproval.setCreateBy(getLoginName());
        return toAjax(bizPersonQualificationApprovalService.insertBizPersonQualificationApproval(bizPersonQualificationApproval));
    }

    /**
     * 修改任职资格核准
     */
    @RequiresPermissions(value={"library:person:edit","library:qualification_approval:edit"},logical= Logical.OR)
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonQualificationApproval bizPersonQualificationApproval = bizPersonQualificationApprovalService.selectBizPersonQualificationApprovalById(id);
        mmap.put("bizPersonQualificationApproval", bizPersonQualificationApproval);
        return prefix + "/edit";
    }

    /**
     * 修改保存任职资格核准
     */
    @RequiresPermissions(value={"library:person:edit","library:qualification_approval:edit"},logical= Logical.OR)
    @Log(title = "任职资格核准", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonQualificationApproval bizPersonQualificationApproval)
    {
        bizPersonQualificationApproval.setUpdateBy(getLoginName());
        return toAjax(bizPersonQualificationApprovalService.updateBizPersonQualificationApproval(bizPersonQualificationApproval));
    }

    /**
     * 删除任职资格核准
     */
    @RequiresPermissions(value={"library:person:edit","library:qualification_approval:remove"},logical= Logical.OR)
    @Log(title = "任职资格核准", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonQualificationApprovalService.deleteBizPersonQualificationApprovalByIds(ids));
    }
}
