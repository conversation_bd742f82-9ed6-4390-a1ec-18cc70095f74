"""
系统配置管理模块
"""

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from pathlib import Path
from dotenv import load_dotenv
from urllib.parse import quote_plus

# 加载环境变量
load_dotenv(Path(__file__).parent.parent.parent.parent / ".env")  

print(f"环境变量配置目录: {Path(__file__).parent.parent.parent.parent}")

class DatabaseConfig(BaseSettings):
    """数据库配置"""
    host: str = "127.0.0.1"
    port: int = 15433
    name: str = ""
    user: str = ""
    pwd: str = ""
    db_schema: str = Field(default="public", json_schema_extra="DATABASE_SCHEMA")

    model_config = SettingsConfigDict(
        env_prefix="DATABASE_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )

    @property
    def connection_string(self) -> str:
        """获取数据库连接字符串"""
        encoded_pwd = quote_plus(self.pwd)
        return f"postgresql://{self.user}:{encoded_pwd}@{self.host}:{self.port}/{self.name}?options=-c%20search_path%3D{self.db_schema}"


class RabbitMQConfig(BaseSettings):
    """RabbitMQ 配置"""
    
    host: str = "127.0.0.1"
    port: int = 5672
    username: str = ""
    pwd: str = ""
    virtual_host: str = "/"
    queue_name: str = "metaorg.sync.punish.crawler.request.queue"
    
    model_config = SettingsConfigDict(
        env_prefix="RABBITMQ_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    @property
    def connection_url(self) -> str:
        """构建 RabbitMQ 连接 URL"""
        return f"amqp://{self.username}:{self.pwd}@{self.host}:{self.port}{self.virtual_host}"


class OpenAIConfig(BaseSettings):
    """OpenAI配置"""
    api_key: str = ""
    base_url: str = ""
    model_name: str = ""

    model_config = SettingsConfigDict(
        env_prefix="OPENAI_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        protected_namespaces=('settings_',)  # 修复Pydantic保护命名空间冲突
    )


class CrawlerConfig(BaseSettings):
    """爬虫配置"""
    target_url: str = "https://www.nfra.gov.cn/branch/xinjiang/view/pages/common/ItemList.html?itemPId=2120&itemId=2124&itemUrl=ItemListRightList.html&itemName=%E8%A1%8C%E6%94%BF%E5%A4%84%E7%BD%9A#1"
    headless: bool = True
    timeout: int = 30
    max_retries: int = 3
    
    model_config = SettingsConfigDict(
        env_prefix="CRAWLER_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


class LogConfig(BaseSettings):
    """日志配置"""
    level: str = "INFO"
    file: str = "logs/nfra_crawler.log"
    
    model_config = SettingsConfigDict(
        env_prefix="LOG_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


class Settings(BaseSettings):
    """系统总配置"""
    database: DatabaseConfig = DatabaseConfig()
    rabbitmq: RabbitMQConfig = RabbitMQConfig()
    openai: OpenAIConfig = OpenAIConfig()
    crawler: CrawlerConfig = CrawlerConfig()
    log: LogConfig = LogConfig()
    
    # 业务配置
    business_type: str = "nfra_punish"
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


# 全局配置实例
settings = Settings()
