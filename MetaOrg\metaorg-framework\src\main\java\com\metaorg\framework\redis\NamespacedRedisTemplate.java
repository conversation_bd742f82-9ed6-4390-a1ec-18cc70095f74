package com.metaorg.framework.redis;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * 带命名空间的RedisTemplate包装器
 * 自动为所有Redis操作添加命名空间前缀，避免与其他系统的缓存冲突
 * 
 * <AUTHOR>
 */
public class NamespacedRedisTemplate<K, V> {
    
    private final RedisTemplate<K, V> redisTemplate;
    private final RedisNamespaceUtil namespaceUtil;
    
    public NamespacedRedisTemplate(RedisTemplate<K, V> redisTemplate, RedisNamespaceUtil namespaceUtil) {
        this.redisTemplate = redisTemplate;
        this.namespaceUtil = namespaceUtil;
    }
    
    /**
     * 获取原始的RedisTemplate
     */
    public RedisTemplate<K, V> getRedisTemplate() {
        return redisTemplate;
    }
    
    /**
     * 获取命名空间工具类
     */
    public RedisNamespaceUtil getNamespaceUtil() {
        return namespaceUtil;
    }
    
    // ==================== String操作 ====================
    
    public void set(K key, V value) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        redisTemplate.opsForValue().set((K) namespacedKey, value);
    }
    
    public void set(K key, V value, long timeout, TimeUnit unit) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        redisTemplate.opsForValue().set((K) namespacedKey, value, timeout, unit);
    }
    
    public V get(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForValue().get((K) namespacedKey);
    }
    
    public Boolean delete(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.delete((K) namespacedKey);
    }
    
    public Long delete(Collection<K> keys) {
        String[] namespacedKeys = namespaceUtil.addNamespace(keys.stream().map(Object::toString).toArray(String[]::new));
        return redisTemplate.delete((Collection<K>) java.util.Arrays.asList(namespacedKeys));
    }
    
    public Boolean hasKey(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.hasKey((K) namespacedKey);
    }
    
    public Boolean expire(K key, long timeout, TimeUnit unit) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.expire((K) namespacedKey, timeout, unit);
    }
    
    public Long getExpire(K key, TimeUnit unit) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.getExpire((K) namespacedKey, unit);
    }
    
    // ==================== Hash操作 ====================
    
    public void hSet(K key, Object hashKey, V value) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        redisTemplate.opsForHash().put((K) namespacedKey, hashKey, value);
    }
    
    public V hGet(K key, Object hashKey) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return (V) redisTemplate.opsForHash().get((K) namespacedKey, hashKey);
    }
    
    public Map<Object, Object> hGetAll(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForHash().entries((K) namespacedKey);
    }
    
    public Boolean hDelete(K key, Object... hashKeys) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForHash().delete((K) namespacedKey, hashKeys) > 0;
    }
    
    public Boolean hHasKey(K key, Object hashKey) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForHash().hasKey((K) namespacedKey, hashKey);
    }
    
    // ==================== List操作 ====================
    
    public Long lPush(K key, V value) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForList().leftPush((K) namespacedKey, value);
    }
    
    public V lPop(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForList().leftPop((K) namespacedKey);
    }
    
    public Long rPush(K key, V value) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForList().rightPush((K) namespacedKey, value);
    }
    
    public V rPop(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForList().rightPop((K) namespacedKey);
    }
    
    public List<V> lRange(K key, long start, long end) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForList().range((K) namespacedKey, start, end);
    }
    
    // ==================== Set操作 ====================
    
    public Long sAdd(K key, V... values) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForSet().add((K) namespacedKey, values);
    }
    
    public Set<V> sMembers(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForSet().members((K) namespacedKey);
    }
    
    public Boolean sIsMember(K key, V value) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForSet().isMember((K) namespacedKey, value);
    }
    
    public Long sRemove(K key, Object... values) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForSet().remove((K) namespacedKey, values);
    }
    
    // ==================== ZSet操作 ====================
    
    public Boolean zAdd(K key, V value, double score) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForZSet().add((K) namespacedKey, value, score);
    }
    
    public Set<V> zRange(K key, long start, long end) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForZSet().range((K) namespacedKey, start, end);
    }
    
    public Long zRemove(K key, Object... values) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.opsForZSet().remove((K) namespacedKey, values);
    }
    
    // ==================== 通用操作 ====================
    
    public DataType type(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        return redisTemplate.type((K) namespacedKey);
    }
    
    public Set<K> keys(K pattern) {
        // 为模式添加命名空间前缀
        String namespacedPattern = namespaceUtil.addNamespace(pattern.toString());
        Set<K> keys = redisTemplate.keys((K) namespacedPattern);
        
        // 移除返回结果中的命名空间前缀
        if (keys != null) {
            keys = keys.stream()
                .map(key -> (K) namespaceUtil.removeNamespace(key.toString()))
                .collect(java.util.stream.Collectors.toSet());
        }
        
        return keys;
    }
    
    // scan方法暂时不实现，因为需要额外的依赖
    
    public <T> T execute(RedisScript<T> script, List<K> keys, Object... args) {
        // 为脚本的keys添加命名空间前缀
        List<K> namespacedKeys = keys.stream()
            .map(key -> (K) namespaceUtil.addNamespace(key.toString()))
            .collect(java.util.stream.Collectors.toList());
        
        return redisTemplate.execute(script, namespacedKeys, args);
    }
    
    public <T> T execute(RedisScript<T> script, RedisSerializer<?> argsSerializer, RedisSerializer<T> resultSerializer, List<K> keys, Object... args) {
        // 为脚本的keys添加命名空间前缀
        List<K> namespacedKeys = keys.stream()
            .map(key -> (K) namespaceUtil.addNamespace(key.toString()))
            .collect(java.util.stream.Collectors.toList());
        
        return redisTemplate.execute(script, argsSerializer, resultSerializer, namespacedKeys, args);
    }
    
    // ==================== 批量操作 ====================
    
    public void multi() {
        redisTemplate.multi();
    }
    
    public List<Object> exec() {
        return redisTemplate.exec();
    }
    
    public void discard() {
        redisTemplate.discard();
    }
    
    public void watch(K key) {
        String namespacedKey = namespaceUtil.addNamespace(key.toString());
        redisTemplate.watch((K) namespacedKey);
    }
    
    public void unwatch() {
        redisTemplate.unwatch();
    }
}
