https://blog.csdn.net/ClementAD/article/details/53392360

cd /etc/systemd/system

vim metaorg.service

[Unit]
Description=metaorg
After=syslog.target

[Service]
ExecStart=/mnt/app/metavun/meta_org1/metaorg-admin.jar
SuccessExitStatus=143
Restart=always
RestartSec=5
StartLimitInterval=0

[Install]
WantedBy=multi-user.target


systemctl command xxx.service
# 其中command可以是start、stop、restart、enable等，比如：
systemctl start httpd.service #启动Apache服务
systemctl stop httpd.service #停止Apache服务
systemctl restart httpd.service #停止Apache服务
systemctl enable mariadb.service #将MariaDB服务设为开机启动

systemctl status metaorg.service
systemctl list-unit-files | grep enabled 查看所有开机启动服务
