# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: org.postgresql.Driver
        druid:
            # 主库数据源
            master:
                url: *********************************,localhost:15432/metaorg?currentSchema=public&targetServerType=master&loadBalanceHost=true
                username: gaussdb
                encpwd: Je3Mc@z#2024S!
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: *********************************/metaorg?currentSchema=public
                username: gaussdb
                encpwd: Je3Mc@z#2024S!
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
#            validationQuery: SELECT 1 FROM DUAL
            validationQuery: SELECT 1 FROM biz_person
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            connectProperties: config.decrypt=false;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJQbSbTkfJJMoh7+KG6sKBbrxLx2hPLf0fYojx4c3W5jl6ysAbWig1fPYFGGvxIO/IvXL+CeJwsyNWFD6fqYz08CAwEAAQ==
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                login-u: ruoyi
                login-p: 123456
            filter:
                config:
                    # 是否配置加密
                    enabled: false
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
