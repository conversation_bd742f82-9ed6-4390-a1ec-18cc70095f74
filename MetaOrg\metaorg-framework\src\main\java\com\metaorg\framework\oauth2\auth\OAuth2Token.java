package com.metaorg.framework.oauth2.auth;

import org.apache.shiro.authc.UsernamePasswordToken;

/**
 * OAuth2自定义认证Token
 * 用于OAuth2单点登录认证
 * 
 * <AUTHOR>
 */
public class OAuth2Token extends UsernamePasswordToken {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * OAuth2访问令牌
     */
    private String accessToken;
    
    /**
     * OAuth2授权码
     */
    private String authorizationCode;
    
    /**
     * 认证来源（如：统一平台、第三方系统等）
     */
    private String source;
    
    /**
     * 用户信息（从OAuth2服务器获取的用户详情）
     */
    private Object userInfo;
    
    public OAuth2Token() {
        super();
    }
    
    /**
     * 使用用户名和密码创建OAuth2Token（兼容原有逻辑）
     */
    public OAuth2Token(String username, String password) {
        super(username, password, false);
    }
    
    /**
     * 使用用户名、密码和记住我选项创建OAuth2Token
     */
    public OAuth2Token(String username, String password, boolean rememberMe) {
        super(username, password, rememberMe);
    }
    
    /**
     * 使用访问令牌创建OAuth2Token
     */
    public OAuth2Token(String username, String accessToken, String source) {
        super(username, "", false);
        this.accessToken = accessToken;
        this.source = source;
    }
    
    /**
     * 使用授权码创建OAuth2Token
     */
    public OAuth2Token(String username, String authorizationCode, String source, boolean isCode) {
        super(username, "", false);
        if (isCode) {
            this.authorizationCode = authorizationCode;
        } else {
            this.accessToken = authorizationCode;
        }
        this.source = source;
    }
    
    /**
     * 完整参数构造器
     */
    public OAuth2Token(String username, String accessToken, String authorizationCode, String source, Object userInfo) {
        super(username, "", false);
        this.accessToken = accessToken;
        this.authorizationCode = authorizationCode;
        this.source = source;
        this.userInfo = userInfo;
    }
    
    public String getAccessToken() {
        return accessToken;
    }
    
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    
    public String getAuthorizationCode() {
        return authorizationCode;
    }
    
    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public Object getUserInfo() {
        return userInfo;
    }
    
    public void setUserInfo(Object userInfo) {
        this.userInfo = userInfo;
    }
    
    /**
     * 判断是否为OAuth2认证
     */
    public boolean isOAuth2Auth() {
        return accessToken != null || authorizationCode != null;
    }
    
    /**
     * 获取认证标识符（优先使用accessToken，其次authorizationCode）
     */
    public String getCredential() {
        if (accessToken != null) {
            return accessToken;
        }
        if (authorizationCode != null) {
            return authorizationCode;
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "OAuth2Token{" +
                "username='" + getUsername() + '\'' +
                ", source='" + source + '\'' +
                ", hasAccessToken=" + (accessToken != null) +
                ", hasAuthCode=" + (authorizationCode != null) +
                ", hasUserInfo=" + (userInfo != null) +
                '}';
    }
}
