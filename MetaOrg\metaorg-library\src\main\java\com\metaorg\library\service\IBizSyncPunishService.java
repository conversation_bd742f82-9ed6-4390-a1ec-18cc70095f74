package com.metaorg.library.service;

import java.util.List;
import java.util.Map;
import com.metaorg.library.domain.BizSyncPunishItem;
import com.metaorg.library.domain.BizSyncPunishDetail;

/**
 * 同步处罚公示Service接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IBizSyncPunishService
{
    /**
     * 查询处罚公示项
     *
     * @param id 处罚公示项主键
     * @return 处罚公示项
     */
    public BizSyncPunishItem selectBizSyncPunishItemById(String id);

    /**
     * 查询处罚公示项列表
     *
     * @param bizSyncPunishItem 处罚公示项
     * @return 处罚公示项集合
     */
    public List<BizSyncPunishItem> selectBizSyncPunishItemList(BizSyncPunishItem bizSyncPunishItem);

    /**
     * 查询处罚详情
     *
     * @param id 处罚详情主键
     * @return 处罚详情
     */
    public BizSyncPunishDetail selectBizSyncPunishDetailById(String id);

    /**
     * 查询处罚详情列表
     *
     * @param bizSyncPunishDetail 处罚详情
     * @return 处罚详情集合
     */
    public List<BizSyncPunishDetail> selectBizSyncPunishDetailList(BizSyncPunishDetail bizSyncPunishDetail);

    /**
     * 更新处罚详情状态
     *
     * @param bizSyncPunishDetail 处罚详情
     */
    public void updateBizSyncPunishDetailStatus(BizSyncPunishDetail bizSyncPunishDetail);

    /**
     * 发起同步处罚详情请求
     *
     * @param params 同步参数
     * @return 同步结果
     */
    public Boolean syncPunishDetailsRequest(Map<String, Object> params);
} 