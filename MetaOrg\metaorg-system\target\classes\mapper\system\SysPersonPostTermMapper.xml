<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.system.mapper.SysPersonPostTermMapper">

	<resultMap type="SysPersonPostTerm" id="SysPersonPostTermResult">
		<result property="id"     column="id"      />
		<result property="personPostId"     column="person_post_id"      />
		<result property="deptLevelCode"     column="dept_level_code"      />
		<result property="postTerm"     column="post_term"      />
		<result property="deptLevelName"     column="deptLevelName"      />
	</resultMap>

	<sql id="selectPersonPostTermVo">
		select d.id, d.personPostId, d.deptLevelCode, d.post_term, d.deptLevelName
		from sys_person_post_term d
		left join sys_dict_data dd on d.dept_level_code = dd.dict_value and dd.dict_type = 'sys_dept_level'
	</sql>
	
	<select id="selectPersonPostTermByPersonPostId" parameterType="String" resultMap="SysPersonPostTermResult">
		<include refid="selectPersonPostTermVo"/>
        where d.person_post_id = #{personPostId}
	</select>

	<delete id="deletePersonPostTermByPersonPostId" parameterType="String">
		delete from sys_person_post_term where person_post_id = #{PersonPostId}
	</delete>
	
	<delete id="deletePersonPostTerm" parameterType="String">
 		delete from sys_person_post_term where person_post_id in
 		<foreach collection="array" item="PersonPostId" open="(" separator="," close=")">
 			#{PersonPostId}
        </foreach> 
 	</delete>
	
	<insert id="batchPersonPostTerm">
		insert into sys_person_post_term(id, person_post_id, dept_level_code, post_term) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.id}, #{item.personPostId},#{item.deptLevelCode},#{item.postTerm})
		</foreach>
	</insert>
	
	<delete id="deletePersonPostTermInfo" parameterType="SysPersonPostTerm">
		delete from sys_person_post_term where person_post_id=#{PersonPostId} and dept_level_code=#{deptLevelCode}
	</delete>
</mapper> 