<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth2单点登录测试页面</title>
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e5e5;
            border-radius: 5px;
        }
        .test-section h4 {
            color: #1ab394;
            margin-bottom: 15px;
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .btn-test {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .status-success {
            color: #52c41a;
        }
        .status-error {
            color: #f5222d;
        }
        .status-info {
            color: #1890ff;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="test-container">
        <div class="text-center">
            <h2><i class="fa fa-cogs"></i> OAuth2单点登录功能测试</h2>
            <p class="text-muted">测试OAuth2认证流程的各个环节</p>
            <hr/>
        </div>
        
        <!-- 配置信息测试 -->
        <div class="test-section">
            <h4><i class="fa fa-cog"></i> 配置信息测试</h4>
            <p>检查OAuth2相关配置是否正确加载</p>
            <button type="button" class="btn btn-primary btn-test" onclick="testConfig()">
                <i class="fa fa-check"></i> 测试配置
            </button>
            <div id="config-result" class="test-result" style="display:none;"></div>
        </div>
        
        <!-- 认证状态测试 -->
        <div class="test-section">
            <h4><i class="fa fa-user"></i> 认证状态测试</h4>
            <p>检查当前用户的认证状态</p>
            <button type="button" class="btn btn-info btn-test" onclick="testAuthStatus()">
                <i class="fa fa-search"></i> 检查状态
            </button>
            <div id="auth-result" class="test-result" style="display:none;"></div>
        </div>
        
        <!-- OAuth2流程测试 -->
        <div class="test-section">
            <h4><i class="fa fa-exchange"></i> OAuth2流程测试</h4>
            <p>测试完整的OAuth2认证流程</p>
            <button type="button" class="btn btn-success btn-test" onclick="testOAuth2Flow()">
                <i class="fa fa-play"></i> 开始OAuth2认证
            </button>
            <button type="button" class="btn btn-warning btn-test" onclick="testMockFlow()">
                <i class="fa fa-flask"></i> 测试模拟数据
            </button>
            <div id="oauth-result" class="test-result" style="display:none;"></div>
        </div>
        
        <!-- 网络连接测试 -->
        <div class="test-section">
            <h4><i class="fa fa-wifi"></i> 网络连接测试</h4>
            <p>测试与OAuth2服务器的网络连接</p>
            <button type="button" class="btn btn-default btn-test" onclick="testNetworkConnection()">
                <i class="fa fa-globe"></i> 测试连接
            </button>
            <div id="network-result" class="test-result" style="display:none;"></div>
        </div>
        
        <!-- 返回链接 -->
        <div class="text-center" style="margin-top: 40px;">
            <a href="/login" class="btn btn-primary">
                <i class="fa fa-arrow-left"></i> 返回登录页面
            </a>
            <a href="/index" class="btn btn-success">
                <i class="fa fa-home"></i> 返回首页
            </a>
        </div>
    </div>

    <!-- 全局js -->
    <script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
    <script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script src="../static/ruoyi/js/sso-oauth2.js" th:src="@{/ruoyi/js/sso-oauth2.js}"></script>
    
    <script>
        // 测试配置信息
        function testConfig() {
            showResult('config-result', '正在检查配置信息...', 'info');
            
            var config = {
                clientId: OAuth2.config.clientId,
                authorizeUrl: OAuth2.config.authorizeUrl,
                redirectUri: OAuth2.config.redirectUri,
                responseType: OAuth2.config.responseType,
                scope: OAuth2.config.scope
            };
            
            var result = '配置信息:\n' + JSON.stringify(config, null, 2);
            showResult('config-result', result, 'success');
        }
        
        // 测试认证状态
        function testAuthStatus() {
            showResult('auth-result', '正在检查认证状态...', 'info');
            
            $.ajax({
                url: '/sso/status',
                type: 'POST',
                success: function(response) {
                    var result = '认证状态检查结果:\n' + JSON.stringify(response, null, 2);
                    showResult('auth-result', result, response.code === 0 ? 'success' : 'error');
                },
                error: function(xhr, status, error) {
                    var result = '认证状态检查失败:\n状态码: ' + xhr.status + '\n错误: ' + error;
                    showResult('auth-result', result, 'error');
                }
            });
        }
        
        // 测试OAuth2流程
        function testOAuth2Flow() {
            showResult('oauth-result', '准备启动OAuth2认证流程...', 'info');
            
            if (confirm('即将跳转到OAuth2认证服务器，是否继续？')) {
                OAuth2.startAuth();
            } else {
                showResult('oauth-result', '用户取消了OAuth2认证流程', 'info');
            }
        }
        
        // 测试模拟数据流程
        function testMockFlow() {
            showResult('oauth-result', '正在测试模拟数据流程...', 'info');
            
            // 模拟获取token
            $.ajax({
                url: '/sso/token',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    code: 'mock_code_for_testing'
                }),
                success: function(response) {
                    var result = '模拟Token获取结果:\n' + JSON.stringify(response, null, 2);
                    showResult('oauth-result', result, response.code === 0 ? 'success' : 'error');
                    
                    // 如果token获取成功，继续测试用户信息获取
                    if (response.code === 0 && response.data && response.data.access_token) {
                        setTimeout(function() {
                            testMockUserInfo(response.data.access_token);
                        }, 1000);
                    }
                },
                error: function(xhr, status, error) {
                    var result = '模拟Token获取失败:\n状态码: ' + xhr.status + '\n错误: ' + error;
                    showResult('oauth-result', result, 'error');
                }
            });
        }
        
        // 测试模拟用户信息获取
        function testMockUserInfo(accessToken) {
            $.ajax({
                url: '/sso/userinfo',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    access_token: accessToken
                }),
                success: function(response) {
                    var currentResult = $('#oauth-result').text();
                    var newResult = currentResult + '\n\n模拟用户信息获取结果:\n' + JSON.stringify(response, null, 2);
                    showResult('oauth-result', newResult, response.code === 0 ? 'success' : 'error');
                },
                error: function(xhr, status, error) {
                    var currentResult = $('#oauth-result').text();
                    var newResult = currentResult + '\n\n模拟用户信息获取失败:\n状态码: ' + xhr.status + '\n错误: ' + error;
                    showResult('oauth-result', newResult, 'error');
                }
            });
        }
        
        // 测试网络连接
        function testNetworkConnection() {
            showResult('network-result', '正在测试网络连接...', 'info');
            
            var testUrls = [
                OAuth2.config.authorizeUrl,
                'http://10.1.145.23:8080/idp/oauth2/getToken',
                'http://10.1.145.23:8080/idp/oauth2/getUserInfo'
            ];
            
            var results = [];
            var completed = 0;
            
            testUrls.forEach(function(url, index) {
                // 使用图片加载来测试连接（简单的方法）
                var img = new Image();
                var timeout = setTimeout(function() {
                    results[index] = url + ' - 连接超时';
                    completed++;
                    if (completed === testUrls.length) {
                        showNetworkResults(results);
                    }
                }, 5000);
                
                img.onload = function() {
                    clearTimeout(timeout);
                    results[index] = url + ' - 连接成功';
                    completed++;
                    if (completed === testUrls.length) {
                        showNetworkResults(results);
                    }
                };
                
                img.onerror = function() {
                    clearTimeout(timeout);
                    results[index] = url + ' - 连接失败或不支持跨域';
                    completed++;
                    if (completed === testUrls.length) {
                        showNetworkResults(results);
                    }
                };
                
                img.src = url + '?t=' + Date.now();
            });
        }
        
        function showNetworkResults(results) {
            var result = '网络连接测试结果:\n' + results.join('\n');
            showResult('network-result', result, 'info');
        }
        
        // 显示测试结果
        function showResult(elementId, message, type) {
            var $element = $('#' + elementId);
            $element.show();
            $element.text(message);
            
            $element.removeClass('status-success status-error status-info');
            if (type === 'success') {
                $element.addClass('status-success');
            } else if (type === 'error') {
                $element.addClass('status-error');
            } else {
                $element.addClass('status-info');
            }
        }
        
        // 页面加载完成后的初始化
        $(document).ready(function() {
            console.log('OAuth2测试页面已加载');
            
            // 检查OAuth2对象是否存在
            if (typeof OAuth2 === 'undefined') {
                layer.msg('OAuth2模块未正确加载', {icon: 2});
            } else {
                console.log('OAuth2模块已正确加载');
            }
        });
    </script>
</body>
</html>
