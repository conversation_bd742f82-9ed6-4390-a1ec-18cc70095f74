package com.metaorg.library.domain;

import com.metaorg.common.annotation.Excel;
import com.metaorg.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 同步处罚公示项对象 biz_sync_punish_item
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public class BizSyncPunishItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 同步记录ID */
    @Excel(name = "同步记录ID")
    private String syncRecordId;

    /** 请求ID */
    @Excel(name = "请求ID")
    private String requestId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 奖惩机关 */
    @Excel(name = "奖惩机关")
    private String rewardPunishOrg;

    /** 发布日期 */
    @Excel(name = "发布日期")
    private String publishedDate;

    /** 来源名称 */
    @Excel(name = "来源名称")
    private String sourceName;

    /** 来源ID */
    @Excel(name = "来源ID")
    private String sourceId;

    /** 来源URL */
    @Excel(name = "来源URL")
    private String sourceUrl;

    /** 关联的同步记录 */
    private BizSyncRecord syncRecord;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setSyncRecordId(String syncRecordId)
    {
        this.syncRecordId = syncRecordId;
    }

    public String getSyncRecordId()
    {
        return syncRecordId;
    }

    public void setRequestId(String requestId)
    {
        this.requestId = requestId;
    }

    public String getRequestId()
    {
        return requestId;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }

    public void setRewardPunishOrg(String rewardPunishOrg)
    {
        this.rewardPunishOrg = rewardPunishOrg;
    }

    public String getRewardPunishOrg()
    {
        return rewardPunishOrg;
    }

    public void setPublishedDate(String publishedDate)
    {
        this.publishedDate = publishedDate;
    }

    public String getPublishedDate()
    {
        return publishedDate;
    }

    public void setSourceName(String sourceName)
    {
        this.sourceName = sourceName;
    }

    public String getSourceName()
    {
        return sourceName;
    }

    public void setSourceId(String sourceId)
    {
        this.sourceId = sourceId;
    }

    public String getSourceId()
    {
        return sourceId;
    }

    public void setSourceUrl(String sourceUrl)
    {
        this.sourceUrl = sourceUrl;
    }

    public String getSourceUrl()
    {
        return sourceUrl;
    }

    public BizSyncRecord getSyncRecord()
    {
        return syncRecord;
    }

    public void setSyncRecord(BizSyncRecord syncRecord)
    {
        this.syncRecord = syncRecord;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("syncRecordId", getSyncRecordId())
            .append("requestId", getRequestId())
            .append("title", getTitle())
            .append("rewardPunishOrg", getRewardPunishOrg())
            .append("publishedDate", getPublishedDate())
            .append("sourceName", getSourceName())
            .append("sourceId", getSourceId())
            .append("sourceUrl", getSourceUrl())
            .toString();
    }
} 