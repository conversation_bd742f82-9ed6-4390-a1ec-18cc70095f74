package com.metaorg.web.controller.library;

import java.util.*;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.library.domain.BizMessageNotifySubscriber;
import com.metaorg.library.service.IBizMessageNotifySubscriberService;
import org.apache.shiro.authz.annotation.RequiresPermissions;   
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.utils.DateUtils;

/**
 * 消息通知订阅者Controller
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Controller
@RequestMapping("/library/message/subscribe")
public class BizMessageNotifySubscribeController extends BaseController {
    private String prefix = "library/message/subscribe";

    @Autowired
    private IBizMessageNotifySubscriberService bizMessageNotifySubscriberService;

    @RequiresPermissions("library:message_notify_subscribe:view")
    @GetMapping()
    public String subscribe() {
        return prefix + "/subscribe";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizMessageNotifySubscriber bizMessageNotifySubscriber, Map<String, Object> paramMap) {
        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizMessageNotifySubscriber.getParams().put("keyword", keyword);
        }
    }

    /**
     * 查询消息通知模板列表
     */
    @RequiresPermissions("library:message_notify_subscribe:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BizMessageNotifySubscriber bizMessageNotifySubscriber, @RequestParam Map<String, Object> paramMap) {
        String subscriberId = getUserId().toString();
        bizMessageNotifySubscriber.setSubscriberId(subscriberId);

        getQueryOptions(bizMessageNotifySubscriber, paramMap);
        startPage();
        List<BizMessageNotifySubscriber> list = bizMessageNotifySubscriberService.selectBizMessageNotifySubscriberList(bizMessageNotifySubscriber);
        return getDataTable(list);
    }

    /**
     * 站内消息启用/禁用
     */
    @Log(title = "站内消息启用/禁用", businessType = BusinessType.UPDATE)
    @RequiresPermissions("library:message_notify_subscribe:edit")
    @PostMapping("/changeStatus/{templateId}")
    @ResponseBody
    public AjaxResult changeStatus(@PathVariable("templateId") String templateId, @RequestParam Map<String, Object> paramMap)
    {
        if (StringUtils.isEmpty(templateId)) {
            return error("模板标识不能为空");
        }

        String subscriberId = getUserId().toString();
        String subscriberName = getUserName();

        String[] receiverIds = new String[] { subscriberId };
        bizMessageNotifySubscriberService.deleteBizMessageNotifySubscriberByTemplateId(templateId, receiverIds);

        Boolean subscribedSystemNotify = Convert.toBool(paramMap.get("subscribed_system_notify"), false);
        Boolean subscribedSmsNotify = Convert.toBool(paramMap.get("subscribed_sms_notify"), false);
        Boolean subscribedAppNotify = Convert.toBool(paramMap.get("subscribed_app_notify"), false);

        BizMessageNotifySubscriber subscriber = new BizMessageNotifySubscriber();
        subscriber.setTemplateId(templateId);
        subscriber.setSubscriberId(subscriberId);
        subscriber.setSubscriberName(subscriberName);
        subscriber.setSubscribedSystemNotify(subscribedSystemNotify);
        subscriber.setSubscribedSmsNotify(subscribedSmsNotify);
        subscriber.setSubscribedAppNotify(subscribedAppNotify);
        subscriber.setCreateBy(subscriberName);
        subscriber.setCreateById(subscriberId);
        subscriber.setUpdateBy(subscriberName);
        subscriber.setUpdateById(subscriberId);

        Date currentDate = DateUtils.getNowDate();
        subscriber.setCreateTime(currentDate);
        subscriber.setUpdateTime(currentDate);

        return toAjax(bizMessageNotifySubscriberService.insertBizMessageNotifySubscriber(subscriber));
    }
} 