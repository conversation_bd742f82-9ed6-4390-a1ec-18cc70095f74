[project]
name = "nfra-punish-crawler"
version = "0.1.0"
description = "新疆金融监管局行政处罚信息自动化抓取和处理系统"
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "crawl4ai>=0.5.0",
    "pika>=1.3.0",
    "psycopg2-binary>=2.9.0",
    "openai>=1.0.0",
    "python-dotenv>=1.0.0",
    "asyncio-mqtt>=0.13.0",
    "aiofiles>=23.0.0",
    "loguru>=0.7.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "uuid>=1.30",
    "playwright>=1.40.0",
    "nanoid>=2.0.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "pytest-dotenv>=0.5.2",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pyinstaller>=6.16.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/nfra_crawler"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
