package com.metaorg.web.controller.library;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonTrain;
import com.metaorg.library.service.IBizPersonTrainService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 学习（培训、进修）Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/train")
public class BizPersonTrainController extends BaseController
{
    private String prefix = "library/train";

    @Autowired
    private IBizPersonTrainService bizPersonTrainService;

    @RequiresPermissions("library:train:view")
    @GetMapping()
    public String train()
    {
        return prefix + "/train";
    }

    /**
     * 查询学习（培训、进修）列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonTrain bizPersonTrain)
    {
        startPage();
        List<BizPersonTrain> list = bizPersonTrainService.selectBizPersonTrainList(bizPersonTrain);
        return getDataTable(list);
    }

    /**
     * 导出学习（培训、进修）列表
     */
    @RequiresPermissions("library:train:export")
    @Log(title = "学习（培训、进修）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonTrain bizPersonTrain)
    {
        List<BizPersonTrain> list = bizPersonTrainService.selectBizPersonTrainList(bizPersonTrain);
        ExcelUtil<BizPersonTrain> util = new ExcelUtil<BizPersonTrain>(BizPersonTrain.class);
        return util.exportExcel(list, "学习（培训、进修）数据");
    }

    /**
     * 新增学习（培训、进修）
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存学习（培训、进修）
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学习（培训、进修）", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "学习（培训、进修）", tableName = PersonTable.BIZ_PERSON_TRAIN, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonTrain bizPersonTrain)
    {
        return toAjax(bizPersonTrainService.insertBizPersonTrain(bizPersonTrain));
    }

    /**
     * 修改学习（培训、进修）
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonTrain bizPersonTrain = bizPersonTrainService.selectBizPersonTrainById(id);
        mmap.put("bizPersonTrain", bizPersonTrain);
        return prefix + "/edit";
    }

    /**
     * 修改保存学习（培训、进修）
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学习（培训、进修）", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "学习（培训、进修）", tableName = PersonTable.BIZ_PERSON_TRAIN, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonTrain bizPersonTrain)
    {
        return toAjax(bizPersonTrainService.updateBizPersonTrain(bizPersonTrain));
    }

    /**
     * 删除学习（培训、进修）
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学习（培训、进修）", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "学习（培训、进修）", tableName = PersonTable.BIZ_PERSON_TRAIN, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bizPersonTrainService.deleteBizPersonTrainByIds(ids));
    }

    /**
     * 排序学习（培训、进修）
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学习（培训、进修）", businessType = BusinessType.UPDATE)
    @PostMapping( "/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids)
    {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++)
        {
            BizPersonTrain bizPersonTrain = bizPersonTrainService.selectBizPersonTrainById(idsArray[i]);
            bizPersonTrain.setOrderNum(Convert.toLong(i + 1));
            bizPersonTrainService.updateBizPersonTrain(bizPersonTrain);
        }
        return success();
    }

    private long getMaxOrderNum(String personId)
    {
        BizPersonTrain bizPersonTrain = new BizPersonTrain();
        bizPersonTrain.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonTrain> list = bizPersonTrainService.selectBizPersonTrainList(bizPersonTrain);
        if(!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
