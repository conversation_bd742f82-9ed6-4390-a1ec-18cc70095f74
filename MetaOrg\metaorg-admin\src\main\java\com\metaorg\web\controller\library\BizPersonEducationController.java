package com.metaorg.web.controller.library;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.metaorg.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonEducation;
import com.metaorg.library.service.IBizPersonEducationService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 学历Controller
 * 
 * <AUTHOR>
 * @date 2023-05-19
 */
@Controller
@RequestMapping("/library/education")
public class BizPersonEducationController extends BaseController {
    private String prefix = "library/education";

    @Autowired
    private IBizPersonEducationService bizPersonEducationService;

    @GetMapping()
    public String education() {
        return prefix + "/education";
    }

    /**
     * 查询学历列表
     */
    @RequiresPermissions("library:person:view")
    @PostMapping("/list/{personId}")
    @ResponseBody
    public TableDataInfo list(BizPersonEducation bizPersonEducation) {
        startPage();
        List<BizPersonEducation> list = bizPersonEducationService.selectBizPersonEducationList(bizPersonEducation);
        return getDataTable(list);
    }

    /**
     * 导出学历列表
     */
    @RequiresPermissions("library:person:export")
    @Log(title = "学历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonEducation bizPersonEducation) {
        List<BizPersonEducation> list = bizPersonEducationService.selectBizPersonEducationList(bizPersonEducation);
        ExcelUtil<BizPersonEducation> util = new ExcelUtil<BizPersonEducation>(BizPersonEducation.class);
        return util.exportExcel(list, "学历数据");
    }

    /**
     * 新增学历
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap) {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存学历
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学历", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "学历", tableName = PersonTable.BIZ_PERSON_EDUCATION, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonEducation bizPersonEducation) {
        return toAjax(bizPersonEducationService.insertBizPersonEducation(bizPersonEducation));
    }

    /**
     * 修改学历
     */
    @RequiresPermissions("library:person:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPersonEducation bizPersonEducation = bizPersonEducationService.selectBizPersonEducationById(id);
        mmap.put("bizPersonEducation", bizPersonEducation);
        return prefix + "/edit";
    }

    /**
     * 修改保存学历
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学历", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "学历", tableName = PersonTable.BIZ_PERSON_EDUCATION, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonEducation bizPersonEducation) {
        return toAjax(bizPersonEducationService.updateBizPersonEducation(bizPersonEducation));
    }

    /**
     * 删除学历
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学历", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "学历", tableName = PersonTable.BIZ_PERSON_EDUCATION, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizPersonEducationService.deleteBizPersonEducationByIds(ids));
    }

    /**
     * 排序学历
     */
    @RequiresPermissions("library:person:edit")
    @Log(title = "学历", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++) {
            BizPersonEducation bizPersonEducation = bizPersonEducationService.selectBizPersonEducationById(idsArray[i]);
            bizPersonEducation.setOrderNum(Convert.toLong(i + 1));
            bizPersonEducationService.updateBizPersonEducation(bizPersonEducation);
        }
        return success();
    }

    private long getMaxOrderNum(String personId) {
        BizPersonEducation bizPersonEducation = new BizPersonEducation();
        bizPersonEducation.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonEducation> list = bizPersonEducationService.selectBizPersonEducationList(bizPersonEducation);
        if (!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
