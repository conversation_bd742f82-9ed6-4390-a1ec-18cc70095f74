<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.metaorg.library.mapper.BizSyncPunishMapper">
    
    <resultMap type="BizSyncPunishItem" id="BizSyncPunishItemResult">
        <result property="id"    column="id"    />
        <result property="syncRecordId"    column="sync_record_id"    />
        <result property="requestId"    column="request_id"    />
        <result property="title"    column="title"    />
        <result property="rewardPunishOrg"    column="reward_punish_org"    />
        <result property="publishedDate"    column="published_date"    />
        <result property="sourceName"    column="source_name"    />
        <result property="sourceId"    column="source_id"    />
        <result property="sourceUrl"    column="source_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="BizSyncPunishDetail" id="BizSyncPunishDetailResult">
        <result property="id"    column="id"    />
        <result property="punishItemId"    column="punish_item_id"    />
        <result property="requestId"    column="request_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="partyName"    column="party_name"    />
        <result property="partyPosition"    column="party_position"    />
        <result property="punishDocNo"    column="punish_doc_no"    />
        <result property="punishBasis"    column="punish_basis"    />
        <result property="violation"    column="violation"    />
        <result property="punishContent"    column="punish_content"    />
        <result property="punishDate"    column="punish_date"    />
        <result property="decisionAuthority"    column="decision_authority"    />
        <result property="partyType"    column="party_type"    />
        <result property="sourceId"    column="source_id"    />
        <result property="sourceUrl"    column="source_url"    />
        <result property="bindPersonId"    column="bind_person_id"    />
        <result property="bindPunishId"    column="bind_punish_id"    />
        <result property="bindStatus"    column="bind_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="BizSyncPunishDetail" id="BizSyncPunishDetailWithPunishItemResult" extends="BizSyncPunishDetailResult">
        <association property="punishItem"     column="punish_item_id" javaType="BizSyncPunishItem" resultMap="BizSyncPunishItemResult" />
    </resultMap>

    <sql id="selectBizSyncPunishItemVo">
        select id, sync_record_id, request_id, title, reward_punish_org, published_date, source_id, source_url, create_by, create_time, update_by, update_time from biz_sync_punish_item
    </sql>

    <sql id="selectBizSyncPunishDetailVo">
        select id, punish_item_id, request_id, order_num, party_name, party_position, violation, punish_content, punish_date, decision_authority, party_type, source_id, source_url, bind_person_id, bind_punish_id, bind_status, create_by, create_time, update_by, update_time from biz_sync_punish_detail
    </sql>

    <select id="selectBizSyncPunishItemList" parameterType="BizSyncPunishItem" resultMap="BizSyncPunishItemResult">
        <include refid="selectBizSyncPunishItemVo"/>
        <where>  
            <if test="syncRecordId != null and syncRecordId != ''"> and sync_record_id = #{syncRecordId}</if>
            <if test="title != null and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="rewardPunishOrg != null and rewardPunishOrg != ''"> and reward_punish_org like concat('%', #{rewardPunishOrg}, '%')</if>
            <if test="publishedDate != null and publishedDate != ''"> and published_date = #{publishedDate}</if>
        </where>
    </select>
    
    <select id="selectBizSyncPunishItemById" parameterType="String" resultMap="BizSyncPunishItemResult">
        <include refid="selectBizSyncPunishItemVo"/>
        where id = #{id}
    </select>

    <select id="selectBizSyncPunishDetailList" parameterType="BizSyncPunishDetail" resultMap="BizSyncPunishDetailWithPunishItemResult">
        SELECT DISTINCT
            item.title,
            item.reward_punish_org,
            item.published_date,
            detail.id,
            detail.party_name,
            detail.party_type,
            detail.party_position,
            detail.decision_authority,
            detail.punish_doc_no,
            detail.punish_date,
            detail.punish_content,
            detail.bind_status,
            detail.bind_person_id,
            detail.bind_punish_id
        FROM biz_sync_punish_item item
        INNER JOIN biz_sync_punish_detail detail ON item.id = detail.punish_item_id
        <where>
            <if test="params.keyword != null and params.keyword != ''">
                AND (
                    item.title LIKE CONCAT('%', #{params.keyword}, '%') OR
                    detail.party_name LIKE CONCAT('%', #{params.keyword}, '%') OR
                    detail.party_position LIKE CONCAT('%', #{params.keyword}, '%') OR
                    detail.decision_authority LIKE CONCAT('%', #{params.keyword}, '%') OR
                    detail.punish_doc_no LIKE CONCAT('%', #{params.keyword}, '%') OR
                    detail.punish_content LIKE CONCAT('%', #{params.keyword}, '%')
                )
            </if>
            <if test="partyName != null and partyName != ''"> and detail.party_name like concat('%', #{partyName}, '%')</if>
            <if test="partyPosition != null and partyPosition != ''"> and detail.party_position like concat('%', #{partyPosition}, '%')</if>
            <if test="punishDocNo != null and punishDocNo != ''"> and detail.punish_doc_no like concat('%', #{punishDocNo}, '%')</if>
            <if test="punishDate != null and punishDate != ''"> and detail.punish_date = #{punishDate}</if>
            <if test="decisionAuthority != null and decisionAuthority != ''"> and detail.decision_authority like concat('%', #{decisionAuthority}, '%')</if>
            <if test="partyType != null and partyType != ''"> and detail.party_type = #{partyType}</if>
            <if test="sourceUrl != null and sourceUrl != ''"> and detail.source_url like concat('%', #{sourceUrl}, '%')</if>
            <if test="params.punishDateBegin != null and params.punishDateBegin != ''"> and detail.punish_date >= #{params.punishDateBegin}</if>
            <if test="params.punishDateEnd != null and params.punishDateEnd != ''"> and detail.punish_date &lt;= #{params.punishDateEnd}</if>
            <if test="params.publishedDateBegin != null and params.publishedDateBegin != ''"> and item.published_date >= #{params.publishedDateBegin}</if>
            <if test="params.publishedDateEnd != null and params.publishedDateEnd != ''"> and item.published_date &lt;= #{params.publishedDateEnd}</if>
            <if test="params.bindStatusList != null"> and detail.bind_status in
                <foreach item="item" index="index" collection="params.bindStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY item.published_date DESC
    </select>
    
    <select id="selectBizSyncPunishDetailById" parameterType="String" resultMap="BizSyncPunishDetailResult">
        <include refid="selectBizSyncPunishDetailVo"/>
        where id = #{id}
    </select>

    <update id="updateBizSyncPunishDetailStatus" parameterType="BizSyncPunishDetail">
        update biz_sync_punish_detail
        <set>
            <if test="bindPersonId != null">bind_person_id = #{bindPersonId},</if>
            <if test="bindPunishId != null">bind_punish_id = #{bindPunishId},</if>
            <if test="bindStatus != null">bind_status = #{bindStatus},</if>
        </set>
        where id = #{id}
    </update>
</mapper> 