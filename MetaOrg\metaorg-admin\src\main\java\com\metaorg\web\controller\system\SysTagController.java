package com.metaorg.web.controller.system;

import com.metaorg.common.annotation.Log;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.domain.Ztree;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.system.domain.SysTag;
import com.metaorg.system.service.ISysTagService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.core.text.Convert;

/**
 * 标签信息
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/tag")
public class SysTagController extends BaseController
{
    private String prefix = "system/tag";

    @Autowired
    private ISysTagService tagService;

    @RequiresPermissions("system:tag:view")
    @GetMapping()
    public String tag(@RequestParam Map<String, Object> params, ModelMap mmap)
    {
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                mmap.put(entry.getKey(), entry.getValue());
            }
        }
        return prefix + "/tag";
    }

    @RequiresPermissions("system:tag:list")
    @PostMapping("/list")
    @ResponseBody
    public List<SysTag> list(SysTag tag, @RequestParam Map<String, Object> paramMap)
    {
        if (paramMap.get("parentCodeList") != null && paramMap.get("parentCodeList") != "") {
            tag.getParams().put("parentCodeList", Convert.toStrArray(paramMap.get("parentCodeList").toString()));
        }
        List<SysTag> list = tagService.selectTagList(tag);
        return list;
    }

    /**
     * 新增标签分类（一级标签）
     */
    @GetMapping("/add_first")
    public String add_first(ModelMap modelMap)
    {
        modelMap.put("maxOrderNum", getMaxOrderNum("0"));

        return prefix + "/add_first";
    }

    /**
     * 新增标签
     */
    @GetMapping("/add/{parentId}")
    public String add(@PathVariable("parentId") String parentId, ModelMap modelMap)
    {
        modelMap.put("tag", tagService.selectTagById(parentId));
        modelMap.put("maxOrderNum", getMaxOrderNum(parentId));

        return prefix + "/add";
    }

    /**
     * 新增保存标签
     */
    @Log(title = "标签管理", businessType = BusinessType.INSERT)
    @RequiresPermissions("system:tag:add")
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysTag tag)
    {
        if (!tagService.checkTagCodeUnique(tag))
        {
            return error("新增标签'" + tag.getName() + "'失败，标签编码已存在");
        }

        String userId = getUserId().toString();
        String userName = getUserName();

        tag.setCreateBy(userName);
        tag.setCreateById(userId);
        tag.setCreateTime(DateUtils.getNowDate());
        tag.setUpdateBy(userName);
        tag.setUpdateById(userId);
        tag.setUpdateTime(DateUtils.getNowDate());

        return toAjax(tagService.insertTag(tag));
    }

    /**
     * 修改标签分类（一级标签）
     */
    @RequiresPermissions("system:tag:edit")
    @GetMapping("/edit_first/{id}")
    public String edit_first(@PathVariable("id") String id, ModelMap modelMap)
    {
        SysTag tag = tagService.selectTagById(id);
        modelMap.put("tag", tag);

        return prefix + "/edit_first";
    }

    /**
     * 修改标签
     */
    @RequiresPermissions("system:tag:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap modelMap)
    {
        SysTag tag = tagService.selectTagById(id);
        if (StringUtils.isNotNull(tag) && "0".equals(id))
        {
            tag.setParentName("无");
        }
        modelMap.put("tag", tag);
        return prefix + "/edit";
    }

    /**
     * 修改保存标签
     */
    @Log(title = "标签管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("system:tag:edit")
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysTag tag)
    {
        String id = tag.getId();
        if (!tagService.checkTagCodeUnique(tag))
        {
            return error("修改标签'" + tag.getName() + "'失败，标签名称已存在");
        }
        else if (tag.getParentId().equals(id))
        {
            return error("修改标签'" + tag.getName() + "'失败，上级标签不能是自己");
        }

        String userId = getUserId().toString();
        String userName = getUserName();

        tag.setUpdateBy(userName);
        tag.setUpdateById(userId);
        tag.setUpdateTime(DateUtils.getNowDate());
        return toAjax(tagService.updateTag(tag));
    }

    /**
     * 删除
     */
    @Log(title = "删除标签", businessType = BusinessType.DELETE)
    @RequiresPermissions("system:tag:remove")
    @GetMapping("/remove/{id}")
    @ResponseBody
    public AjaxResult remove(@PathVariable("id") String id)
    {
        if (tagService.selectTagCount(id) > 0)
        {
            return AjaxResult.warn("存在下级标签,不允许删除");
        }
        return toAjax(tagService.deleteTagById(id));
    }

    /**
     * 批量删除
     */
    @Log(title = "批量删除标签", businessType = BusinessType.DELETE)
    @RequiresPermissions("system:tag:remove")
    @GetMapping("/remove")
    @ResponseBody
    public AjaxResult bulkRemove(String ids) {
        return toAjax(tagService.deleteTagByIds(ids.split(",")));
    }

    /**
     * 校验标签编码
     */
    @PostMapping("/checkTagCodeUnique")
    @ResponseBody
    public boolean checkTagCodeUnique(SysTag tag)
    {
        return tagService.checkTagCodeUnique(tag);
    }

    /**
     * 选择标签树
     * 
     * @param id 标签ID
     * @param excludeId 排除ID
     */
    @GetMapping(value = { "/selectTagTree/{id}", "/selectTagTree/{id}/{excludeId}" })
    public String selectTagTree(@PathVariable("id") String id,
            @PathVariable(value = "excludeId", required = false) String excludeId, ModelMap modelMap)
    {
        SysTag tag = tagService.selectTagById(id);
        modelMap.put("tag", tag);
        modelMap.put("excludeId", excludeId);
        return prefix + "/tree";
    }

    /**
     * 加载标签列表树（排除下级）
     */
    @GetMapping("/treeData/{excludeId}")
    @ResponseBody
    public List<Ztree> treeDataExcludeChild(@PathVariable(value = "excludeId", required = false) String excludeId)
    {
        SysTag tag = new SysTag();
        tag.setExcludeId(excludeId);
        List<Ztree> ztrees = tagService.selectTagTreeExcludeChild(tag);
        return ztrees;
    }

    /**
     * 选择标签
     * 
     * @param parentCode 父级标签编码
     */
    @GetMapping(value = {"/selectTags/{parentCode}"})
    public String selectTagTree(@PathVariable("parentCode") String parentCode, ModelMap modelMap)
    {
        SysTag sysTag = new SysTag();
        sysTag.setParentCode(parentCode);
        sysTag.setStatus(true);

        List<SysTag> parentTags = tagService.selectTagList(sysTag);
        if (!parentTags.isEmpty()) {
            modelMap.put("tagCategoryId", parentTags.get(0).getId());
        }

        List<SysTag> tags = tagService.selectTagTreeList(sysTag);
        modelMap.put("tags", tags);
        return prefix + "/select_tag";
    }

    /**
     * 标签状态修改
     */
    @Log(title = "标签管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("system:tag:edit")
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus(SysTag tag)
    {
        SysTag updateTag = new SysTag();
        updateTag.setId(tag.getId());
        updateTag.setStatus(tag.getStatus());

        String userId = getUserId().toString();
        String userName = getUserName();

        updateTag.setUpdateBy(userName);
        updateTag.setUpdateById(userId);
        updateTag.setUpdateTime(DateUtils.getNowDate());

        return toAjax(tagService.updateTag(updateTag));
    }

    private long getMaxOrderNum(String parentId)
    {
        SysTag tag = new SysTag();
        tag.setParentId(parentId);
        List<SysTag> tagList = tagService.selectTagList(tag);
        // 使用 Stream 排序
        tagList = tagList.stream().sorted(Comparator.comparing(SysTag::getOrderNum).reversed())
                .collect(Collectors.toList());
        if(!tagList.isEmpty()) {
            return tagList.get(0).getOrderNum() + 10;
        }
        return 1;
    }
} 