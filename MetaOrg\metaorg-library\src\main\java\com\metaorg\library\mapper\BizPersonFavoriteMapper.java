package com.metaorg.library.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.metaorg.library.domain.BizPersonFavorite;

/**
 * 用户收藏Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
public interface BizPersonFavoriteMapper 
{
    /**
     * 查询用户收藏
     * 
     * @param bizPersonFavorite 用户收藏信息
     * @return 用户收藏
     */
    public List<BizPersonFavorite> selectBizPersonFavoriteList(BizPersonFavorite bizPersonFavorite);

    /**
     * 查询用户收藏
     * 
     * @param userId 用户ID
     * @return 用户收藏
     */
    public List<BizPersonFavorite> selectBizPersonFavoriteByUserId(@Param("userId") Long userId, @Param("personId") String personId);

    public List<BizPersonFavorite> selectBizPersonFavoriteByPersonId(@Param("personId") String personId, @Param("userId") Long userId);

    public List<BizPersonFavorite> selectBizPersonFavoriteByPersonIds(@Param("personIds") String[] personIds, @Param("userId") Long userId);

    /**
     * 批量新增用户收藏信息
     * 
     * @param bizPersonFavoriteList 用户收藏列表
     * @return 结果
     */
    public int batchBizPersonFavorite(List<BizPersonFavorite> bizPersonFavoriteList);

    /**
     * 通过用户ID删除用户收藏
     * 
     * @param userId 用户ID
     * @param personIds 人员ID
     * @return 结果
     */
    public int deleteBizPersonFavoriteByUserId(@Param("userId") Long userId, @Param("personIds") String[] personIds);

   /**
     * 删除用户收藏
     * 
     * @param bizPersonFavorite 用户收藏信息
     * @return 结果
     */
    public int deleteBizPersonFavoriteInfo(BizPersonFavorite bizPersonFavorite);

    /**
     * 批量取消授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要删除的用户数据ID
     * @return 结果
     */
    public int deleteBizPersonFavoriteInfos(@Param("personId") String personId, @Param("userIds") Long[] userIds);

    /**
     * 通过用户ID和ID列表删除用户收藏
     * 
     * @param userId 用户ID
     * @param ids ID列表
     * @return 结果
     */
    public int deleteBizPersonFavoriteByIds(@Param("userId") Long userId, @Param("ids") String[] ids);
}
