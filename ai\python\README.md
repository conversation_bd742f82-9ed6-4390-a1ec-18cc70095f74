# 新疆金融监管局行政处罚信息自动化抓取和处理系统

## 项目简介

本项目是一个金融监管处罚信息自动化抓取和处理系统，主要功能包括：

- 从RabbitMQ接收处罚数据抓取请求
- 使用crawl4ai抓取新疆金融监管局行政处罚公示页面
- 使用AI模型清洗和结构化HTML表格数据
- 将处理后的数据存储到GaussDB数据库

## 技术栈

- **Python 3.8+**
- **crawl4ai**: 网页爬虫框架
- **RabbitMQ**: 消息队列
- **PostgreSQL/GaussDB**: 数据库
- **OpenAI API**: AI数据清洗
- **pika**: RabbitMQ Python客户端
- **psycopg2**: PostgreSQL Python客户端

## 项目结构

```text
python/
├── src/nfra_crawler/          # 主要源代码
│   ├── config/                # 配置模块
│   ├── models/                # 数据模型
│   ├── services/              # 业务服务
│   └── utils/                 # 工具函数
├── tests/                     # 测试代码
├── config/                    # 配置文件
├── logs/                      # 日志文件
├── pyproject.toml            # 项目配置
├── .env.example              # 环境变量模板
└── README.md                 # 项目说明
```

## 安装和配置

### 1. 环境要求

- Python 3.8+
- uv (Python包管理器)
- PostgreSQL/GaussDB
- RabbitMQ

### 2. 安装依赖

```bash
# 使用uv安装依赖
uv sync; uv sync --extra dev;

# 安装playwright浏览器
uv run playwright install
```

### 3. 环境配置

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑`.env`文件，配置数据库、RabbitMQ和OpenAI API等信息。

### 4. 数据库初始化

运行PRD.md中提供的SQL脚本创建数据库表。

## 使用方法

### 快速启动

#### 方式1: 使用启动脚本

**Linux/macOS:**
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

**Windows:**
```cmd
scripts\start.bat
```

#### 方式2: 手动启动

```bash
# 1. 安装依赖
uv sync

# 2. 安装playwright浏览器
uv run playwright install

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，设置必要的配置

# 4. 启动服务
uv run python -m nfra_crawler.main
```

#### 方式3: Docker部署

```bash
# 1. 构建镜像
docker-compose build

# 2. 启动所有服务
docker-compose up -d

# 3. 查看日志
docker-compose logs -f nfra-crawler
```

### 命令行工具

```bash
# 健康检查
uv run python -m nfra_crawler.cli health

# 测试爬虫功能
uv run python -m nfra_crawler.cli test-crawler

# 测试AI清洗功能
uv run python -m nfra_crawler.cli test-ai

# 测试完整流程
uv run python -m nfra_crawler.cli test-full

# 查询任务状态
uv run python -m nfra_crawler.cli status --task-id <request_id>

# Excel导出功能
uv run python -m nfra_crawler.main --export punish_data.xlsx
```

### Excel导出功能

系统支持将爬取的处罚数据直接导出到Excel文件，无需写入数据库：

```bash
# 导出到Excel文件
uv run python -m nfra_crawler.main --export output.xlsx

# 使用启动脚本导出
./scripts/start.sh --export data.xlsx        # Linux/macOS
scripts\start.bat --export data.xlsx         # Windows
```

**Excel文件结构：**
- **处罚公示列表** Sheet：包含处罚公示标识、标题、批准机构、发布日期等信息
- **处罚公示明细** Sheet：包含当事人信息、违法行为、处罚内容等详细信息

**导出特点：**
- 跳过数据库写入，直接导出到Excel
- 包含完整的爬虫和AI清洗流程
- 自动生成绝对路径和时间戳日志
- 支持文件路径验证和权限检查
```

### 运行测试

#### 基础单元测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试文件
uv run pytest tests/test_full_workflow.py

# 运行Excel导出功能测试
uv run pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_export_functionality -v
uv run pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_command_line_export_parameter -v

# 运行测试并显示覆盖率
uv run pytest --cov=src/nfra_crawler
```

#### 真实场景全流程集成测试

**前置条件：**
1. PostgreSQL数据库运行中
2. RabbitMQ服务运行中
3. 配置有效的OpenAI API密钥

**快速运行：**

```bash
# Linux/macOS
python scripts/run_integration_test.py

# Windows
scripts\run_integration_test.bat
```

**手动运行步骤：**

1. **准备测试环境**
   ```bash
   # 复制测试配置
   cp .env.test .env

   # 编辑.env文件，设置真实的数据库和API配置
   # 特别是OPENAI_API_KEY必须设置为有效值
   ```

2. **创建测试数据库**

   ```sql
   CREATE DATABASE test_metaorg;
   \c test_metaorg;
   \i sql/01_init_tables.sql;
   ```

3. **运行集成测试**

   ```bash
   # 仅测试连接和消息接收
   uv run pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_full_workflow_integration -v -s;

   # 完整流程测试（需要启动爬虫服务）
   uv run pytest tests/test_full_workflow.py::TestRealWorldIntegration::test_full_workflow_integration -v -s;
   ```

**测试流程说明：**

1. **消息发送** - 向RabbitMQ队列发送真实的爬虫请求
2. **消息接收** - 验证系统能正确接收和解析消息
3. **数据库记录** - 检查同步记录是否正确创建
4. **网页爬取** - 实际访问新疆金融监管局网站进行数据抓取
5. **AI数据清洗** - 使用OpenAI API清洗HTML表格数据
6. **数据存储** - 验证处罚信息是否正确存储到数据库

**注意事项：**

- 完整流程测试需要网络连接和有效的API密钥
- 测试可能需要几分钟时间完成
- 建议在测试环境中运行，避免影响生产数据

## 主要功能模块

### 1. RabbitMQ消息队列模块
- 接收处罚数据抓取请求
- 实现消息确认机制
- 错误处理和重试

### 2. 网页爬虫模块
- 使用crawl4ai抓取动态网页内容
- 处理JavaScript渲染
- 实现分页抓取
- 抓取详情页面内容

### 3. AI数据清洗模块
- 使用OpenAI API清洗HTML表格数据
- 结构化数据输出
- 自动识别当事人类型（个人/机构）

### 4. 数据存储模块
- 数据去重处理
- 批量插入优化
- 状态跟踪和更新

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| RABBITMQ_HOST | RabbitMQ服务器地址 | localhost |
| DATABASE_HOST | 数据库服务器地址 | localhost |
| OPENAI_API_KEY | OpenAI API密钥 | - |
| LOG_LEVEL | 日志级别 | INFO |

## 开发规范

- 遵循PEP 8代码规范
- 使用类型注解
- 编写单元测试
- 使用black格式化代码
- 使用isort排序导入

## 部署说明

### 生产环境部署

1. **环境准备**
   - PostgreSQL/GaussDB数据库
   - RabbitMQ消息队列
   - OpenAI API密钥

2. **配置文件**
   ```bash
   # 复制并编辑配置文件
   cp .env.example .env

   # 设置生产环境配置
   CRAWLER_HEADLESS=true
   LOG_LEVEL=INFO
   DATABASE_HOST=your_db_host
   RABBITMQ_HOST=your_mq_host
   OPENAI_API_KEY=your_api_key
   ```

3. **数据库初始化**
   ```bash
   # 执行数据库初始化脚本
   psql -h your_db_host -U postgres -d metaorg -f sql/01_init_tables.sql
   ```

4. **Docker部署**
   ```bash
   # 使用Docker Compose部署
   docker-compose up -d

   # 查看服务状态
   docker-compose ps

   # 查看日志
   docker-compose logs -f
   ```

### 监控和维护

1. **健康检查**
   ```bash
   # 检查应用健康状态
   curl http://localhost:8000/health

   # 或使用CLI工具
   uv run python -m nfra_crawler.cli health
   ```

2. **日志监控**
   - 应用日志：`logs/nfra_crawler.log`
   - Docker日志：`docker-compose logs`

3. **数据库监控**
   ```sql
   -- 查看同步记录统计
   SELECT status, COUNT(*) FROM biz_sync_record GROUP BY status;

   -- 查看最近的处理记录
   SELECT * FROM biz_sync_record ORDER BY create_time DESC LIMIT 10;
   ```

## 故障排除

### 常见问题

1. **爬虫无法访问目标网站**
   - 检查网络连接
   - 确认目标网站可访问
   - 检查防火墙设置

2. **AI清洗失败**
   - 检查OpenAI API密钥
   - 确认API额度充足
   - 检查网络连接

3. **数据库连接失败**
   - 检查数据库服务状态
   - 确认连接参数正确
   - 检查数据库权限

4. **RabbitMQ连接失败**
   - 检查RabbitMQ服务状态
   - 确认连接参数正确
   - 检查队列是否存在

### 性能优化

1. **爬虫性能**
   - 调整并发数量
   - 设置合适的延迟时间
   - 使用代理池（如需要）

2. **数据库性能**
   - 定期清理历史数据
   - 优化索引
   - 监控连接池使用情况

3. **内存优化**
   - 调整批处理大小
   - 监控内存使用情况
   - 设置合适的超时时间

## 许可证

MIT License
