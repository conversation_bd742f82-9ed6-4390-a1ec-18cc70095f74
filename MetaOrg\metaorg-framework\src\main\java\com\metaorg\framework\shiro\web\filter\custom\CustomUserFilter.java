package com.metaorg.framework.shiro.web.filter.custom;

import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.utils.spring.SpringUtils;
import com.metaorg.framework.shiro.session.OnlineSession;
import com.metaorg.framework.shiro.session.OnlineSessionDAO;
import com.metaorg.framework.shiro.auth.DatabaseRestoreToken;
import com.metaorg.system.service.ISysUserService;
import com.metaorg.common.constant.ShiroConstants;
import com.metaorg.common.enums.OnlineStatus;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.UserFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

/**
 * 自定义UserFilter，支持负载均衡环境下的跨服务器认证
 * 
 * <AUTHOR>
 */
public class CustomUserFilter extends UserFilter {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomUserFilter.class);
    
    // 注意：过滤器中@Autowired不生效，需要通过SpringUtils手动获取Bean
    private ISysUserService getUserService() {
        return SpringUtils.getBean(ISysUserService.class);
    }
    
    private OnlineSessionDAO onlineSessionDAO;
    
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        Subject subject = getSubject(request, response);
        
        // 首先使用父类的标准认证检查
        boolean standardResult = super.isAccessAllowed(request, response, mappedValue);
        
        // 如果标准认证通过，直接返回
        if (standardResult) {
            return true;
        }
        
        boolean databaseResult = tryRestoreAuthenticationFromDatabase(subject, request);
        
        return databaseResult;
    }
    
    private boolean tryRestoreAuthenticationFromDatabase(Subject subject, ServletRequest request) {
        try {
            if (subject == null || subject.getSession(false) == null) {
                return false;
            }
            
            Session session = subject.getSession(false);

            if (onlineSessionDAO != null) {
                Session onlineSession = onlineSessionDAO.readSession(session.getId());
                
                if (onlineSession instanceof OnlineSession) {
                    OnlineSession os = (OnlineSession) onlineSession;
                    String loginName = os.getLoginName();
                    
                    // 检查Session状态
                    if (os.getStatus() == OnlineStatus.off_line) {
                        return false;
                    }

                    if (loginName != null && !loginName.trim().isEmpty()) {
                        ISysUserService userService = getUserService();
                        SysUser dbUser = userService.selectUserByLoginName(loginName);
                        
                        if (dbUser != null && dbUser.getDelFlag().equals("0")) {
                            rebuildSubjectAuthentication(subject, dbUser);
                            
                            boolean rebuilt = subject.isAuthenticated() || subject.isRemembered();
                            return rebuilt;
                        }
                    }
                }
            }
            
            return false;
        } catch (Exception e) {
            logger.error("恢复认证信息时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    private void rebuildSubjectAuthentication(Subject subject, SysUser user) {
        try {
            // 保存当前Session信息，避免Session丢失
            Session currentSession = subject.getSession(false);

            Object onlineSessionAttr = null;
            if (currentSession != null) {
                onlineSessionAttr = currentSession.getAttribute(ShiroConstants.ONLINE_SESSION);
            }
            
            DatabaseRestoreToken restoreToken = new DatabaseRestoreToken(user.getLoginName(), user);
            
            subject.login(restoreToken);
            
            Session newSession = subject.getSession(false);
            if (newSession != null && onlineSessionAttr != null) {
                newSession.setAttribute(ShiroConstants.ONLINE_SESSION, onlineSessionAttr);
            }
            
        } catch (Exception e) {
            logger.error("认证信息重建失败: {}", e.getMessage(), e);
            throw new RuntimeException("认证失败", e);
        }
    }
    
    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        saveRequestAndRedirectToLogin(request, response);
        return false;
    }
    
    @Override
    protected void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        super.redirectToLogin(request, response);
    }
    
    public void setOnlineSessionDAO(OnlineSessionDAO onlineSessionDAO) {
        this.onlineSessionDAO = onlineSessionDAO;
    }
}