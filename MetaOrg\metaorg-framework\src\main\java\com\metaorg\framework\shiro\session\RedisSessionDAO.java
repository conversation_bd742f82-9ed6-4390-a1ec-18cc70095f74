package com.metaorg.framework.shiro.session;

import java.io.Serializable;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.apache.shiro.session.Session;
import org.apache.shiro.session.mgt.ValidatingSession;
import org.apache.shiro.session.mgt.eis.EnterpriseCacheSessionDAO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import com.metaorg.framework.redis.NamespacedRedisTemplate;

import com.metaorg.common.enums.OnlineStatus;
import com.metaorg.framework.manager.AsyncManager;
import com.metaorg.framework.manager.factory.AsyncFactory;
import com.metaorg.system.service.impl.SysUserOnlineServiceImpl;

/**
 * 针对自定义的ShiroSession的redis操作
 * 
 * <AUTHOR>
 */
@SuppressWarnings({ "rawtypes", "unchecked" })
public class RedisSessionDAO extends EnterpriseCacheSessionDAO
{
    /**
     * 同步session到数据库的周期 单位为毫秒（默认1分钟）
     */
    @Value("${shiro.session.dbSyncPeriod}")
    private int dbSyncPeriod;

    /**
     * session 在redis过期时间
     */
    private int expireTime;

    @Autowired
    private NamespacedRedisTemplate<String, Object> namespacedRedisTemplate;

    @Autowired
    private SysUserOnlineServiceImpl onlineService;

    /**
     * shiro redis 前缀
     */
    private final static String SYS_SHIRO_SESSION_ID = "shiro_session:";

    /**
     * 上次同步数据库的时间戳
     */
    private static final String LAST_SYNC_DB_TIMESTAMP = RedisSessionDAO.class.getName() + "LAST_SYNC_DB_TIMESTAMP";

    public void setExpireTime(int expireTime)
    {
        this.expireTime = expireTime;
    }

    /**
     * 根据会话ID获取会话 先从缓存中获取session
     * @param sessionId 会话ID
     * @return Session
     */
    @Override
    public Session readSession(Serializable sessionId)
    {
        String key = SYS_SHIRO_SESSION_ID + sessionId;
        try {
            Object obj = namespacedRedisTemplate.get(key);
            if (obj == null) {
                return null;
            }
            OnlineSession session = (OnlineSession)obj;
            return session;
        } catch (Exception e) {
            // 区分不同类型的异常
            if (e.getMessage() != null && e.getMessage().contains("Command timed out")) {
                // Redis超时异常，不删除数据，直接返回null
                System.err.println("Redis timeout when reading session: " + sessionId + ", error: " + e.getMessage());
                return null;
            } else {
                // 其他异常（如反序列化失败），删除损坏的数据
                System.err.println("Failed to read session from Redis, removing corrupted data: " + e.getMessage());
                System.err.println("Exception type: " + e.getClass().getName());
                
                // 打印更详细的错误信息
                if (e.getCause() != null) {
                    System.err.println("Root cause: " + e.getCause().getMessage());
                }
                
                // 如果反序列化失败，删除损坏的数据
                try {
                    namespacedRedisTemplate.delete(key);
                } catch (Exception deleteException) {
                    System.err.println("Failed to delete corrupted session: " + deleteException.getMessage());
                }
                return null;
            }
        }
    }

    /**
     * 创建会话
     *
     * @param session 会话信息
     * @return Serializable
     */
    @Override
    protected Serializable doCreate(Session session)
    {
        Serializable sessionId = generateSessionId(session);
        this.assignSessionId(session, sessionId);
        String key = SYS_SHIRO_SESSION_ID + sessionId;
        
        try {
            namespacedRedisTemplate.set(key, session);
            
            // 如果expireTime为负数（永不过期），不设置过期时间
            if (expireTime > 0) {
                namespacedRedisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
            }
            
        } catch (Exception e) {
            System.err.println("Failed to store session in Redis: " + e.getMessage());
            e.printStackTrace();
        }
        
        return sessionId;
    }

    // 更新session
    @Override
    protected void doUpdate(Session session)
    {
        // 如果会话过期/停止 没必要更新
        if (session instanceof ValidatingSession && !((ValidatingSession) session).isValid())
        {
            return;
        }
        super.doUpdate(session);
        if (session != null && session.getId() != null)
        {
            String key = SYS_SHIRO_SESSION_ID + session.getId();
            namespacedRedisTemplate.set(key, session);
            
            // 如果expireTime为负数（永不过期），不设置过期时间
            if (expireTime > 0) {
                namespacedRedisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
            }
        }
    }

    /**
     * 当会话过期/停止（如用户退出时）属性等会调用
     */
    @Override
    protected void doDelete(Session session)
    {
        OnlineSession onlineSession = (OnlineSession) session;
        if (null == onlineSession)
        {
            return;
        }
        String key = SYS_SHIRO_SESSION_ID + session.getId();
        boolean result = namespacedRedisTemplate.delete(key);
        if (result)
        {
            onlineSession.setStatus(OnlineStatus.off_line);
            onlineService.deleteOnlineById(String.valueOf(onlineSession.getId()));
        }
    }

    /**
     * 更新会话；如更新会话最后访问时间/停止会话/设置超时时间/设置移除属性等会调用
     */
    public void syncToDb(OnlineSession onlineSession)
    {
        Date lastSyncTimestamp = (Date) onlineSession.getAttribute(LAST_SYNC_DB_TIMESTAMP);
        if (lastSyncTimestamp != null)
        {
            boolean needSync = true;
            long deltaTime = onlineSession.getLastAccessTime().getTime() - lastSyncTimestamp.getTime();
            if (deltaTime < dbSyncPeriod * 60 * 1000)
            {
                // 时间差不足 无需同步
                needSync = false;
            }
            boolean isGuest = onlineSession.getUserId() == null || onlineSession.getUserId() == 0L;

            // session 数据变更了 同步
            if (isGuest == false && onlineSession.isAttributeChanged())
            {
                needSync = true;
            }

            if (needSync == false)
            {
                return;
            }
        }
        onlineSession.setAttribute(LAST_SYNC_DB_TIMESTAMP, onlineSession.getLastAccessTime());
        // 更新完后 重置标识
        if (onlineSession.isAttributeChanged())
        {
            onlineSession.resetAttributeChanged();
        }
        AsyncManager.me().execute(AsyncFactory.syncSessionToDb(onlineSession));
        String key = SYS_SHIRO_SESSION_ID + onlineSession.getId();
        namespacedRedisTemplate.set(key, onlineSession);
        
        // 如果expireTime为负数（永不过期），不设置过期时间
        if (expireTime > 0) {
            namespacedRedisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
        }
    }
}