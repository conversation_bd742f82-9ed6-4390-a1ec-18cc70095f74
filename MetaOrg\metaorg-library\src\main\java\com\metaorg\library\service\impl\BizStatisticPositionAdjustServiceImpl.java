package com.metaorg.library.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.metaorg.library.mapper.BizStatisticPositionAdjustMapper;
import com.metaorg.library.service.IBizStatisticPositionAdjustService;
import com.metaorg.library.domain.BizStatisticPositionAdjust;
import com.metaorg.library.domain.BizPersonPositionAdjustProposed;

@Service
public class BizStatisticPositionAdjustServiceImpl implements IBizStatisticPositionAdjustService {
    @Autowired
    private BizStatisticPositionAdjustMapper bizStatisticPositionAdjustMapper;

    /**
     * 查询干部调整统计记录
     * 
     * @param bizStatisticPositionAdjust 干部调整统计记录
     * @return 干部调整统计记录
     */
    @Override
    public List<BizStatisticPositionAdjust> selectBizStatisticPositionAdjust(BizStatisticPositionAdjust bizStatisticPositionAdjust) {
        return bizStatisticPositionAdjustMapper.selectBizStatisticPositionAdjust(bizStatisticPositionAdjust);
    }

    /**
     * 查询干部职务拟调整详情
     * 
     * @param bizPersonPositionAdjustProposed 干部职务拟调整详情
     * @return 干部职务拟调整详情
     */
    @Override
    public List<BizPersonPositionAdjustProposed> selectBizPersonPositionAdjustProposed(BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed) {
        return bizStatisticPositionAdjustMapper.selectBizPersonPositionAdjustProposed(bizPersonPositionAdjustProposed);
    }

    /**
     * 查询干部调整统计记录
     * 
     * @param id 干部调整统计记录主键
     * @return 干部调整统计记录
     */
    @Override
    public BizStatisticPositionAdjust selectBizStatisticPositionAdjustById(String id) {
        return bizStatisticPositionAdjustMapper.selectBizStatisticPositionAdjustById(id);
    }

    /**
     * 查询干部职务拟调整详情
     * 
     * @param id 干部职务拟调整详情主键
     * @return 干部职务拟调整详情
     */
    @Override
    public BizPersonPositionAdjustProposed selectBizPersonPositionAdjustProposedById(String id) {
        return bizStatisticPositionAdjustMapper.selectBizPersonPositionAdjustProposedById(id);
    }

    /**
     * 批量新增干部调整统计记录
     * 
     * @param bizStatisticPositionAdjustList 干部调整统计记录列表
     * @return 结果
     */
    @Override
    public int batchInsertBizStatisticPositionAdjust(List<BizStatisticPositionAdjust> bizStatisticPositionAdjustList) {
        return bizStatisticPositionAdjustMapper.batchInsertBizStatisticPositionAdjust(bizStatisticPositionAdjustList);
    }

    /**
     * 批量新增干部职务拟调整详情
     * 
     * @param bizPersonPositionAdjustProposedList 干部职务拟调整详情列表
     * @return 结果
     */
    @Override
    public int batchInsertBizPersonPositionAdjustProposed(List<BizPersonPositionAdjustProposed> bizPersonPositionAdjustProposedList) {
        return bizStatisticPositionAdjustMapper.batchInsertBizPersonPositionAdjustProposed(bizPersonPositionAdjustProposedList);
    }

    /**
     * 删除干部调整统计记录
     * 
     * @param bizStatisticPositionAdjust 干部调整统计记录
     * @return 结果
     */
    @Override
    public int deleteBizStatisticPositionAdjust(BizStatisticPositionAdjust bizStatisticPositionAdjust) {
        return bizStatisticPositionAdjustMapper.deleteBizStatisticPositionAdjust(bizStatisticPositionAdjust);
    }

    /**
     * 删除干部职务拟调整详情
     * 
     * @param bizPersonPositionAdjustProposed 干部职务拟调整详情
     * @return 结果
     */
    @Override
    public int deleteBizPersonPositionAdjustProposed(BizPersonPositionAdjustProposed bizPersonPositionAdjustProposed) {
        return bizStatisticPositionAdjustMapper.deleteBizPersonPositionAdjustProposed(bizPersonPositionAdjustProposed);
    }

    /**
     * 统计干部职务调整
     * 
     * @param bizStatisticPositionAdjust 统计参数
     * @return 结果
     */
    @Override
    public int invokeStatisticPositionAdjust(BizStatisticPositionAdjust bizStatisticPositionAdjust) {
        return bizStatisticPositionAdjustMapper.invokeStatisticPositionAdjust(bizStatisticPositionAdjust);
    }
}
