package com.metaorg.framework.utils;

import com.metaorg.common.utils.ShiroUtils;
import com.metaorg.common.utils.bean.BeanUtils;
import com.metaorg.system.domain.SysOperLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.framework.manager.AsyncManager;
import com.metaorg.framework.manager.factory.AsyncFactory;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.enums.BusinessStatus;

/**
 * 处理并记录操作日志文件
 * 
 * <AUTHOR>
 */
public class LogOperUtils
{
    public static void logOperation(SysOperLog operlog){
        try {

            SysOperLog logInfo = new SysOperLog();
            BeanUtils.copyBeanProp(logInfo, operlog);

            String ip = ShiroUtils.getIp();
            logInfo.setOperIp(ip);
            if(logInfo.getOperatorType() == null){
                logInfo.setOperatorType(1);
            }
            if(logInfo.getBusinessType() == null){
                logInfo.setBusinessType(BusinessType.OTHER.ordinal());
            }
            if(logInfo.getMethod() == null){
                logInfo.setRequestMethod("POST");
            }
            AsyncManager.me().execute(AsyncFactory.recordOper(logInfo));
        } catch (Exception ignore) {}
    }

    public static void logOperation(SysUser user, SysOperLog operlog){
        try {

            SysOperLog logInfo = new SysOperLog();
            BeanUtils.copyBeanProp(logInfo, operlog);

            String ip = ShiroUtils.getIp();
            logInfo.setOperIp(ip);
            if (user != null && user.getDept() != null && StringUtils.isNotEmpty(user.getDept().getDeptName())) {
                logInfo.setDeptName(user.getDept().getDeptName());
            }

            if(logInfo.getOperatorType() == null){
                logInfo.setOperatorType(1);
            }
            if(logInfo.getBusinessType() == null){
                logInfo.setBusinessType(BusinessType.OTHER.ordinal());
            }
            if(logInfo.getMethod() == null){
                logInfo.setRequestMethod("POST");
            }
            AsyncManager.me().execute(AsyncFactory.recordOper(logInfo));
        } catch (Exception ignore) {}
    }

    public static void logOperation(SysUser user, String url, boolean success, String title, String callMethod,
        String requestMethod, String jsonResult, String errorMsg, long costMs, String operParam) {
        try {
            SysOperLog operLog = new SysOperLog();
            String ip = ShiroUtils.getIp();
            operLog.setOperIp(ip);
            operLog.setOperatorType(1);
            operLog.setBusinessType(BusinessType.OTHER.ordinal());
            operLog.setTitle(title);
            operLog.setOperName(user != null ? user.getLoginName() : "");
            if (user != null && user.getDept() != null && StringUtils.isNotEmpty(user.getDept().getDeptName())) {
                operLog.setDeptName(user.getDept().getDeptName());
            }
            operLog.setOperUrl(url);
            operLog.setRequestMethod(StringUtils.isNotEmpty(requestMethod) ? requestMethod : "POST");
            operLog.setMethod(callMethod);
            operLog.setOperParam(StringUtils.substring(operParam, 0, 2000));
            operLog.setStatus(success ? BusinessStatus.SUCCESS.ordinal() : BusinessStatus.FAIL.ordinal());
            if (success) {
                operLog.setJsonResult(StringUtils.substring(jsonResult, 0, 2000));
            } else {
                operLog.setErrorMsg(StringUtils.substring(errorMsg, 0, 2000));
            }
            operLog.setCostTime(costMs);
            AsyncManager.me().execute(AsyncFactory.recordOper(operLog));
        } catch (Exception ignore) {
            // 忽略日志异常
        }
    }
}
