#!/usr/bin/env python3
"""
全流程集成测试运行脚本
"""

import os
import sys
import subprocess
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def setup_test_environment():
    """设置测试环境"""
    print("🔧 设置测试环境...")
    
    # 复制测试配置文件
    test_env_file = project_root / ".env.test"
    env_file = project_root / ".env"
    
    if test_env_file.exists():
        import shutil
        shutil.copy(test_env_file, env_file)
        print("✅ 测试配置文件已复制")
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(project_root / "src")
    
    return True

def check_dependencies():
    """检查依赖服务"""
    print("🔍 检查依赖服务...")
    
    # 检查PostgreSQL
    try:
        import psycopg2
        conn = psycopg2.connect(
            host=os.environ.get("DATABASE_HOST", "localhost"),
            port=os.environ.get("DATABASE_PORT", "5432"),
            database=os.environ.get("DATABASE_NAME", "test_metaorg"),
            user=os.environ.get("DATABASE_USER", "postgres"),
            password=os.environ.get("DATABASE_PWD", "password")
        )
        conn.close()
        print("✅ PostgreSQL连接正常")
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        return False
    
    # 检查RabbitMQ
    try:
        import pika
        credentials = pika.PlainCredentials(
            os.environ.get("RABBITMQ_USERNAME", "guest"),
            os.environ.get("RABBITMQ_PWD", "guest")
        )
        parameters = pika.ConnectionParameters(
            host=os.environ.get("RABBITMQ_HOST", "localhost"),
            port=int(os.environ.get("RABBITMQ_PORT", "5672")),
            virtual_host=os.environ.get("RABBITMQ_VIRTUAL_HOST", "/"),
            credentials=credentials
        )
        connection = pika.BlockingConnection(parameters)
        connection.close()
        print("✅ RabbitMQ连接正常")
    except Exception as e:
        print(f"❌ RabbitMQ连接失败: {e}")
        return False
    
    return True

def start_crawler_service():
    """启动爬虫服务"""
    print("🚀 启动爬虫服务...")
    
    try:
        # 启动爬虫服务
        cmd = [sys.executable, "-m", "nfra_crawler.main"]
        process = subprocess.Popen(
            cmd,
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=os.environ.copy()
        )
        
        # 等待服务启动
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ 爬虫服务启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 爬虫服务启动失败:")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ 启动爬虫服务时出错: {e}")
        return None

def run_integration_test():
    """运行集成测试"""
    print("🧪 运行集成测试...")
    
    try:
        # 运行测试
        cmd = [
            sys.executable, "-m", "pytest", 
            "tests/test_full_workflow.py::TestRealWorldIntegration::test_full_workflow_integration",
            "-v", "-s"
        ]
        
        result = subprocess.run(
            cmd,
            cwd=project_root,
            env=os.environ.copy(),
            capture_output=False
        )
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def main():
    """主函数"""
    print("🎯 新疆金融监管局处罚信息爬虫 - 全流程集成测试")
    print("=" * 60)
    
    # 设置测试环境
    if not setup_test_environment():
        print("❌ 测试环境设置失败")
        return 1
    
    # 检查依赖服务
    if not check_dependencies():
        print("❌ 依赖服务检查失败，请确保PostgreSQL和RabbitMQ正在运行")
        return 1
    
    # 询问是否启动爬虫服务
    start_service = input("\n是否启动爬虫服务进行完整测试？(y/N): ").lower().strip()
    
    crawler_process = None
    if start_service == 'y':
        # 设置手动测试模式
        os.environ["MANUAL_TEST_MODE"] = "true"
        
        # 启动爬虫服务
        crawler_process = start_crawler_service()
        if not crawler_process:
            print("❌ 无法启动爬虫服务")
            return 1
    else:
        print("ℹ️ 仅运行基础连接测试")
        os.environ["MANUAL_TEST_MODE"] = "false"
    
    try:
        # 运行集成测试
        success = run_integration_test()
        
        if success:
            print("\n🎉 集成测试完成！")
            return 0
        else:
            print("\n❌ 集成测试失败！")
            return 1
            
    finally:
        # 清理爬虫服务
        if crawler_process:
            print("\n🛑 停止爬虫服务...")
            crawler_process.terminate()
            try:
                crawler_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                crawler_process.kill()
            print("✅ 爬虫服务已停止")

if __name__ == "__main__":
    sys.exit(main())
