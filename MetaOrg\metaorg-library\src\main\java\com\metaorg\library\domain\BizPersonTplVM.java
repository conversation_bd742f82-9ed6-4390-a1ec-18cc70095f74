package com.metaorg.library.domain;

import com.metaorg.common.annotation.Excel;
import com.metaorg.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;
import javax.validation.constraints.*;

import com.metaorg.common.core.domain.entity.SysDept;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Objects;

/**
 * 人员信息导入对象
 *
 * <AUTHOR>
 * @date 2025-06-09
 */

@Data
@NoArgsConstructor
public class BizPersonTplVM extends BaseEntity
{
    /** 姓名 */
    @NotBlank
    @Excel(name = "*姓名", height = 27, headerColor = IndexedColors.BROWN)
    private String personName;

    /** 身份证 */
    @NotBlank
    @Excel(name = "*身份证", width = 22, headerColor = IndexedColors.BROWN)
    private String citizenId;

    /** 机构名称 */
    @Excel(name = "机构名称", headerColor = IndexedColors.BROWN)
    private String deptName;

    /** 性别 */
    @Excel(name = "性别", dictType = "sys_user_sex", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String sex;

    /** 民族 */
    @Excel(name = "民族", dictType = "biz_person_nation", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String nation;

    /** 出生年月 */
    @Excel(name = "出生年月", dateFormat = "yyyy-MM", headerColor = IndexedColors.BROWN)
    private String birthday;

    /** 籍贯 */
    @Excel(name = "籍贯", headerColor = IndexedColors.BROWN)
    private String nativePlace;

    /** 出生地 */
    @Excel(name = "出生地", headerColor = IndexedColors.BROWN)
    private String birthPlace;

    /** 户籍所在地 */
    @Excel(name = "户籍所在地", headerColor = IndexedColors.BROWN)
    private String registerPlace;

    /** 成长地 */
    @Excel(name = "成长地", headerColor = IndexedColors.BROWN)
    private String growupPlace;

    /** 婚姻状况 */
    @Excel(name = "婚姻状况", dictType = "biz_person_marital", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String maritalStatus;

    /** 健康状况 */
    @Excel(name = "健康状况", dictType = "biz_person_health", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String health;

    /** 户籍补充 */
    @Excel(name = "户籍补充", headerColor = IndexedColors.BROWN)
    private String registerExtend;

    /** 曾用名 */
    @Excel(name = "曾用名", headerColor = IndexedColors.BROWN)
    private String usedName;

    /** 个人身份特殊标识 */
    @Excel(name = "个人身份特殊标识", dictType = "biz_person_identity_flag", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String identityFlag;

    /** 领导班子成员标识 */
    @Excel(name = "领导班子成员标识", dictType = "sys_yes_no", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String leaderFlag;

    /** 全国优秀共产党员 */
    @Excel(name = "全国优秀共产党员", dictType = "sys_yes_no", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String excellentMemberOfCpc;

    /** 全国优秀县委书记 */
    @Excel(name = "全国优秀县委书记", dictType = "sys_yes_no", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String excellentMemberOfCps;

    /** 是否援疆干部 */
    @Excel(name = "是否援疆干部", dictType = "sys_yes_no", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String supportFrontierMember;

    /** 参加工作时间 */
    @Excel(name = "参加工作时间", dateFormat = "yyyy-MM", headerColor = IndexedColors.BROWN)
    private String jobDay;

    /** 进入农信社工作日期 */
    @Excel(name = "进入农信社工作日期", dateFormat = "yyyy-MM", headerColor = IndexedColors.BROWN)
    private String civilServantRegisterDay;

    /** 进入本系统工作日期 */
    @Excel(name = "进入本系统工作日期", dateFormat = "yyyy-MM", headerColor = IndexedColors.BROWN)
    private String joinSystemDay;

    /** 是否有基层工作经历 */
    @Excel(name = "是否有基层工作经历", dictType = "sys_yes_no", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String experienceInBase;

    /** 人员管理类别 */
    @Excel(name = "人员管理类别", dictType = "biz_person_manage_category", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String manageCategory;

    /** 人员管理状态 */
    @Excel(name = "人员管理状态", dictType = "biz_person_manage_status", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String manageStatus;

    /** 去世时间 */
    @Excel(name = "去世时间", dateFormat = "yyyy-MM", headerColor = IndexedColors.BROWN)
    private String deathDay;

    /** 免职时间 */
    @Excel(name = "免职时间", dateFormat = "yyyy-MM", headerColor = IndexedColors.BROWN)
    private String dismissDay;

    /** 现工作单位及职务 */
    @Excel(name = "现工作单位及职务", headerColor = IndexedColors.BROWN)
    private String jobUnit;

    /** 个人身份 */
    @Excel(name = "个人身份", dictType = "biz_person_identity", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String personIdentity;

    /** 专长 */
    @Excel(name = "专长", headerColor = IndexedColors.BROWN)
    private String speciality;

    /** 个人基本情况备注 */
    @Excel(name = "个人基本情况备注", headerColor = IndexedColors.BROWN)
    private String personNote;

    /** 离岗退养标识 */
    @Excel(name = "离岗退养标识", dictType = "sys_yes_no", comboReadDict = true, headerColor = IndexedColors.BROWN)
    private String retireLeaveFlag;

    /** 离岗退养时间 */
    @Excel(name = "离岗退养时间", dateFormat = "yyyy-MM", headerColor = IndexedColors.BROWN)
    private String retireLeaveDay;

    /** 人员对象 */
    private BizPerson person;

    /** 机构 */
    private SysDept dept;

    /** 错误信息 */
    private String errMsg;

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof BizPersonTplVM)) {
            return false;
        }

        BizPersonTplVM other = (BizPersonTplVM) o;
        return (Objects.equals(citizenId, other.citizenId));
    }

    @Override
    public int hashCode() {
        return Objects.hash(citizenId);
    }
} 