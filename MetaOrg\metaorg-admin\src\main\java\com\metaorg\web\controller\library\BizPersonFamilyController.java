package com.metaorg.web.controller.library;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Date;
import java.lang.reflect.InvocationTargetException;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.metaorg.common.core.domain.entity.SysDictData;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonFamily;
import com.metaorg.library.domain.BizPersonFamilyTplVM;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.service.IBizPersonService;
import com.metaorg.library.service.IBizPersonFamilyService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.Logical;
import com.metaorg.utils.DataScopeUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import java.util.Map;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.utils.DictUtils;
import com.metaorg.library.service.RabbitService;

/**
 * 家庭成员Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/family")
public class BizPersonFamilyController extends BaseController {
    private String prefix = "library/family";

    @Autowired
    private RabbitService rabbitService;

    @Autowired
    private IBizPersonFamilyService bizPersonFamilyService;

    @Autowired
    private IBizPersonService bizPersonService;

    @RequiresPermissions("library:family:view")
    @GetMapping()
    public String family() {
        return prefix + "/family";
    }

    /**
     * 查询家庭成员列表
     */
    @RequiresPermissions(value = { "library:family:list", "library:person:view" }, logical = Logical.OR)
    @PostMapping({ "/list/{personId}", "/list" })
    @ResponseBody
    public TableDataInfo list(BizPersonFamily bizPersonFamily, @RequestParam Map<String, Object> paramMap) {
        long deptId = -1;
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonFamily.getParams().put("deptId", deptId);
        }

        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonFamily, getSysUser(), "vpd", "", "", deptId);
        }
        // /list/{personId}不需要withPerson
        if (StringUtil.isEmpty(bizPersonFamily.getPersonId())) {
            bizPersonFamily.getParams().put("withPerson", true);
        }

        if (paramMap.get("relationshipCategories") != null && paramMap.get("relationshipCategories") != "") {
            bizPersonFamily.getParams().put("relationshipCategories",
                    Convert.toStrArray(paramMap.get("relationshipCategories").toString()));
        }

        startPage();
        List<BizPersonFamily> list = bizPersonFamilyService.selectBizPersonFamilyList(bizPersonFamily);
        return getDataTable(list);
    }

    /**
     * 导出家庭成员列表
     */
    @RequiresPermissions("library:family:export")
    @Log(title = "家庭成员及社会关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonFamily bizPersonFamily) {
        List<BizPersonFamily> list = bizPersonFamilyService.selectBizPersonFamilyList(bizPersonFamily);
        ExcelUtil<BizPersonFamily> util = new ExcelUtil<BizPersonFamily>(BizPersonFamily.class);
        return util.exportExcel(list, "家庭成员数据");
    }

    /**
     * 新增家庭成员
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap) {
        mmap.put("personId", personId);
        mmap.put("maxOrderNum", getMaxOrderNum(personId));
        return prefix + "/add";
    }

    /**
     * 新增保存家庭成员
     */
    @RequiresPermissions(value = { "library:person:edit" })
    @Log(title = "家庭成员及社会关系", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "家庭成员及社会关系", tableName = PersonTable.BIZ_PERSON_FAMILY, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonFamily bizPersonFamily) {
        return toAjax(bizPersonFamilyService.insertBizPersonFamily(bizPersonFamily));
    }

    /**
     * 修改家庭成员
     */
    @RequiresPermissions(value = { "library:person:edit", "library:family:edit" }, logical = Logical.OR)
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        BizPersonFamily bizPersonFamily = bizPersonFamilyService.selectBizPersonFamilyById(id);
        mmap.put("bizPersonFamily", bizPersonFamily);
        return prefix + "/edit";
    }

    /**
     * 修改保存家庭成员
     */
    @RequiresPermissions(value = { "library:person:edit", "library:family:edit" }, logical = Logical.OR)
    @Log(title = "家庭成员及社会关系", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "家庭成员及社会关系", tableName = PersonTable.BIZ_PERSON_FAMILY, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonFamily bizPersonFamily) {
        return toAjax(bizPersonFamilyService.updateBizPersonFamily(bizPersonFamily));
    }

    /**
     * 删除家庭成员
     */
    @RequiresPermissions(value = { "library:person:edit", "library:family:remove" }, logical = Logical.OR)
    @Log(title = "家庭成员及社会关系", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "家庭成员及社会关系", tableName = PersonTable.BIZ_PERSON_FAMILY, operationType = PersonOperationType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizPersonFamilyService.deleteBizPersonFamilyByIds(ids));
    }

    /**
     * 下载家庭成员及社会关系导入模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        List<BizPersonFamilyTplVM> list = new ArrayList<>();
        BizPersonFamilyTplVM demo1 = new BizPersonFamilyTplVM();
        BizPersonFamilyTplVM demo2 = new BizPersonFamilyTplVM();

        String appellation = "";
        List<SysDictData> appellationDicts = DictUtils.getDictCache("biz_person_appellation");
        if (appellationDicts != null && appellationDicts.size() > 0) {
            boolean found = false;
            for (SysDictData dict : appellationDicts) {
                if ("儿子".equals(dict.getDictLabel())) {
                    appellation = dict.getDictValue();
                    found = true;
                    break;
                }
            }
            if (!found) {
                appellation = appellationDicts.get(0).getDictValue();
            }
        }

        String politicsCategory = "";
        List<SysDictData> politicsCategoryDicts = DictUtils.getDictCache("biz_person_politics_category");
        if (politicsCategoryDicts != null && politicsCategoryDicts.size() > 0) {
            boolean found = false;
            for (SysDictData dict : politicsCategoryDicts) {
                if ("其他".equals(dict.getDictLabel())) {
                    politicsCategory = dict.getDictValue();
                    found = true;
                    break;
                }
            }
            if (!found) {
                politicsCategory = politicsCategoryDicts.get(0).getDictValue();
            }
        }

        demo1.setPersonName("陈天帆");
        demo1.setCitizenId("450921198767880618");
        demo1.setRelativesName("陈小帆");
        demo1.setAppellation(appellation);
        demo1.setRelativesBirthday("2000-06-07");
        demo1.setPoliticsCategory(politicsCategory);
        demo1.setWorkUnitTitle("学生");
        demo1.setRelationshipCategory("县级及以上,系统内");

        demo2.setPersonName("谢小娜");
        demo2.setCitizenId("110101199003078830");
        demo2.setRelativesName("谢小天");
        demo2.setAppellation(appellation);
        demo2.setRelativesBirthday("2001-06-07");
        demo2.setPoliticsCategory(politicsCategory);
        demo2.setWorkUnitTitle("学生");
        demo2.setRelationshipCategory("海外关系");
        list.add(demo1);
        list.add(demo2);
        ExcelUtil<BizPersonFamilyTplVM> util = new ExcelUtil<BizPersonFamilyTplVM>(BizPersonFamilyTplVM.class);
        return util.exportExcel(list, "家庭成员及社会关系导入模板");
    }

    /**
     * 导入家庭成员及社会关系数据
     */
    @RequiresPermissions("library:family:import")
    @Log(title = "家庭成员及社会关系", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BizPersonFamilyTplVM> util = new ExcelUtil<BizPersonFamilyTplVM>(BizPersonFamilyTplVM.class);
        List<BizPersonFamilyTplVM> familyList = util.importExcel(file.getInputStream());
        String userName = getUserName();
        String message = importFamily(familyList, updateSupport, userName);
        return success(message);
    }

    /**
     * 处理家庭成员及社会关系数据导入
     */
    public String importFamily(List<BizPersonFamilyTplVM> familyList, Boolean isUpdateSupport, String userName) {
        if (StringUtils.isNull(familyList) || familyList.size() == 0) {
            throw new ServiceException("导入家庭成员及社会关系数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 导入数据检查
        Boolean isValid = checkImportData(familyList);
        HashSet<String> personIdSet = new HashSet<>();
        HashMap<String, Long> maxOrderNumMap = new HashMap<>();
        if (isValid) {
            for (BizPersonFamilyTplVM checkTplVM : familyList) {
                personIdSet.add(checkTplVM.getPerson().getId());
            }
            // 根据人员ID获取每一个人的最大序号
            List<Map<String, Object>> maxOrderNumList = bizPersonFamilyService
                    .selectMaxOrderNumGroupPerson(personIdSet.toArray(new String[0]));
            for (Map<String, Object> maxOrderNum : maxOrderNumList) {
                maxOrderNumMap.put(maxOrderNum.get("person_id").toString(),
                        Long.valueOf(maxOrderNum.get("order_num").toString()));
            }
        }

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        Map<String, Integer> successPersonMap = new HashMap<>();
        List<BizPersonFamily> successFamilyList = new ArrayList<>();

        for (BizPersonFamilyTplVM pvm : familyList) {
            if (!isValid) {
                if (StringUtils.isNotEmpty(pvm.getErrMsg())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + pvm.getErrMsg());
                }
                continue;
            }

            try {
                BizPersonFamily family = BizPersonFamily.fromBizPersonFamilyTplVM(pvm);

                family.setCreateById(createUserId);
                family.setCreateBy(createUserName);
                family.setUpdateById(createUserId);
                family.setUpdateBy(createUserName);
                family.setCreateTime(currentDate);
                family.setUpdateTime(currentDate);

                // 家庭成员序号
                if (!maxOrderNumMap.containsKey(family.getPersonId())) {
                    maxOrderNumMap.put(family.getPersonId(), 1L);
                }
                family.setOrderNum(maxOrderNumMap.get(family.getPersonId()));
                maxOrderNumMap.put(family.getPersonId(), maxOrderNumMap.get(family.getPersonId()) + 1);

                int result = bizPersonFamilyService.insertBizPersonFamily(family);
                if (result > 0) {
                    successFamilyList.add(family);
                    successNum++;
                    successPersonMap.put(family.getPersonId(),
                            successPersonMap.getOrDefault(family.getPersonId(), 0) + 1);
                } else {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、姓名：" + pvm.getPersonName() + "导入失败：";
                    failureMsg.append(msg + "数据入库失败");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、身份证号 " + pvm.getCitizenId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }

        successPersonMap.forEach((personId, count) -> {
            String operationModule = "家庭成员及社会关系";
            String operationType = PersonOperationType.IMPORT.getValue();
            String tableName = PersonTable.BIZ_PERSON_FAMILY.getValue();
            String remark = createUserName + "导入了" + count + "条" + "\"" + operationModule + "\"信息。";
            rabbitService.sendPersonOperationLog(personId, operationType, operationModule, tableName, null, remark,
                    createUserId, createUserName, null);
        });

        return successMsg.toString();

    }

    /**
     * 校验导入数据
     */
    private Boolean checkImportData(List<BizPersonFamilyTplVM> familyList) {
        Boolean isOk = true;

        // 根据身份证获取人员信息
        HashMap<String, BizPerson> personMap = new HashMap<>();
        BizPerson bizPersonSearch = new BizPerson();
        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonSearch, getSysUser(), "vd", "", "", getSysUser().getDeptId());
        }
        HashSet<String> citizenIdSet = new HashSet<>();
        HashSet<String> personIdSet = new HashSet<>(); // 人员ID
        for (BizPersonFamilyTplVM pvm : familyList) {
            if (StringUtils.isNotEmpty(pvm.getCitizenId())) {
                citizenIdSet.add(pvm.getCitizenId());
            }
        }
        if (citizenIdSet.size() > 0) {
            bizPersonSearch.getParams().put("citizenIds", citizenIdSet.toArray());
            List<BizPerson> personList = bizPersonService.selectBizPersonListByCitizenIds(bizPersonSearch);
            for (BizPerson person : personList) {
                personMap.put(person.getCitizenId(), person);
                personIdSet.add(person.getId());
            }
        }

        for (BizPersonFamilyTplVM pvm : familyList) {
            String preMsg = "姓名：" + pvm.getPersonName() + " ";
            try {
                // 判空
                ArrayList<String> emptyMsgList = new ArrayList<>();
                if (StringUtils.isEmpty(pvm.getPersonName()))
                    emptyMsgList.add("姓名");
                if (StringUtils.isEmpty(pvm.getCitizenId()))
                    emptyMsgList.add("身份证");
                if (StringUtils.isEmpty(pvm.getRelativesName()))
                    emptyMsgList.add("关系人姓名");
                if (StringUtils.isEmpty(pvm.getAppellation()))
                    emptyMsgList.add("称谓");
                if (emptyMsgList.size() > 0) {
                    isOk = false;
                    pvm.setErrMsg(preMsg + StringUtils.join(emptyMsgList, "、") + "不能为空");
                    continue;
                }

                pvm.setAppellation(DictUtils.getDictLabel("biz_person_appellation", pvm.getAppellation()));
                if (StringUtils.isEmpty(pvm.getAppellation())) {
                    isOk = false;
                    pvm.setErrMsg(preMsg + "称谓格式不正确，请从下拉列表中选中相应值");
                    continue;
                }

                pvm.setPoliticsCategory(
                        DictUtils.getDictLabel("biz_person_politics_category", pvm.getPoliticsCategory()));
                if (StringUtils.isEmpty(pvm.getPoliticsCategory())) {
                    isOk = false;
                    pvm.setErrMsg(preMsg + "政治面貌格式不正确，请从下拉列表中选中相应值");
                    continue;
                }

                // 出生日期格式校验
                if (StringUtils.isNotEmpty(pvm.getRelativesBirthday())) {
                    Date relativesBirthday = DateUtils.dateTime("yyyy-MM-dd", pvm.getRelativesBirthday());
                    if (relativesBirthday == null) {
                        isOk = false;
                        pvm.setErrMsg(preMsg + "出生日期格式不正确，正确示例: 2025-12-12");
                        continue;
                    }
                    pvm.setRelativesBirthday(DateUtils.parseDateToStr("yyyy-MM-dd", relativesBirthday));
                }

                // 检查人员信息
                if (!personMap.containsKey(pvm.getCitizenId())) {
                    isOk = false;
                    pvm.setErrMsg(preMsg + "人员信息不存在，检查姓名、身份证");
                    continue;
                } else {
                    if (!personMap.get(pvm.getCitizenId()).getName().equals(pvm.getPersonName())) {
                        isOk = false;
                        pvm.setErrMsg(preMsg + "人员信息不存在，检查姓名、身份证");
                        continue;
                    }
                }
                pvm.setPerson(personMap.get(pvm.getCitizenId()));

                // 关系类别
                String[] relationshipCategories = pvm.getRelationshipCategory().split(",");
                String relationshipCategoryLabels = DictUtils.getDictLabels("biz_person_family_relationship_category");
                for (int i = 0; i < relationshipCategories.length; i++) {
                    if (StringUtils.isEmpty(DictUtils.getDictValue("biz_person_family_relationship_category",
                            relationshipCategories[i]))) {
                        isOk = false;
                        pvm.setErrMsg(preMsg + "关系类别格式不正确，应为以下值:" + relationshipCategoryLabels);
                        break;
                    }
                }
            } catch (Exception e) {
                isOk = false;
                String msg = preMsg + "导入失败：";
                String exMsg = e.getMessage();
                if (StringUtils.isEmpty(exMsg) && e instanceof InvocationTargetException) {
                    exMsg = ((InvocationTargetException) e).getTargetException().getMessage();
                }
                if (StringUtils.isEmpty(exMsg)) {
                    exMsg = "数据校验失败";
                }
                pvm.setErrMsg(msg + exMsg);
            }
        }

        return isOk;
    }

    /**
     * 排序家庭成员
     */
    @RequiresPermissions(value = { "library:family:edit", "library:person:edit" }, logical = Logical.OR)
    @Log(title = "家庭成员及社会关系", businessType = BusinessType.UPDATE)
    @PostMapping("/resortItem")
    @ResponseBody
    public AjaxResult resortItem(String ids) {
        String[] idsArray = Convert.toStrArray(ids);
        for (int i = 0; i < idsArray.length; i++) {
            BizPersonFamily bizPersonFamily = bizPersonFamilyService.selectBizPersonFamilyById(idsArray[i]);
            bizPersonFamily.setOrderNum(Convert.toLong(i + 1));
            bizPersonFamilyService.updateBizPersonFamily(bizPersonFamily);
        }
        return success();
    }

    private long getMaxOrderNum(String personId) {
        BizPersonFamily bizPersonFamily = new BizPersonFamily();
        bizPersonFamily.setPersonId(personId);
        PageHelper.startPage(1, 1, "order_num desc").setReasonable(true);
        List<BizPersonFamily> list = bizPersonFamilyService.selectBizPersonFamilyList(bizPersonFamily);
        if (!list.isEmpty()) {
            return list.get(0).getOrderNum() + 1;
        }
        return 1;
    }
}
