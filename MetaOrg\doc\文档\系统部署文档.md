



# 干部信息管理系统部署文档



## 1、准备工作

```
JDK >= 1.8 (推荐1.8版本)
Mysql >= 5.7.0 (推荐5.7版本, 信创可使用mariaDb)
Maven >= 3.0（开发环境使用）
```

## 2、部署文件结构

```yaml
/metaorg:                        #系统部署根目录
   config:                       #系统配置目录
      application.yml            #系统参数配置文件
      application-druid.yml      #系统数据源配置文件
      rmb_template.docx          #干部任免表生产word与pdf模板文件
   fonts:
      chinese/STSONG.TTF         #Linux下中文字体文件，用于解决系统pdf生成时乱码问题
   service:
      etc/systemd/system/metaorg.service            #Linux下系统自动运行脚本
   metaorg-admin.jar             #系统主程序jar包，支持./metaorg-admin.jar直接运行
   metaorg.sh                    #系统服务管理脚本
   sql:
      metaorg.sql                #系统数据库初始化脚本
   uploadPath:		             #系统文件上传目录
```



## 3、数据库建立与导入

### 3-1、建立数据库

使用数据库管理工具或ssh进入数据库服务器建立数据库：

```sql
-- 数据库建立命令：
CREATE DATABASE `metaorg` CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_general_ci';
```

### 3-2、导入数据库初始化脚本

使用数据库管理工具或ssh进入数据库服务器导入脚本：

```sql
-- 导入数据库命令：
use metaorg;
source /metaorg/sql/metaorg.sql	
```





## 4、干部信息管理系统部署

```yml
1、将部署文件中metaorg目录整体拷贝到服务器的/metaorg目录;
2、拷贝中文字体：cp -rf /metaorg/fonts/chinese /usr/share/fonts/
3、设置目录权限：
   a、chmod a+x /metaorg/metaorg-admin.jar
   b、chmod -R 777 /metaorg/uploadPath
4、修改系统的数据库连接与用户：
   vi /metaorg/config/application-druid.yml
   # 数据源配置
   spring:
	   datasource:
		   druid:
			   # 主库数据源
			   master:
				   url: ***********************************?[...]
                   username: metaorg
                   password: metaorg					   
5、测试系统是否正常运行
   ./metaorg-admin.jar
   根据日志判断系统是否正常运行。如正常启动，可通过http://[ip]:8488访问干部信息管理系统；如遇异常根据日志调整相关配置。
6、拷贝服务脚本：cp /metaorg/service/etc/systemd/system/metaorg.service /etc/systemd/system
7、使用服务管理命令管理metaorg服务
   systemctl enable metaorg
   systemctl start metaorg
   systemctl stop metaorg
   systemctl restart metaorg
   systemctl status metaorg
```

# 5、以非root用户管理干部信息管理系统

```shell
使用./metaorg.sh管理服务器的运行。
./metaorg.sh start
./metaorg.sh restart
./metaorg.sh status
./metaorg.sh stop
```





