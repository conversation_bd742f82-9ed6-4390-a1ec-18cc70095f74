/**
 * OAuth2单点登录前端处理脚本
 * 
 * <AUTHOR>
 * @version 1.0
 */

var OAuth2 = {
    
    // OAuth2配置参数
    config: {
        clientId: 'metaorg_client',
        authorizeUrl: 'http://10.1.145.23:8080/idp/oauth2/authorize',
        redirectUri: window.location.origin + '/sso/callback',
        responseType: 'code',
        scope: 'read write'
    },
    
    /**
     * 初始化OAuth2认证
     */
    init: function() {
        console.log('OAuth2认证模块初始化');
        
        // 检查URL参数
        this.checkUrlParams();
        
        // 绑定事件
        this.bindEvents();
    },
    
    /**
     * 检查URL参数，处理认证回调
     */
    checkUrlParams: function() {
        var urlParams = new URLSearchParams(window.location.search);
        var code = urlParams.get('code');
        var state = urlParams.get('state');
        var error = urlParams.get('error');
        
        if (error) {
            console.error('OAuth2认证错误:', error);
            this.showError('认证失败: ' + error);
            return;
        }
        
        if (code && state) {
            console.log('检测到OAuth2回调参数，开始处理认证');
            this.handleAuthCallback(code, state);
        }
    },
    
    /**
     * 发起OAuth2认证
     */
    startAuth: function() {
        console.log('开始OAuth2认证流程');
        
        // 生成state参数用于防CSRF攻击
        var state = this.generateState();
        sessionStorage.setItem('oauth_state', state);
        
        // 构建认证URL
        var authUrl = this.buildAuthUrl(state);
        
        console.log('跳转到认证服务器:', authUrl);
        
        // 跳转到认证服务器
        window.location.href = authUrl;
    },
    
    /**
     * 构建认证URL
     */
    buildAuthUrl: function(state) {
        var params = new URLSearchParams({
            client_id: this.config.clientId,
            redirect_uri: this.config.redirectUri,
            response_type: this.config.responseType,
            state: state,
            scope: this.config.scope
        });
        
        return this.config.authorizeUrl + '?' + params.toString();
    },
    
    /**
     * 生成随机state参数
     */
    generateState: function() {
        return 'oauth_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    },
    
    /**
     * 处理认证回调
     */
    handleAuthCallback: function(code, state) {
        console.log('处理OAuth2认证回调');
        
        // 验证state参数
        var storedState = sessionStorage.getItem('oauth_state');
        if (!storedState || storedState !== state) {
            console.error('State参数验证失败');
            this.showError('认证状态验证失败，可能存在安全风险');
            return;
        }
        
        // 清除存储的state
        sessionStorage.removeItem('oauth_state');
        
        // 显示处理中状态
        this.showProcessing();
        
        // 发送code到后端处理
        this.exchangeCodeForToken(code);
    },
    
    /**
     * 使用授权码换取token
     */
    exchangeCodeForToken: function(code) {
        console.log('使用授权码换取访问令牌');
        
        $.ajax({
            url: '/sso/token',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                code: code,
                grant_type: 'authorization_code'
            }),
            success: function(response) {
                console.log('Token获取成功');
                OAuth2.handleTokenResponse(response);
            },
            error: function(xhr, status, error) {
                console.error('Token获取失败:', error);
                OAuth2.showError('获取访问令牌失败，请重试');
            }
        });
    },
    
    /**
     * 处理token响应
     */
    handleTokenResponse: function(response) {
        if (response.code === 0 && response.data && response.data.access_token) {
            console.log('Token验证成功，准备获取用户信息');
            this.getUserInfo(response.data.access_token);
        } else {
            console.error('Token响应无效:', response);
            this.showError(response.msg || '令牌验证失败');
        }
    },
    
    /**
     * 获取用户信息
     */
    getUserInfo: function(accessToken) {
        console.log('获取用户信息');
        
        $.ajax({
            url: '/sso/userinfo',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                access_token: accessToken
            }),
            success: function(response) {
                console.log('用户信息获取成功');
                OAuth2.handleUserInfo(response);
            },
            error: function(xhr, status, error) {
                console.error('用户信息获取失败:', error);
                OAuth2.showError('获取用户信息失败，请重试');
            }
        });
    },
    
    /**
     * 处理用户信息
     */
    handleUserInfo: function(response) {
        if (response.code === 0 && response.data) {
            console.log('用户信息验证成功，准备登录系统');
            this.loginSystem(response.data);
        } else {
            console.error('用户信息响应无效:', response);
            this.showError(response.msg || '用户信息验证失败');
        }
    },
    
    /**
     * 登录系统
     */
    loginSystem: function(userInfo) {
        console.log('登录系统中...', userInfo);
        
        // 显示登录成功状态
        this.showSuccess('认证成功，正在登录系统...');
        
        // 延迟跳转，让用户看到成功信息
        setTimeout(function() {
            window.location.href = '/index';
        }, 1500);
    },
    
    /**
     * 显示处理中状态
     */
    showProcessing: function() {
        if (typeof layer !== 'undefined') {
            layer.msg('正在处理认证信息...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        } else {
            console.log('正在处理认证信息...');
        }
    },
    
    /**
     * 显示成功信息
     */
    showSuccess: function(message) {
        if (typeof layer !== 'undefined') {
            layer.closeAll();
            layer.msg(message, {
                icon: 1,
                time: 2000
            });
        } else {
            alert(message);
        }
    },
    
    /**
     * 显示错误信息
     */
    showError: function(message) {
        // 重置登录按钮状态
        this.resetLoginButton();
        
        var errorMsg = message || '认证过程中发生未知错误';
        
        if (typeof layer !== 'undefined') {
            layer.closeAll();
            layer.confirm(errorMsg + '<br/><br/>是否重新尝试认证？', {
                icon: 2,
                title: 'OAuth2认证失败',
                btn: ['重新认证', '普通登录', '取消']
            }, function(index) {
                layer.close(index);
                OAuth2.startAuth();
            }, function(index) {
                layer.close(index);
                window.location.href = '/login';
            }, function(index) {
                layer.close(index);
            });
        } else {
            var retry = confirm('OAuth2认证失败: ' + errorMsg + '\n\n是否重新尝试认证？');
            if (retry) {
                this.startAuth();
            }
        }
        
        // 记录错误日志
        console.error('OAuth2认证错误:', message);
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        var self = this;
        
        // 绑定SSO登录按钮
        $(document).on('click', '.btn-sso-login', function(e) {
            e.preventDefault();
            var $btn = $(this);
            
            // 防止重复点击
            if ($btn.hasClass('loading')) {
                return false;
            }
            
            // 显示加载状态
            $btn.addClass('loading')
                .find('i').removeClass('fa-shield').addClass('fa-spinner');
            
            // 开始认证
            self.startAuth();
        });
        
        // 绑定重试按钮
        $(document).on('click', '.btn-sso-retry', function(e) {
            e.preventDefault();
            self.startAuth();
        });
        
        // 绑定取消按钮
        $(document).on('click', '.btn-cancel-sso', function(e) {
            e.preventDefault();
            self.resetLoginButton();
            if (typeof layer !== 'undefined') {
                layer.msg('已取消OAuth2认证', { icon: 0 });
            }
        });
        
        // 页面卸载时清理
        $(window).on('beforeunload', function() {
            // 清理可能的定时器或请求
            self.resetLoginButton();
        });
    },
    
    /**
     * 重置登录按钮状态
     */
    resetLoginButton: function() {
        $('.btn-sso-login').removeClass('loading')
            .find('i').removeClass('fa-spinner').addClass('fa-shield');
    },
    
    /**
     * 检查认证状态
     */
    checkAuthStatus: function() {
        $.ajax({
            url: '/sso/status',
            type: 'POST',
            success: function(response) {
                if (response.code === 0) {
                    console.log('用户已登录:', response.data);
                    return true;
                } else {
                    console.log('用户未登录');
                    return false;
                }
            },
            error: function() {
                console.log('无法获取认证状态');
                return false;
            }
        });
    },
    
    /**
     * 退出登录
     */
    logout: function() {
        // 清理本地存储
        sessionStorage.removeItem('oauth_state');
        localStorage.removeItem('oauth_token');
        
        // 跳转到登录页
        window.location.href = '/login';
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    OAuth2.init();
});

// 导出到全局作用域
window.OAuth2 = OAuth2;
