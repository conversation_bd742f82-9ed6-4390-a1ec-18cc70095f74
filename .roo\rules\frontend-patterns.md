---
description: 前端开发模式
globs: 
alwaysApply: false
---
# MetaOrg 前端开发模式

## 前端技术栈
- **模板引擎**: Thymeleaf 3.x
- **UI框架**: Bootstrap 3.x
- **JavaScript库**: jQuery 3.x
- **表格组件**: Bootstrap Table
- **弹框组件**: Layer
- **树形组件**: zTree
- **图表组件**: ECharts

## 页面结构模式

### 标准列表页面结构
```html
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <!-- 标准头部引用 -->
    <th:block th:include="include :: header('页面标题')" />
    <th:block th:include="include :: layout-latest-css"/>
</head>
<body class="gray-bg">
    <!-- 左侧树形导航（可选） -->
    <div class="ui-layout-west">...</div>
    
    <!-- 主内容区域 -->
    <div class="ui-layout-center">
        <!-- 搜索表单 -->
        <form id="formId">...</form>
        
        <!-- 工具栏 -->
        <div class="btn-group-sm" id="toolbar">...</div>
        
        <!-- 数据表格 -->
        <table id="bootstrap-table"></table>
    </div>
    
    <!-- 脚本引用 -->
    <th:block th:include="include :: footer" />
</body>
</html>
```

### JavaScript 模式
参考文件: [MetaOrg/metaorg-admin/src/main/resources/templates/library/check/check.html](mdc:MetaOrg/metaorg-admin/src/main/resources/templates/library/check/check.html)

```javascript
// Thymeleaf 内联 JavaScript 模式
<script th:inline="javascript">
    /*<![CDATA[*/
    // 权限变量定义
    const editFlag = [[${@permission.hasPermi('module:function:edit')}]];
    const removeFlag = [[${@permission.hasPermi('module:function:remove')}]];
    
    // 字典数据
    const dictData = [[${@dict.getType('dict_type')}]];
    
    // 表格初始化
    $(function() {
        const options = {
            toolbar: "toolbar-id",
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            columns: [...]
        };
        $.table.init(options);
    });
    /*]]>*/
</script>
```

## 组件使用模式

### Bootstrap Table 配置
- 支持分页、排序、搜索
- 固定列、拖拽调整列宽
- 批量操作选择
- 自定义列格式化

### 权限控制显示
```html
<!-- Shiro 权限标签 -->
<a class="btn btn-info" shiro:hasPermission="module:function:add">新增</a>
<a class="btn btn-danger" shiro:hasPermission="module:function:remove">删除</a>
```

### 字典数据显示
```javascript
// 字典值格式化
formatter: function(value, row, index) {
    return $.table.selectDictLabel(dictData, value);
}
```

### 模态框表单
- 使用 Layer 插件创建弹框
- 表单验证使用 jQuery Validate
- AJAX 提交表单数据

## 样式规范

### CSS 类命名
- 使用 Bootstrap 标准类名
- 自定义样式使用语义化命名
- 表格相关样式: `.select-table`, `.table-striped`

### 响应式设计
- 支持移动端自适应
- 小屏幕自动折叠侧边栏
- 表格支持水平滚动

## 国际化支持
- 使用 Thymeleaf 消息国际化
- JavaScript 中通过后端传递国际化文本
- 日期时间格式本地化

## 性能优化
- CSS/JS 文件压缩
- 图片懒加载
- 表格虚拟滚动（大数据量）
- AJAX 请求缓存机制
