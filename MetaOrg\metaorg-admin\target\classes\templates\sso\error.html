<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>OAuth2认证错误 - 干部管理信息系统</title>
    <meta name="description" content="干部管理信息系统OAuth2认证错误页面">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.7.7}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">
        .sso-error-panel {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            margin: 100px auto;
        }
        .error-icon {
            font-size: 64px;
            color: #f5222d;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 24px;
            color: #262626;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .error-message {
            font-size: 16px;
            color: #595959;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        .error-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
        }
        .btn-retry {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
            padding: 8px 24px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn-retry:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            color: white;
            text-decoration: none;
        }
        .btn-login {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
            padding: 8px 24px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn-login:hover {
            background: #73d13d;
            border-color: #73d13d;
            color: white;
            text-decoration: none;
        }
        .system-info {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #8c8c8c;
            font-size: 12px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="sso-error-panel">
        <div class="error-icon">
            <i class="fa fa-exclamation-triangle"></i>
        </div>
        <div class="error-title">OAuth2认证失败</div>
        <div class="error-message" th:text="${error ?: '认证过程中发生未知错误，请重试或联系管理员'}">
            认证过程中发生未知错误，请重试或联系管理员
        </div>
        <div class="error-actions">
            <a href="/sso/entry" class="btn-retry">
                <i class="fa fa-refresh"></i> 重新认证
            </a>
            <a href="/login" class="btn-login">
                <i class="fa fa-sign-in"></i> 普通登录
            </a>
        </div>
    </div>
    
    <div class="system-info">
        <p>干部管理信息系统 - OAuth2单点登录</p>
    </div>

    <!-- 全局js -->
    <script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 5秒后自动隐藏错误信息并提供重试选项
            setTimeout(function() {
                $('.error-message').fadeOut(300, function() {
                    $(this).text('您可以尝试重新认证或使用普通登录方式').fadeIn(300);
                });
            }, 5000);
        });
    </script>
</body>
</html>
