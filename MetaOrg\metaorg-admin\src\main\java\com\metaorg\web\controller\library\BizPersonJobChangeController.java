package com.metaorg.web.controller.library;

import java.util.*;

import com.metaorg.common.core.domain.entity.SysDept;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.library.domain.BizPerson;
import com.metaorg.library.service.IBizPersonService;
import com.metaorg.system.service.ISysDeptService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.annotation.PersonOperationLog;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.enums.PersonOperationType;
import com.metaorg.common.enums.PersonTable;
import com.metaorg.library.domain.BizPersonJobChange;
import com.metaorg.library.service.IBizPersonJobChangeService;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.poi.ExcelUtil;
import com.metaorg.common.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.RequestParam;
import com.metaorg.utils.DataScopeUtils;

/**
 * 职务变动Controller
 * 
 * <AUTHOR>
 * @date 2023-05-22
 */
@Controller
@RequestMapping("/library/job_change")
public class BizPersonJobChangeController extends BaseController
{
    private String prefix = "library/job_change";

    @Autowired
    private IBizPersonJobChangeService bizPersonJobChangeService;

    @Autowired
    private IBizPersonService bizPersonService;

    @Autowired
    private ISysDeptService deptService;

    @RequiresPermissions("library:job_change:view")
    @GetMapping()
    public String job_change()
    {
        return prefix + "/job_change";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizPersonJobChange bizPersonJobChange, Map<String, Object> paramMap){
        long deptId = getUserDeptId();
        if (paramMap.get("deptId") != null && paramMap.get("deptId") != "") {
            deptId = Long.parseLong(paramMap.get("deptId").toString());
            bizPersonJobChange.getParams().put("deptId", deptId);
        }
        
        if (paramMap.get("approvalStatusList") != null && paramMap.get(("approvalStatusList")) != "") {
            bizPersonJobChange.getParams().put("approvalStatusList", Convert.toStrArray(paramMap.get("approvalStatusList").toString()));
        }

        if (!getSysUser().isAdmin()) {
            DataScopeUtils.dataScopeFilter(bizPersonJobChange, getSysUser(), "vd", "", "", deptId);
        }
    }

    /**
     * 查询职务变动列表
     */
    @RequiresPermissions("library:job_change:list")
    @PostMapping({"/list/{personId}", "/list"})
    @ResponseBody
    public TableDataInfo list(BizPersonJobChange bizPersonJobChange, @RequestParam Map<String, Object> paramMap)
    {
        getQueryOptions(bizPersonJobChange, paramMap);
        startPage();
        List<BizPersonJobChange> list = bizPersonJobChangeService.selectBizPersonJobChangeList(bizPersonJobChange);
        return getDataTable(list);
    }

    /**
     * 导出职务变动列表
     */
    @RequiresPermissions("library:job_change:export")
    @Log(title = "职务变动情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BizPersonJobChange bizPersonJobChange)
    {
        List<BizPersonJobChange> list = bizPersonJobChangeService.selectBizPersonJobChangeList(bizPersonJobChange);
        ExcelUtil<BizPersonJobChange> util = new ExcelUtil<BizPersonJobChange>(BizPersonJobChange.class);
        return util.exportExcel(list, "职务变动数据");
    }

    /**
     * 新增职务变动
     */
    @GetMapping("/add/{personId}")
    public String add(@PathVariable("personId") String personId, ModelMap mmap)
    {
        mmap.put("personId", personId);
        return prefix + "/add";
    }

    /**
     * 新增保存职务变动
     */
    @RequiresPermissions("library:job_change:add")
    @Log(title = "职务变动情况", businessType = BusinessType.INSERT)
    @PersonOperationLog(module = "职务变动情况", tableName = PersonTable.BIZ_PERSON_JOB_CHANGE, operationType = PersonOperationType.CREATE)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizPersonJobChange bizPersonJobChange)
    {
        BizPerson bizPerson = bizPersonService.selectBizPersonById(bizPersonJobChange.getPersonId());
        if(bizPerson != null) {
            SysDept dept = deptService.selectDeptById(bizPerson.getDeptId());
            if(StringUtils.isNotNull(dept)) {
                bizPersonJobChange.setFromUnitId(dept.getDeptId().toString());
                bizPersonJobChange.setApprovalStatus("INPROGRESS");
            }
        }
        return toAjax(bizPersonJobChangeService.insertBizPersonJobChange(bizPersonJobChange));
    }

    /**
     * 修改职务变动
     */
    @RequiresPermissions("library:job_change:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonJobChange bizPersonJobChange = bizPersonJobChangeService.selectBizPersonJobChangeById(id);
        mmap.put("bizPersonJobChange", bizPersonJobChange);
        return prefix + "/edit";
    }

    /**
     * 修改保存职务变动
     */
    @RequiresPermissions("library:job_change:edit")
    @Log(title = "职务变动情况", businessType = BusinessType.UPDATE)
    @PersonOperationLog(module = "职务变动情况", tableName = PersonTable.BIZ_PERSON_JOB_CHANGE, operationType = PersonOperationType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizPersonJobChange bizPersonJobChange)
    {
        BizPersonJobChange bizPersonJobChangeCheck = bizPersonJobChangeService.selectBizPersonJobChangeById(bizPersonJobChange.getId());
        if(bizPersonJobChangeCheck.getApprovalStatus().equals("APPROVED") || bizPersonJobChangeCheck.getApprovalStatus().equals("REJECTED")) {
            return error("职务变动已审核，不允许再编辑，请刷新页面查看最新状态！");
        }
        return toAjax(bizPersonJobChangeService.updateBizPersonJobChange(bizPersonJobChange));
    }

    /**
     * 审核职务变动
     */
    @RequiresPermissions("library:job_change:edit")
    @GetMapping("/check/{id}")
    public String check(@PathVariable("id") String id, ModelMap mmap)
    {
        BizPersonJobChange bizPersonJobChange = bizPersonJobChangeService.selectBizPersonJobChangeById(id);
        mmap.put("bizPersonJobChange", bizPersonJobChange);
        return prefix + "/check";
    }

    /**
     * 审核保存职务变动
     */
    @RequiresPermissions("library:job_change:edit")
    @Log(title = "职务变动情况", businessType = BusinessType.UPDATE)
    @PostMapping("/check")
    @ResponseBody
    public AjaxResult checkSave(BizPersonJobChange bizPersonJobChange)
    {
        if(bizPersonJobChange.getApprovalStatus().equals("APPROVED")) {
            BizPerson bizPerson = bizPersonService.selectBizPersonById(bizPersonJobChange.getPersonId());
            bizPerson.setDeptId(Convert.toLong(bizPersonJobChange.getUnitId()));
            bizPersonService.updateBizPerson(bizPerson);
        }
        return toAjax(bizPersonJobChangeService.updateBizPersonJobChange(bizPersonJobChange));
    }

    /**
     * 删除职务变动
     */
    @RequiresPermissions("library:job_change:remove")
    @Log(title = "职务变动情况", businessType = BusinessType.DELETE)
    @PersonOperationLog(module = "职务变动情况", tableName = PersonTable.BIZ_PERSON_JOB_CHANGE, operationType = PersonOperationType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        String[] idList = Convert.toStrArray(ids);
        for(String id: idList) {
            BizPersonJobChange bizPersonJobChange = bizPersonJobChangeService.selectBizPersonJobChangeById(id);
            if(bizPersonJobChange.getApprovalStatus().equals("APPROVED") || bizPersonJobChange.getApprovalStatus().equals("REJECTED")) {
                return error("职务变动已审核，不允许再编辑，请刷新页面查看最新状态！");
            }
        }

        return toAjax(bizPersonJobChangeService.deleteBizPersonJobChangeByIds(ids));
    }
}
