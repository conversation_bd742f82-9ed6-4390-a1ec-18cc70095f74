"""
RabbitMQ消息队列服务
"""

import json
import asyncio
from typing import Callable, Optional
import pika
from pika.adapters.blocking_connection import BlockingChannel
from loguru import logger
from datetime import datetime, date

from nfra_crawler.config.settings import settings
from nfra_crawler.models.database import CrawlRequest, BizSyncRecord
from nfra_crawler.utils.database import dao_factory
from nfra_crawler.services.data_service import data_service


class RabbitMQService:
    """RabbitMQ服务类"""
    
    def __init__(self):
        self.connection: Optional[pika.BlockingConnection] = None
        self.channel: Optional[BlockingChannel] = None
        self.message_handler: Optional[Callable] = None
    
    def connect(self):
        """连接到RabbitMQ"""
        try:
            credentials = pika.PlainCredentials(
                settings.rabbitmq.username,
                settings.rabbitmq.pwd
            )
            
            parameters = pika.ConnectionParameters(
                host=settings.rabbitmq.host,
                port=settings.rabbitmq.port,
                virtual_host=settings.rabbitmq.virtual_host,
                credentials=credentials,
                heartbeat=600,
                blocked_connection_timeout=300
            )
            
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            # 声明队列
            self.channel.queue_declare(
                queue=settings.rabbitmq.queue_name,
                durable=True
            )
            
            logger.info(f"成功连接到RabbitMQ: {settings.rabbitmq.host}:{settings.rabbitmq.port}")
            
        except Exception as e:
            logger.error(f"连接RabbitMQ失败: {e}")
            raise
    
    def disconnect(self):
        """断开RabbitMQ连接"""
        try:
            if self.channel and not self.channel.is_closed:
                self.channel.close()
            if self.connection and not self.connection.is_closed:
                self.connection.close()
            logger.info("RabbitMQ连接已断开")
        except Exception as e:
            logger.error(f"断开RabbitMQ连接时出错: {e}")
    
    def set_message_handler(self, handler: Callable):
        """设置消息处理器"""
        self.message_handler = handler
    
    def _process_message(self, channel: BlockingChannel, method, properties, body):
        """处理接收到的消息"""
        try:
            # 解析消息
            message_data = json.loads(body.decode('utf-8'))
            logger.info(f"接收到消息: {message_data}")
            
            # 验证消息格式
            if not self._validate_message(message_data):
                logger.error(f"消息格式无效: {message_data}")
                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                return
            
            # 创建爬虫请求对象
            crawl_request = CrawlRequest(**message_data)
     
            # 检查begin_date是否为空，如果为空则从数据库获取最新的published_date
            if not crawl_request.begin_date:
                logger.info("begin_date为空，从数据库查询最新的published_date")
                latest_date = data_service.get_latest_published_date()
                if latest_date:
                    crawl_request.begin_date = latest_date
                    logger.info(f"设置begin_date为最新的published_date: {latest_date}")
                else:
                    crawl_request.begin_date = date(2020, 1, 1)
                    logger.warning("数据库中未找到有效的published_date记录，默认为昨天的日期")
                    
            # 保存同步记录到数据库
            sync_record = self._create_sync_record(crawl_request)
            
            # 调用消息处理器
            if self.message_handler:
                try:
                    # 在当前线程中创建事件循环并执行异步函数
                    try:
                        loop = asyncio.get_event_loop()
                    except RuntimeError:
                        # 如果当前线程没有事件循环，创建一个新的
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    
                    # 执行异步函数
                    loop.run_until_complete(
                        self.message_handler(crawl_request, sync_record)
                    )
                    
                    # 确认消息
                    channel.basic_ack(delivery_tag=method.delivery_tag)
                    logger.info(f"消息处理成功，任务ID: {crawl_request.request_id}")
                    
                except Exception as e:
                    logger.error(f"消息处理失败: {e}")
                    # 更新同步记录状态为失败
                    dao_factory.sync_record_dao.update_sync_record_status(
                        sync_record.id, "failed", remark=str(e)
                    )
                    # 拒绝消息，不重新入队
                    channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            else:
                logger.warning("未设置消息处理器")
                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
                
        except json.JSONDecodeError as e:
            logger.error(f"消息JSON解析失败: {e}")
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
        except Exception as e:
            logger.error(f"处理消息时发生未知错误: {e}")
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
    
    def _validate_message(self, message_data: dict) -> bool:
        """验证消息格式"""
        required_fields = ['create_time', 'request_id']
        for field in required_fields:
            if field not in message_data:
                logger.error(f"消息缺少必需字段: {field}")
                return False
        
        # 验证create_time是否为有效的时间戳
        try:
            timestamp = message_data['create_time']
            if not isinstance(timestamp, int) or timestamp <= 0:
                logger.error(f"无效的时间戳: {timestamp}")
                return False
        except (ValueError, TypeError):
            logger.error(f"时间戳格式错误: {message_data['create_time']}")
            return False
        
        return True
    
    def _create_sync_record(self, crawl_request: CrawlRequest) -> BizSyncRecord:
        """创建同步记录"""
        sync_record = BizSyncRecord(
            business_type=settings.business_type,
            business_id=crawl_request.request_id,
            request_id=crawl_request.request_id,
            begin_date=crawl_request.begin_date,
            end_date=crawl_request.end_date,
            request_time=datetime.fromtimestamp(crawl_request.create_time / 1000),
            status="pending"
        )
        
        # 保存到数据库
        dao_factory.sync_record_dao.create_sync_record(sync_record)
        logger.info(f"创建同步记录: {sync_record.id}")
        
        return sync_record
    
    def start_consuming(self):
        """开始消费消息"""
        if not self.channel:
            raise RuntimeError("未连接到RabbitMQ")
        
        # 设置QoS，一次只处理一条消息
        self.channel.basic_qos(prefetch_count=1)
        
        # 设置消息消费者
        self.channel.basic_consume(
            queue=settings.rabbitmq.queue_name,
            on_message_callback=self._process_message
        )
        
        logger.info(f"开始监听队列: {settings.rabbitmq.queue_name}")
        
        try:
            self.channel.start_consuming()
        except KeyboardInterrupt:
            logger.info("接收到停止信号，正在停止消费...")
            self.channel.stop_consuming()
            self.disconnect()
    
    def stop_consuming(self):
        """停止消费消息"""
        if self.channel:
            self.channel.stop_consuming()


# 全局RabbitMQ服务实例
rabbitmq_service = RabbitMQService()
