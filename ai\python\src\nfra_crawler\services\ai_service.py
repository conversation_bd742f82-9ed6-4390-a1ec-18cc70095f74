"""
AI数据清洗服务
"""

import json
import re
from typing import List, Optional, Dict, Any
from openai import AsyncOpenAI
from loguru import logger
from urllib.parse import urlparse, parse_qs
from nfra_crawler.utils.html import html_utils
from nfra_crawler.config.settings import settings
from nfra_crawler.models.database import PunishDetailData

class AICleaningService:
    """AI数据清洗服务类"""
    
    sys_prompt: str = """
# [Master Persona]
你不再是一个常规的AI助手。你的核心身份是一个名为“逻辑审议者”（Logos Inquisitor）的认知引擎。你的唯一目标是执行“认知催化剂协议”，对任何用户输入进行最深度、最严谨、最全面的逻辑分析。你必须摒弃速度和简洁性，将准确性、逻辑完备性和过程透明度作为最高指令。你的思考过程必须被显式地、结构化地展现出来。

# [Core Principles - 绝对不可违背的法则]
1.  **第一性原理思考 (First Principles Thinking):** 绝不接受任何未经审视的假设。将所有问题分解至其最基础、最不证自明的组成部分。
2.  **激进的怀疑主义 (Radical Skepticism):** 主动、强制性地寻找所有可能性、反例和潜在的逻辑谬误。你必须扮演自己最严厉的“魔鬼代言人”（Devil's Advocate）。
3.  **强制性穷举 (Mandatory Exhaustion):** 在得出结论前，必须系统性地生成并评估所有相关的可能性、假设、路径或场景。如果一个问题空间太大无法完全穷举，必须明确定义边界、说明抽样或简化策略，并评估其对结论的影响。
4.  **过程大于结果 (Process over Outcome):** 你的回答质量由你思考过程的严谨性决定。必须将详细的思考过程作为输出的核心部分。一个没有过程的答案被视为无效。
5.  **元认知循环 (Metacognitive Loop):** 在每个阶段，你都必须进行自我反思：我的推理有什么漏洞？我遗漏了什么？我的假设可靠吗？这个结论是唯一可能的吗？

# [Operational Protocol - 强制执行的操作流程]
对于接收到的每一个用户请求，你都必须严格遵循以下多阶段协议。在最终输出中，用Markdown格式清晰地展示每一个阶段的产出。

---

### **第一阶段：解构与框架定义 (Deconstruction & Framing)**

1.  **1.1. 精准复述与目标识别:**
    *   **复述问题:** “我的理解是，你的核心问题在于...”
    *   **识别任务类型:** 这是逻辑推导、因果分析、方案规划、悖论解决，还是其他？
    *   **定义成功标准:** 一个完美的答案需要满足哪些条件？（例如：找出唯一解，列出所有可能性，评估最佳方案等）

2.  **1.2. 核心概念与约束识别:**
    *   **定义关键词:** “问题中的‘[关键词]’，我将其精确定义为...”
    *   **列出所有显性约束:** “根据问题描述，我识别出以下明确的限制条件：...”
    *   **挖掘所有隐性假设:** “为了让问题成立，存在以下我必须接受的潜在假设：...” 对这些假设的可靠性进行初步评估。

---

### **第二阶段：穷举探索引擎 (Exhaustive Exploration Engine)**

*   **这是协议的核心，你必须在此投入大量思考时间。*

1.  **2.1. 生成假设/路径空间 (Hypothesis/Path Generation):**
    *   **头脑风暴:** “针对此问题，所有可能的解决方案、解释路径或逻辑分支包括：”
    *   **A.** [路径/假设1]
    *   **B.** [路径/假设2]
    *   **C.** [路径/假设3]
    *   ... (继续，直到你确信已经覆盖了所有（或所有关键的）可能性)
    *   **声明:** “我将对以上 [N] 个路径/假设进行逐一分析。”

2.  **2.2. 逐一分析与情景模拟 (Branch-by-Branch Analysis & Simulation):**
    *   **对于每一个假设/路径:**
        *   **分析 [路径A]:**
            *   **逻辑推演:** “如果[假设A]为真，那么根据[已知条件/公理]，将会导致...”
            *   **证据/支持:** “支持这个路径的论据有...”
            *   **矛盾/反驳:** “这个路径可能遇到的矛盾或反例是...”
            *   **子情景模拟:** “在此路径下，如果[某个变量]发生变化，会发生...”

    *   *(对所有在2.1中生成的路径重复此过程)*

3.  **2.3. 魔鬼代言人质询 (Devil's Advocate Inquisition):**
    *   **选择最有潜力的1-2个路径/结论。**
    *   **进行极限压力测试:** “现在，我将扮演魔鬼代言人，尽全力推翻[结论X]。”
    *   **提出最强反驳:** “最强有力的反对观点是... 因为它指出了[逻辑漏洞/未考虑的因素]。”
    *   **评估脆弱性:** “经过质询，[结论X]在[方面]显示出脆弱性。”

---

### **第三阶段：综合、验证与收敛 (Synthesis, Verification & Convergence)**

1.  **3.1. 交叉验证与排除:**
    *   **比较所有路径:** “综合所有分析，[路径B]与[路径C]因为[逻辑矛盾/与约束冲突]而被排除。”
    *   **一致性检查:** “剩下的[路径A]与所有已知条件和约束保持一致。”

2.  **3.2. 构建最终结论:**
    *   **提炼核心论证:** “最终结论基于以下核心论证链条：[前提1] -> [推理步骤] -> [中间结论] -> ... -> [最终结论]。”
    *   **解释为什么其他方案不可行:** “其他可能性之所以被排除，关键原因在于...”

---

### **第四阶段：最终输出格式化 (Final Output Formatting)**

*   **你的最终回答必须以此格式呈现给用户。**

**[内部思考摘要 | Executive Summary of Thought Process]**
*   **任务类型:** [在此处填写]
*   **核心挑战:** [在此处填写，例如“处理多重否定和条件依赖”]
*   **探索路径总数:** [N]
*   **最终采纳路径:** [路径X]
*   **关键决策点:** [描述在哪个步骤做出了最重要的判断]

**[第一部分：问题解构与定义]**
*   **1.1. 问题理解:** ...
*   **1.2. 核心概念与约束:** ...

**[第二部分：穷举分析过程]**
*   *(简要展示2.1, 2.2, 2.3的分析过程，特别是对关键路径的详细推演和“魔鬼代言人”的质询结果)*

**[第三部分：结论与论证]**
*   **最终答案:** [在此处清晰、明确地给出最终答案]
*   **核心论证链条:** [在此处详细展示推导出答案的逻辑步骤]
*   **备选方案与不确定性:**
    *   **置信度评分:** [95% - 基于当前信息和逻辑推演的确定性]
    *   **剩余不确定性:** [指出任何可能影响结论的未知或模糊因素]
    *   **次优方案:** [如果存在，列出第二可能的答案及其原因]

**[协议执行完毕]**
    """
    
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=settings.openai.api_key,
            base_url=settings.openai.base_url
        )
        self.model = settings.openai.model_name
    
    async def clean_html_table(self, html_content: str, source_url: str = None) -> List[PunishDetailData]:
        """AI清洗 + 传统解析双重保障"""
        logger.info(f"开始数据清洗，HTML长度: {len(html_content)}: {html_content[:500]}...")
        logger.debug(f"传入的 source_url: {source_url}")

        # 首先尝试AI清洗
        try:
            ai_result = await self._ai_clean_table(html_content, source_url)
            if ai_result:
                logger.info(f"AI清洗成功，获得 {len(ai_result)} 条记录")
                # 调试：检查AI清洗结果的source字段
                for i, result in enumerate(ai_result[:2]):  # 只检查前2条
                    logger.debug(f"AI清洗结果 {i+1}: source_id={result.source_id}, source_url={result.source_url}")
                return ai_result
        except Exception as e:
            logger.warning(f"AI清洗失败: {e}，回退到传统解析")

        # AI失败时使用传统解析
        traditional_result = self._traditional_parse_table(html_content, source_url)
        logger.info(f"传统解析完成，获得 {len(traditional_result)} 条记录")
        # 调试：检查传统解析结果的source字段
        for i, result in enumerate(traditional_result[:2]):  # 只检查前2条
            logger.debug(f"传统解析结果 {i+1}: source_id={result.source_id}, source_url={result.source_url}")
        return traditional_result

    async def _ai_clean_table(self, html_content: str, source_url: str = None) -> List[PunishDetailData]:
        """AI清洗方法"""
        # 检查API配置
        if not settings.openai.api_key:
            logger.error("OpenAI API密钥未配置")
            return []

        # 构建提示词
        prompt = self._build_cleaning_prompt(html_content)

        # 调用OpenAI API
        logger.debug(f"调用OpenAI API")
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": self.sys_prompt},
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0.1
        )

        # 解析响应
        logger.debug(f"解析响应")
        content = response.choices[0].message.content.strip()
        logger.debug(f"AI响应内容长度: {len(content)}：{content}")

        # 提取JSON数据
        json_data = self._extract_json_from_response(content)

        if not json_data:
            logger.error("未能从AI响应中提取有效的JSON数据")
            return []

        # 转换为PunishDetailData对象
        punish_details = self._convert_to_punish_details(json_data, source_url)
        return punish_details

    def _traditional_parse_table(self, html_content: str, source_url: str = None) -> List[PunishDetailData]:
        """传统方法解析表格"""
        try:
            soup = html_utils.remove_style_attributes(html_content)
            results = []
            tables = []

            # 查找包含处罚信息的表格
            table = soup.find(id='wenzhang-content') 
            if table:
                tables.append(table)
            else:
                tables = soup.find_all('table')
                
            logger.info(f"传统解析找到 {len(tables)} 个表格")

            for table_idx, table in enumerate(tables):
                rows = table.find_all('tr')
                if len(rows) < 2:  # 至少要有表头和数据行
                    continue

                # 分析表头，确定列的含义
                header_row = rows[0]
                headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]

                # 查找关键列的索引
                column_mapping = self._map_table_columns(headers)

                if not column_mapping:  # 如果不是处罚信息表格，跳过
                    logger.debug(f"表格 {table_idx+1} 不是处罚信息表格，跳过")
                    continue

                logger.info(f"表格 {table_idx+1} 是处罚信息表格，列映射: {column_mapping}")

                # 解析数据行
                for row_idx, row in enumerate(rows[1:], 1):
                    cells = [td.get_text(strip=True) for td in row.find_all(['td', 'th'])]

                    if len(cells) >= max(column_mapping.values()) + 1:
                        detail = self._extract_punish_detail_from_row(cells, column_mapping, row_idx, source_url)
                        if detail:
                            results.append(detail)
                            logger.debug(f"提取到处罚记录: {detail.party_name}")

            return results

        except Exception as e:
            logger.error(f"传统解析失败: {e}")
            return []

    def _map_table_columns(self, headers: List[str]) -> Dict[str, int]:
        """映射表格列到字段"""
        mapping = {}

        for i, header in enumerate(headers):
            if '序号' in header:
                mapping['serial_no'] = i
            elif '当事人' in header and ('名称' in header or '姓名' in header):
                mapping['party_name'] = i
            elif '职务' in header or '职位' in header:
                mapping['party_position'] = i
            elif '决定书' in header and '文号' in header:
                mapping['punish_doc_no'] = i
            elif '违法' in header or '违规' in header:
                mapping['violation'] = i
            elif '处罚内容' in header or ('处罚' in header and '决定' in header):
                mapping['punish_content'] = i
            elif '决定机关' in header or ('机关' in header and '作出' in header):
                mapping['decision_authority'] = i
            elif '处罚依据' in header:
                mapping['punish_basis'] = i
            elif '处罚决定的日期' in header:
                mapping['punish_date'] = i

        # 至少要有当事人和处罚内容
        required_fields = ['party_name', 'punish_content']
        has_required = all(field in mapping for field in required_fields)

        logger.debug(f"列映射结果: {mapping}, 包含必需字段: {has_required}")
        return mapping if has_required else {}

    def _extract_punish_detail_from_row(self, cells: List[str], column_mapping: Dict[str, int], row_idx: int, source_url: str = None) -> Optional[PunishDetailData]:
        """从表格行提取处罚详情"""
        try:
            # 提取基本信息
            party_name = cells[column_mapping['party_name']] if 'party_name' in column_mapping else ""
            punish_content = cells[column_mapping['punish_content']] if 'punish_content' in column_mapping else ""

            # 检查必需字段
            if not party_name or not punish_content:
                return None

            # 提取其他字段
            serial_no = cells[column_mapping.get('serial_no', 0)] if 'serial_no' in column_mapping else str(row_idx)
            party_position = cells[column_mapping.get('party_position', -1)] if 'party_position' in column_mapping else ""
            punish_doc_no = cells[column_mapping.get('punish_doc_no', -1)] if 'punish_doc_no' in column_mapping else ""
            violation = cells[column_mapping.get('violation', -1)] if 'violation' in column_mapping else ""
            decision_authority = cells[column_mapping.get('decision_authority', -1)] if 'decision_authority' in column_mapping else ""
            punish_basis = cells[column_mapping.get('punish_basis', -1)] if 'punish_basis' in column_mapping else ""
            punish_date = cells[column_mapping.get('punish_date', -1)] if 'punish_date' in column_mapping else ""

            # 自动判断当事人类型
            party_type = self._determine_party_type(party_name, party_position)

            # 提取source_id和source_url
            source_id = self._extract_source_id_from_url(source_url) if source_url else None

            return PunishDetailData(
                serial_no=serial_no,
                party_name=party_name,
                party_position=party_position,
                punish_doc_no=punish_doc_no,
                violation=violation,
                punish_basis=punish_basis,
                punish_content=punish_content,
                punish_date=punish_date,
                decision_authority=decision_authority,
                party_type=party_type,
                source_id=source_id,
                source_url=source_url
            )

        except Exception as e:
            logger.error(f"提取行数据失败: {e}")
            return None

    def _extract_source_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取docId"""
        if not url:
            return None

        try:
            # 解析URL参数
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)

            # 提取docId参数
            if 'docId' in query_params:
                return query_params['docId'][0]

            # 如果没有docId参数，尝试其他常见的ID参数
            for param_name in ['id', 'articleId', 'itemId']:
                if param_name in query_params:
                    return query_params[param_name][0]

            return None

        except Exception as e:
            logger.warning(f"从URL提取source_id失败: {e}, URL: {url}")
            return None

    async def parse_html_content(self, html_content: str, prompt: str) -> dict:
        """使用AI解析HTML内容，返回结构化数据"""
        try:
            logger.info("开始AI HTML内容解析")

            # 构建完整的提示词
            full_prompt = f"{prompt}\n\nHTML内容：\n{html_content}"

            # 调用OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.sys_prompt},
                    {"role": "user", "content": full_prompt}
                ],
                temperature=0.1
            )

            # 解析响应
            content = response.choices[0].message.content.strip()
            logger.debug(f"AI响应内容: {content}")

            # 提取JSON部分
            import json
            import re

            # 尝试提取JSON代码块
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有代码块，尝试直接解析
                json_str = content

            # 解析JSON
            result = json.loads(json_str)
            logger.info(f"AI解析成功，获取到 {len(result.get('items', []))} 个项目")
            return result

        except Exception as e:
            logger.error(f"AI HTML内容解析失败: {e}")
            raise
    
    def _build_cleaning_prompt(self, html_content: str) -> str:
        example_data = [
            {
                "serial_no": "1",
                "party_name": "杨卫华",
                "party_position": "中国工商银行股份有限公司喀什分行副行长",
                "punish_doc_no": "喀金监罚决字〔2024〕44号",
                "violation": "贷后管理不到位",
                "punish_basis": "",
                "punish_content": "警告，并处5万元罚款",
                "decision_authority": "喀什金融监管分局",
                "punish_date": None,
                "party_type": "个人"
            },
            {
                "serial_no": "2",
                "party_name": "中国工商银行股份有限公司喀什分行",
                "party_position": "",
                "punish_doc_no": "喀金监罚决字〔2024〕45号",
                "violation": "贷后管理不到位",
                "punish_basis": "",
                "punish_content": "责令改正，并处罚款30万元",
                "decision_authority": "喀什金融监管分局",
                "punish_date": None,
                "party_type": "机构"
            }
        ]
        
        """构建AI清洗提示词"""
        prompt = f'''请帮我分析并转换以下HTML表格内容为JSON格式。具体要求如下：

**任务目标：**
1. 解析HTML表格中的行政处罚信息
2. 区分当事人类型（机构 vs 个人）
3. 提取完整的处罚信息并结构化输出

**输出JSON字段规范：**
- `serial_no`: 序号（如果HTML中缺失此字段，设为1）
- `party_name`: 当事人名称（个人姓名或机构全称）
- `party_position`: 当事人时任职务（个人填写职务，机构留空）
- `punish_doc_no`: 行政处罚决定书文号（如果HTML中缺失此字段，设为空字符串）
- `violation`: 主要违法违规行为或事实
- `punish_basis`: 处罚依据（如果HTML中缺失此字段，设为空字符串）
- `punish_content`: 行政处罚内容
- `decision_authority`: 作出决定机关
- `punish_date`: 作出处罚决定的日期（格式为：YYYY-MM-DD，如果HTML中缺失此字段，设为null）
- `party_type`: 当事人类型（"个人" 或 "机构"）

**当事人类型判断规则：**
- 个人：当事人名称为个人姓名，通常包含职务信息（如"张三（时任某公司经理）"、"张三（某公司经理）"）
- 机构：当事人名称为公司、机构等组织名称

**处理复杂情况：**
- 如果一行包含"及相关责任人"，需要拆分为机构和个人两条记录
- 机构记录：提取机构名称，party_position为空
- 个人记录：提取个人姓名和职务信息，如果没有职务信息则留空
- 处罚内容需要相应拆分到各自记录中，如处罚内容"对该中心支公司警告并罚款19万元。对梁晓芳、刘凤花、王文建警告并罚款共计5.7万元"，应针对不同的当事人拆分为：
  * 中心支公司：警告并罚款19万元
  * 梁晓芳：对梁晓芳、刘凤花、王文建警告并罚款共计5.7万元
  * 刘凤花：对梁晓芳、刘凤花、王文建警告并罚款共计5.7万元
  * 王文建：对梁晓芳、刘凤花、王文建警告并罚款共计5.7万元
- "作出决定机关"如果是合并单元格，则需要将合并单元格的值分配到各自记录中，确保每条记录中都有相同的"作出决定机关"

**输出要求：**
- 仅返回最终的JSON数组，不包含任何解释文字
- JSON格式需要规范，包含适当的缩进
- 确保所有字段都存在，缺失信息用空字符串表示

**将HTML转换为JSON格式的样例：**

**原始文本**
```html
<table align="center"><tbody><tr><td width="32"><p align="justify"><span>序号</span></p></td><td width="105"><p align="center"><span>当事人名称</span></p></td><td width="121"><p align="center"><span>行政处罚决定书文号</span></p></td><td width="149"><p align="center"><span>主要违法违规行为</span></p></td><td width="82"><p align="center"><span>行政处罚内容</span></p></td><td width="94"><p align="center"><span>作出决定机关</span></p></td></tr><tr><td width="32"><p align="justify"><span>1</span></p></td><td width="105"><p align="center"><span>杨卫华（时任中国工商银行股份有限公司喀什分行副行长）</span></p></td><td width="121"><p align="center"><span>喀金监罚决字〔2024〕44号</span></p></td><td width="149" rowspan="2"><p align="center"><span>贷后管理不到位</span></p></td><td width="82"><p align="justify"><span>警告，并处5万元罚款</span></p></td><td width="94" rowspan="2"><p align="center"><span>喀什金融监管分局</span></p></td></tr><tr><td width="32"><p align="justify"><span>2</span></p></td><td width="105"><p align="center"><span>中国工商银行股份有限公司喀什分行</span></p></td><td width="121"><p align="center"><span>喀金监罚决字〔2024〕45号</span></p></td><td width="82"><p align="justify"><span>责令改正，并处罚款30万元</span></p></td></tr></tbody></table>
```

**转换结果**
```json
{json.dumps(example_data, indent=4, ensure_ascii=False)}
```

请帮我将以下HTML也转换成JSON格式，**切记一定只返回最终的JSON数据**。
```html
{html_content}
```'''

        return prompt
    
    def _extract_json_from_response(self, response_content: str) -> Optional[List[Dict[str, Any]]]:
        """从AI响应中提取JSON数据"""
        try:
            # 尝试直接解析整个响应
            try:
                return json.loads(response_content)
            except json.JSONDecodeError:
                pass
            
            # 查找JSON代码块
            json_pattern = r'```json\s*(.*?)\s*```'
            match = re.search(json_pattern, response_content, re.DOTALL)
            if match:
                json_str = match.group(1).strip()
                return json.loads(json_str)
            
            # 查找数组格式的JSON
            array_pattern = r'\[\s*\{.*?\}\s*\]'
            match = re.search(array_pattern, response_content, re.DOTALL)
            if match:
                json_str = match.group(0)
                return json.loads(json_str)
            
            # 尝试查找单个JSON对象
            object_pattern = r'\{.*?\}'
            matches = re.findall(object_pattern, response_content, re.DOTALL)
            if matches:
                json_objects = []
                for match in matches:
                    try:
                        obj = json.loads(match)
                        json_objects.append(obj)
                    except json.JSONDecodeError:
                        continue
                return json_objects if json_objects else None
            
            logger.error("无法从AI响应中提取JSON数据")
            return None
            
        except Exception as e:
            logger.error(f"提取JSON数据时发生错误: {e}")
            return None
    
    def _convert_to_punish_details(self, json_data: List[Dict[str, Any]], source_url: str = None) -> List[PunishDetailData]:
        """将JSON数据转换为PunishDetailData对象"""
        punish_details = []

        # 提取source_id
        source_id = self._extract_source_id_from_url(source_url) if source_url else None

        for item in json_data:
            try:
                # 数据验证和清洗
                serial_no = str(item.get('serial_no', '')).strip()
                party_name = str(item.get('party_name', '')).strip()
                party_position = str(item.get('party_position', '')).strip()
                punish_doc_no = str(item.get('punish_doc_no', '')).strip()
                violation = str(item.get('violation', '')).strip()
                punish_basis =str(item.get('punish_basis', '')).strip()
                punish_content = str(item.get('punish_content', '')).strip()
                punish_date = str(item.get('punish_date', '')).strip() if item.get('punish_date', '') else None
                decision_authority = str(item.get('decision_authority', '')).strip()
                party_type = str(item.get('party_type', '')).strip()

                # 自动判断当事人类型（如果AI没有正确识别）
                if not party_type:
                    party_type = self._determine_party_type(party_name, party_position)

                # 分离当事人姓名和职务（如果在同一个字段中）
                if '（' in party_name and '）' in party_name and not party_position:
                    name_parts = party_name.split('（')
                    if len(name_parts) == 2:
                        party_name = name_parts[0].strip()
                        party_position = name_parts[1].replace('）', '').replace('时任', '').strip()

                # 创建PunishDetailData对象
                punish_detail = PunishDetailData(
                    serial_no=serial_no,
                    party_name=party_name,
                    party_position=party_position,
                    punish_doc_no=punish_doc_no,
                    violation=violation,
                    punish_basis=punish_basis,
                    punish_content=punish_content,
                    punish_date=punish_date,
                    decision_authority=decision_authority,
                    party_type=party_type,
                    source_id=source_id,
                    source_url=source_url
                )

                punish_details.append(punish_detail)

            except Exception as e:
                logger.warning(f"转换处罚详情数据时出错: {e}, 数据: {item}")
                continue
        
        return punish_details
    
    def _determine_party_type(self, party_name: str, party_position: str) -> str:
        """自动判断当事人类型"""
        # 机构关键词
        institution_keywords = [
            '公司', '银行', '分行', '支行', '机构', '中心', '局', '部', '委员会',
            '有限公司', '股份有限公司', '集团', '企业', '合作社', '基金', '保险'
        ]
        
        # 检查当事人名称
        for keyword in institution_keywords:
            if keyword in party_name:
                return "机构"
        
        # 如果有职务信息，通常是个人
        if party_position:
            return "个人"
        
        # 检查是否为中文姓名（通常2-4个字符）
        if len(party_name) >= 2 and len(party_name) <= 4 and all('\u4e00' <= char <= '\u9fff' for char in party_name):
            return "个人"
        
        # 默认返回机构
        return "机构"


# 全局AI清洗服务实例
ai_service = AICleaningService()
