# 使用Python 3.11官方镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 复制项目文件
COPY pyproject.toml .
COPY src/ src/
COPY .env.example .env

# 安装Python依赖
RUN uv sync --no-dev

# 安装playwright浏览器
RUN uv run playwright install chromium
RUN uv run playwright install-deps

# 创建日志目录
RUN mkdir -p logs

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV PYTHONUNBUFFERED=1

# 暴露端口（如果需要）
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD uv run python -m nfra_crawler.cli health || exit 1

# 启动命令
CMD ["uv", "run", "python", "-m", "nfra_crawler.main"]
