package com.metaorg.library.domain;

import com.metaorg.common.annotation.Excel;
import com.metaorg.common.core.domain.BaseEntity;
import javax.validation.constraints.*;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Objects;

/**
 * 同步处罚公示项导入对象
 *
 * <AUTHOR>
 * @date 2025-06-09
 */

@Data
@NoArgsConstructor
public class BizSyncPunishItemTplVM extends BaseEntity
{
    /** 处罚公示标识 */
    @NotBlank
    @Excel(name = "处罚公示标识")
    private String id;

    /** 同步记录标识 */
    @NotBlank
    @Excel(name = "同步记录标识")
    private String syncRecordId;

    /** 同步请求标识 */
    @NotBlank
    @Excel(name = "同步请求标识")
    private String requestId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 批准机关 */
    @Excel(name = "批准机关")
    private String rewardPunishOrg;

    /** 发布日期 */
    @Excel(name = "发布日期")
    private String publishedDate;

    /** 来源名称 */
    @Excel(name = "来源名称")
    private String sourceName;

    /** 来源ID */
    @Excel(name = "来源标识")
    private String sourceId;

    /** 来源URL */
    @Excel(name = "来源网址")
    private String sourceUrl;

    /** 错误信息 */
    private String errMsg;

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof BizSyncPunishItemTplVM)) {
            return false;
        }

        BizSyncPunishItemTplVM other = (BizSyncPunishItemTplVM) o;
        return (Objects.equals(sourceId, other.sourceId));
    }

    @Override
    public int hashCode() {
        return Objects.hash(sourceId);
    }
} 