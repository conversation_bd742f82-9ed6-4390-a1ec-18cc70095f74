-- 新疆金融监管局处罚信息数据库初始化脚本

-- 新建同步记录表
DROP TABLE IF EXISTS biz_sync_record CASCADE;
CREATE TABLE biz_sync_record(
    id varchar(64) NOT NULL,
    business_type varchar(255),
    business_id varchar(255),
    request_id varchar(64),
    request_time timestamp,
    completed_time timestamp,
    elapsed int8,
    status varchar(50),
    remark varchar(500),
    create_by varchar(64),
    create_time timestamp,
    update_by varchar(64),
    update_time timestamp
);

COMMENT ON TABLE biz_sync_record IS '数据同步-同步记录表';
COMMENT ON COLUMN biz_sync_record.id IS '同步记录标识';
COMMENT ON COLUMN biz_sync_record.business_type IS '业务类型';
COMMENT ON COLUMN biz_sync_record.business_id IS '业务标识';
COMMENT ON COLUMN biz_sync_record.request_id IS '同步请求标识';
COMMENT ON COLUMN biz_sync_record.request_time IS '同步请求时间';
COMMENT ON COLUMN biz_sync_record.completed_time IS '同步完成时间';
COMMENT ON COLUMN biz_sync_record.elapsed IS '同步耗时(毫秒)';
COMMENT ON COLUMN biz_sync_record.status IS '同步状态(pending:待处理, processing:处理中, failed: 失败, succeeded: 成功)';
COMMENT ON COLUMN biz_sync_record.remark IS '备注';
COMMENT ON COLUMN biz_sync_record.create_by IS '创建者';
COMMENT ON COLUMN biz_sync_record.create_time IS '创建时间';
COMMENT ON COLUMN biz_sync_record.update_by IS '更新者';
COMMENT ON COLUMN biz_sync_record.update_time IS '更新时间';

CREATE INDEX ix_biz_sync_record_request_time ON biz_sync_record(request_time);
ALTER TABLE biz_sync_record ADD CONSTRAINT pk_biz_sync_record PRIMARY KEY (id);

-- 新建同步处罚公示列表
DROP TABLE IF EXISTS biz_sync_punish_item CASCADE;
CREATE TABLE biz_sync_punish_item(
    id varchar(64) NOT NULL,
    sync_record_id varchar(64),
    request_id varchar(64),
    title varchar(255),
    reward_punish_org varchar(255),
    published_date timestamp,
    source_id varchar(100),
    source_url varchar(500),
    create_by varchar(64),
    create_time timestamp,
    update_by varchar(64),
    update_time timestamp
);

COMMENT ON TABLE biz_sync_punish_item IS '数据同步-处罚公示列表';
COMMENT ON COLUMN biz_sync_punish_item.id IS '处罚公示标识';
COMMENT ON COLUMN biz_sync_punish_item.sync_record_id IS '同步记录标识';
COMMENT ON COLUMN biz_sync_punish_item.request_id IS '同步请求标识';
COMMENT ON COLUMN biz_sync_punish_item.title IS '标题';
COMMENT ON COLUMN biz_sync_punish_item.reward_punish_org IS '批准机关';
COMMENT ON COLUMN biz_sync_punish_item.published_date IS '发布日期';
COMMENT ON COLUMN biz_sync_punish_item.source_id IS '来源标识';
COMMENT ON COLUMN biz_sync_punish_item.source_url IS '来源网址';
COMMENT ON COLUMN biz_sync_punish_item.create_by IS '创建者';
COMMENT ON COLUMN biz_sync_punish_item.create_time IS '创建时间';
COMMENT ON COLUMN biz_sync_punish_item.update_by IS '更新者';
COMMENT ON COLUMN biz_sync_punish_item.update_time IS '更新时间';

CREATE INDEX ix_biz_sync_punish_item_request_id ON biz_sync_punish_item(request_id);
CREATE INDEX ix_biz_sync_punish_item_sync_record_id ON biz_sync_punish_item(sync_record_id);
CREATE INDEX ix_biz_sync_punish_item_published_date ON biz_sync_punish_item(published_date);
ALTER TABLE biz_sync_punish_item ADD CONSTRAINT pk_biz_sync_punish_item PRIMARY KEY (id);

-- 新建同步处罚公示明细表
DROP TABLE IF EXISTS biz_sync_punish_detail CASCADE;
CREATE TABLE biz_sync_punish_detail(
    id varchar(64) NOT NULL,
    punish_item_id varchar(255),
    request_id varchar(64),
    order_num int4 DEFAULT 0,
    party_name varchar(255),
    party_position varchar(255),
    punish_doc_no varchar(255),
    violation varchar(255),
    punish_content text,
    decision_authority varchar(255),
    party_type varchar(255),
    punish_json_content json,
    create_by varchar(64),
    create_time timestamp,
    update_by varchar(64),
    update_time timestamp
);

COMMENT ON TABLE biz_sync_punish_detail IS '数据同步-处罚公示明细';
COMMENT ON COLUMN biz_sync_punish_detail.id IS '处罚明细标识';
COMMENT ON COLUMN biz_sync_punish_detail.punish_item_id IS '处罚公示标识';
COMMENT ON COLUMN biz_sync_punish_detail.request_id IS '同步请求标识';
COMMENT ON COLUMN biz_sync_punish_detail.order_num IS '序号';
COMMENT ON COLUMN biz_sync_punish_detail.party_name IS '当事人名称';
COMMENT ON COLUMN biz_sync_punish_detail.party_position IS '当事人时任职务';
COMMENT ON COLUMN biz_sync_punish_detail.punish_doc_no IS '行政处罚决定书文号';
COMMENT ON COLUMN biz_sync_punish_detail.violation IS '主要违法违规行为';
COMMENT ON COLUMN biz_sync_punish_detail.punish_content IS '行政处罚内容';
COMMENT ON COLUMN biz_sync_punish_detail.decision_authority IS '作出决定机关';
COMMENT ON COLUMN biz_sync_punish_detail.party_type IS '当事人类型(机构，个人)';
COMMENT ON COLUMN biz_sync_punish_detail.punish_json_content IS '行政处罚内容JSON格式';
COMMENT ON COLUMN biz_sync_punish_detail.create_by IS '创建者';
COMMENT ON COLUMN biz_sync_punish_detail.create_time IS '创建时间';
COMMENT ON COLUMN biz_sync_punish_detail.update_by IS '更新者';
COMMENT ON COLUMN biz_sync_punish_detail.update_time IS '更新时间';

CREATE INDEX ix_biz_sync_punish_detail_punish_item_id ON biz_sync_punish_detail(punish_item_id);
CREATE INDEX ix_biz_sync_punish_detail_request_id ON biz_sync_punish_detail(request_id);
CREATE INDEX ix_biz_sync_punish_detail_party_name ON biz_sync_punish_detail(party_name);
CREATE INDEX ix_biz_sync_punish_detail_punish_doc_no ON biz_sync_punish_detail(punish_doc_no);
CREATE INDEX ix_biz_sync_punish_detail_decision_authority ON biz_sync_punish_detail(decision_authority);
ALTER TABLE biz_sync_punish_detail ADD CONSTRAINT pk_biz_sync_punish_detail PRIMARY KEY (id);
