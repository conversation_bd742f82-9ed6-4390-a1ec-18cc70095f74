package com.metaorg.library.domain;

import com.metaorg.common.annotation.Excel;
import com.metaorg.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 同步记录对象 biz_sync_record
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public class BizSyncRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 业务ID */
    @Excel(name = "业务ID")
    private String businessId;

    /** 请求ID */
    @Excel(name = "请求ID")
    private String requestId;

    /** 开始日期 */
    @Excel(name = "开始日期")
    private String beginDate;

    /** 结束日期 */
    @Excel(name = "结束日期")
    private String endDate;

    /** 请求时间 */
    @Excel(name = "请求时间")
    private String requestTime;

    /** 开始时间 */
    @Excel(name = "开始时间")
    private String startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    private String endTime;

    /** 同步状态 */
    @Excel(name = "同步状态")
    private String status;

    /** 总页数 */
    @Excel(name = "总页数")
    private Long totalPages;

    /** 已处理页数 */
    @Excel(name = "已处理页数")
    private Long processedPages;

    /** 总记录数 */
    @Excel(name = "总记录数")
    private Long totalRecords;

    /** 成功记录数 */
    @Excel(name = "成功记录数")
    private Long successRecords;

    /** 失败记录数 */
    @Excel(name = "失败记录数")
    private Long failureRecords;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setBusinessType(String businessType)
    {
        this.businessType = businessType;
    }

    public String getBusinessType()
    {
        return businessType;
    }

    public void setBusinessId(String businessId)
    {
        this.businessId = businessId;
    }

    public String getBusinessId()
    {
        return businessId;
    }

    public void setRequestId(String requestId)
    {
        this.requestId = requestId;
    }

    public String getRequestId()
    {
        return requestId;
    }

    public void setBeginDate(String beginDate)
    {
        this.beginDate = beginDate;
    }

    public String getBeginDate()
    {
        return beginDate;
    }

    public void setEndDate(String endDate)
    {
        this.endDate = endDate;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setRequestTime(String requestTime)
    {
        this.requestTime = requestTime;
    }

    public String getRequestTime()
    {
        return requestTime;
    }

    public void setStartTime(String startTime)
    {
        this.startTime = startTime;
    }

    public String getStartTime()
    {
        return startTime;
    }

    public void setEndTime(String endTime)
    {
        this.endTime = endTime;
    }

    public String getEndTime()
    {
        return endTime;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setTotalPages(Long totalPages)
    {
        this.totalPages = totalPages;
    }

    public Long getTotalPages()
    {
        return totalPages;
    }

    public void setProcessedPages(Long processedPages)
    {
        this.processedPages = processedPages;
    }

    public Long getProcessedPages()
    {
        return processedPages;
    }

    public void setTotalRecords(Long totalRecords)
    {
        this.totalRecords = totalRecords;
    }

    public Long getTotalRecords()
    {
        return totalRecords;
    }

    public void setSuccessRecords(Long successRecords)
    {
        this.successRecords = successRecords;
    }

    public Long getSuccessRecords()
    {
        return successRecords;
    }

    public void setFailureRecords(Long failureRecords)
    {
        this.failureRecords = failureRecords;
    }

    public Long getFailureRecords()
    {
        return failureRecords;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getRemark()
    {
        return remark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessType", getBusinessType())
            .append("businessId", getBusinessId())
            .append("requestId", getRequestId())
            .append("beginDate", getBeginDate())
            .append("endDate", getEndDate())
            .append("requestTime", getRequestTime())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("status", getStatus())
            .append("totalPages", getTotalPages())
            .append("processedPages", getProcessedPages())
            .append("totalRecords", getTotalRecords())
            .append("successRecords", getSuccessRecords())
            .append("failureRecords", getFailureRecords())
            .append("remark", getRemark())
            .toString();
    }
} 