package com.metaorg.utils;

import com.metaorg.common.core.domain.BaseEntity;
import com.metaorg.common.core.domain.entity.SysRole;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.framework.aspectj.DataScopeAspect;
import com.metaorg.common.utils.ShiroUtils;

import java.util.ArrayList;
import java.util.List;

public class DataScopeUtils {
    /**
     * 数据范围过滤
     *
     * @param baseEntity 数据对象
     * @param user 用户对象
     * @param deptAlias 部门表别名
     * @param userAlias 用户表别名
     * @param permission 数据权限
     * @param deptId 当前部门ID
     */
    public static void dataScopeFilter(BaseEntity baseEntity, SysUser user, String deptAlias, String userAlias, String permission, long deptId)
    {
        // bugfix: 需要使用当前用户所在部门标识进行权限判断。
        SysUser currentUser = ShiroUtils.getSysUser();
        deptId = currentUser.getDeptId();

        if (StringUtils.isEmpty(deptAlias)) {
            deptAlias = "";
        }

        if (!deptAlias.contains(".")) {
            deptAlias = StringUtils.format("{}.dept_id", deptAlias);
        }

        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<String>();

        for (SysRole role : user.getRoles())
        {
            String dataScope = role.getDataScope();
            if (!DataScopeAspect.DATA_SCOPE_CUSTOM.equals(dataScope) && conditions.contains(dataScope))
            {
                continue;
            }
            if (StringUtils.isNotEmpty(permission) && StringUtils.isNotEmpty(role.getPermissions())
                    && !StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission)))
            {
                continue;
            }
            if (DataScopeAspect.DATA_SCOPE_ALL.equals(dataScope))
            {
                sqlString = new StringBuilder();
                conditions.add(dataScope);
                break;
            }
            else if (DataScopeAspect.DATA_SCOPE_CUSTOM.equals(dataScope))
            {
                sqlString.append(StringUtils.format(
                        " OR {} IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ",
                        deptAlias, role.getRoleId()));
            }
            else if (DataScopeAspect.DATA_SCOPE_DEPT.equals(dataScope))
            {
                sqlString.append(StringUtils.format(" OR {} = {} ", deptAlias, deptId));
            }
            else if (DataScopeAspect.DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope))
            {
                sqlString.append(StringUtils.format(
                        " OR {} IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )",
                        deptAlias, deptId, deptId));
            }
            else if (DataScopeAspect.DATA_SCOPE_SELF.equals(dataScope))
            {
                if (StringUtils.isNotBlank(userAlias))
                {
                    sqlString.append(StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId()));
                }
                else
                {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    sqlString.append(StringUtils.format(" OR {} = 0 ", deptAlias));
                }
            }
            conditions.add(dataScope);
        }

        // 多角色情况下，所有角色都不包含传递过来的权限字符，这个时候sqlString也会为空，所以要限制一下,不查询任何数据
        if (StringUtils.isEmpty(conditions))
        {
            sqlString.append(StringUtils.format(" OR {} = 0 ", deptAlias));
        }

        if (StringUtils.isNotBlank(sqlString.toString()))
        {
            baseEntity.getParams().put(DataScopeAspect.DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
        }
    }
}
