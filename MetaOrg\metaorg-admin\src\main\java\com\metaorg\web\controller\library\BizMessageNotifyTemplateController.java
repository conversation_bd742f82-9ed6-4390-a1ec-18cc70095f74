package com.metaorg.web.controller.library;

import java.util.*;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.library.domain.BizMessageNotifyTemplate;
import com.metaorg.library.domain.BizMessageNotifySubscriber;
import com.metaorg.library.service.IBizMessageNotifyTemplateService;
import com.metaorg.library.service.IBizMessageNotifySubscriberService;
import org.apache.shiro.authz.annotation.RequiresPermissions;   
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.library.domain.MessageTemplateSystemConfigInfo;
import com.metaorg.common.utils.DateUtils;
import com.metaorg.common.core.domain.entity.SysUser;
import com.metaorg.system.service.ISysUserService;
/**
 * 消息通知模板Controller
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Controller
@RequestMapping("/library/message/template")
public class BizMessageNotifyTemplateController extends BaseController {
    private String prefix = "library/message/template";

    @Autowired
    private IBizMessageNotifyTemplateService bizMessageNotifyTemplateService;

    @Autowired
    private IBizMessageNotifySubscriberService bizMessageNotifySubscriberService;

    @Autowired
    private ISysUserService sysUserService;

    @RequiresPermissions("library:message_notify_template:view")
    @GetMapping()
    public String template() {
        return prefix + "/template";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizMessageNotifyTemplate bizMessageNotifyTemplate, Map<String, Object> paramMap) {
        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizMessageNotifyTemplate.getParams().put("keyword", keyword);
        }
    }

    /**
     * 查询消息通知模板列表
     */
    @RequiresPermissions("library:message_notify_template:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BizMessageNotifyTemplate bizMessageNotifyTemplate, @RequestParam Map<String, Object> paramMap) {
        getQueryOptions(bizMessageNotifyTemplate, paramMap);
        startPage();
        List<BizMessageNotifyTemplate> list = bizMessageNotifyTemplateService.selectBizMessageNotifyTemplateList(bizMessageNotifyTemplate);
        return getDataTable(list);
    }

    /**
     * 新增消息通知模板
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存消息通知模板
     */
    @RequiresPermissions("library:message_notify_template:add")
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BizMessageNotifyTemplate bizMessageNotifyTemplate) {
        bizMessageNotifyTemplate.setCreateById(getUserId().toString());
        bizMessageNotifyTemplate.setCreateBy(getLoginName());
        bizMessageNotifyTemplate.setUpdateById(getUserId().toString());
        bizMessageNotifyTemplate.setUpdateBy(getLoginName());

        Date currentDate = DateUtils.getNowDate();
        bizMessageNotifyTemplate.setCreateTime(currentDate);
        bizMessageNotifyTemplate.setUpdateTime(currentDate);
        return toAjax(bizMessageNotifyTemplateService.insertBizMessageNotifyTemplate(bizMessageNotifyTemplate));
    }

    /**
     * 修改消息通知模板
     */
    @RequiresPermissions("library:message_notify_template:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap modelMap) {
        BizMessageNotifyTemplate bizMessageNotifyTemplate = bizMessageNotifyTemplateService.selectBizMessageNotifyTemplateById(id);
        modelMap.put("template", bizMessageNotifyTemplate);
        return prefix + "/edit";
    }

    /**
     * 站内消息启用/禁用
     */
    @Log(title = "站内消息启用/禁用", businessType = BusinessType.UPDATE)
    @RequiresPermissions("library:message_notify_template:edit")
    @PostMapping("/changeSystemConfigStatus/{id}")
    @ResponseBody
    public AjaxResult changeSystemConfigStatus(@PathVariable("id") String id, Boolean enabled)
    {
        // 先获取原始记录
        BizMessageNotifyTemplate originalTemplate = bizMessageNotifyTemplateService.selectBizMessageNotifyTemplateById(id);
        if (originalTemplate == null) {
            return error("未找到模板记录");
        }
        
        // 获取原始系统配置
        MessageTemplateSystemConfigInfo originalConfig = originalTemplate.getSystemConfigs();
        if (originalConfig == null) {
            originalConfig = new MessageTemplateSystemConfigInfo();
        }
        
        // 只更新启用状态，保留原有title和content
        originalConfig.setEnabled(enabled);
        
        BizMessageNotifyTemplate bizMessageNotifyTemplate = new BizMessageNotifyTemplate();
        bizMessageNotifyTemplate.setId(id);
        bizMessageNotifyTemplate.setSystemConfigs(originalConfig);
        bizMessageNotifyTemplate.setUpdateById(getUserId().toString());
        bizMessageNotifyTemplate.setUpdateBy(getLoginName());
        bizMessageNotifyTemplate.setUpdateTime(DateUtils.getNowDate());
        return toAjax(bizMessageNotifyTemplateService.updateBizMessageNotifyTemplate(bizMessageNotifyTemplate));
    }

    /**
     * 修改保存消息通知模板
     */
    @RequiresPermissions("library:message_notify_template:edit")
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BizMessageNotifyTemplate bizMessageNotifyTemplate) {
        bizMessageNotifyTemplate.setUpdateById(getUserId().toString());
        bizMessageNotifyTemplate.setUpdateBy(getLoginName());
        bizMessageNotifyTemplate.setUpdateTime(DateUtils.getNowDate());
        return toAjax(bizMessageNotifyTemplateService.updateBizMessageNotifyTemplate(bizMessageNotifyTemplate));
    }

    /**
     * 删除消息通知模板
     */
    @RequiresPermissions("library:message_notify_template:remove")
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bizMessageNotifyTemplateService.deleteBizMessageNotifyTemplateByIds(ids.split(",")));
    }

    /**
     * 校验通知编码
     */
    @PostMapping("/checkCodeUnique")
    @ResponseBody
    public boolean checkCodeUnique(BizMessageNotifyTemplate bizMessageNotifyTemplate)
    {
        return bizMessageNotifyTemplateService.checkCodeUnique(bizMessageNotifyTemplate);
    }

    /**
     * 选择已订阅用户列表
     */
    @GetMapping("/subscribes/subscribed/{templateId}")
    public String subscribedUser(@PathVariable("templateId") String templateId, ModelMap map)
    {
        map.put("templateId", templateId);
        return prefix + "/subscribedUser";
    }

    /**
     * 查询已订阅用户列表
     */
    @RequiresPermissions("library:message_notify_template:list")
    @PostMapping("/subscribes/subscribed")
    @ResponseBody
    public TableDataInfo subscribedUserList(BizMessageNotifyTemplate bizMessageNotifyTemplate, @RequestParam Map<String, Object> paramMap)
    {
        startPage();
        List<SysUser> list = bizMessageNotifyTemplateService.selectSubscribedUserList(bizMessageNotifyTemplate);
        return getDataTable(list);
    }

    /**
     * 批量选择用户授权
     */
    @RequiresPermissions("library:message_notify_template:edit")
    @Log(title = "订阅用户", businessType = BusinessType.GRANT)
    @PostMapping("/subscribes/select")
    @ResponseBody
    public AjaxResult selectSubscribed(String templateId, String userIds)
    {
        if (StringUtils.isEmpty(templateId)) {
            return error("模板标识不能为空");
        }

        if (StringUtils.isEmpty(userIds)) {
            return error("用户标识不能为空");
        }

        String[] receiverIds = userIds.split(",");

        SysUser sysUser = new SysUser();
        sysUser.getParams().put("userIds", receiverIds);
        List<SysUser> userList = sysUserService.selectUserList(sysUser);
        if (userList == null || userList.isEmpty()) {
            return error("未找到指定的用户");
        }

        bizMessageNotifySubscriberService.deleteBizMessageNotifySubscriberByTemplateId(templateId, receiverIds);

        String createUserId = getUserId().toString();
        String createUserName = getUserName();
        Date currentDate = DateUtils.getNowDate();

        List<BizMessageNotifySubscriber> subscribers = new ArrayList<>();
        for (SysUser user : userList) {
            BizMessageNotifySubscriber subscriber = new BizMessageNotifySubscriber();
            subscriber.setTemplateId(templateId);
            subscriber.setSubscriberId(user.getUserId().toString());
            subscriber.setSubscriberName(user.getUserName());
            subscriber.setSubscribedSystemNotify(true);
            subscriber.setSubscribedSmsNotify(false);
            subscriber.setSubscribedAppNotify(false);
            subscriber.setCreateBy(createUserName);
            subscriber.setCreateById(createUserId);
            subscriber.setUpdateBy(createUserName);
            subscriber.setUpdateById(createUserId);
            subscriber.setCreateTime(currentDate);
            subscriber.setUpdateTime(currentDate);

            subscribers.add(subscriber);
        }

        return toAjax(bizMessageNotifySubscriberService.insertBizMessageNotifySubscribers(subscribers));
    }

    /**
     * 选择未订阅用户列表
     */
    @GetMapping("/subscribes/unsubscribed/{templateId}")
    public String unsubscribedUser(@PathVariable("templateId") String templateId, ModelMap map)
    {
        map.put("templateId", templateId);
        return prefix + "/unsubscribedUser";
    }

    /**
     * 查询未订阅用户列表
     */
    @RequiresPermissions("library:message_notify_template:list")
    @PostMapping("/subscribes/unsubscribed")
    @ResponseBody
    public TableDataInfo unsubscribedUserList(BizMessageNotifyTemplate bizMessageNotifyTemplate, @RequestParam Map<String, Object> paramMap)
    {
        startPage();
        List<SysUser> list = bizMessageNotifyTemplateService.selectUnsubscribedUserList(bizMessageNotifyTemplate);
        return getDataTable(list);
    }

    /**
     * 批量取消订阅
     */
    @RequiresPermissions("library:message_notify_template:edit")
    @Log(title = "批量取消订阅", businessType = BusinessType.GRANT)
    @PostMapping("/subscribes/cancel")
    @ResponseBody
    public AjaxResult cancelSubscribe(String templateId, String userIds)
    {
        if (StringUtils.isEmpty(templateId)) {
            return error("模板标识不能为空");
        }

        if (StringUtils.isEmpty(userIds)) {
            return error("用户标识不能为空");
        }

        String[] receiverIds = userIds.split(",");
        return toAjax(bizMessageNotifySubscriberService.deleteBizMessageNotifySubscriberByTemplateId(templateId, receiverIds));
    }
} 