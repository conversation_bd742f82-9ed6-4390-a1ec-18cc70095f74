package com.metaorg.web.controller.library;

import java.util.*;
import com.metaorg.common.core.text.Convert;
import com.metaorg.common.utils.StringUtils;
import com.metaorg.library.domain.*;
import com.metaorg.library.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.metaorg.common.core.controller.BaseController;
import com.metaorg.common.core.page.TableDataInfo;
import com.metaorg.common.annotation.Log;
import com.metaorg.common.enums.BusinessType;
import com.metaorg.common.core.domain.AjaxResult;
import com.metaorg.common.utils.DateUtils;
import org.springframework.ui.ModelMap;

/**
 * 消息通知列表Controller
 *
 * <AUTHOR>
 * @date 2025-04-28
 */
@Controller
@RequestMapping("/library/message/notify")
public class BizMessageNotifyController extends BaseController {
    private String prefix = "library/message/notify";

    @Autowired
    private IBizMessageNotifyRecordService bizMessageNotifyRecordService;

    @GetMapping()
    public String notifyIndex() {
        return prefix + "/notify";
    }

    /**
     * 查看消息通知详情
     */
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") String id, ModelMap modelMap) {
        BizMessageNotifyRecord bizMessageNotifyRecord = bizMessageNotifyRecordService.selectBizMessageNotifyRecordById(id);
        bizMessageNotifyRecord.setId(id);
        bizMessageNotifyRecord.setIsRead(true);
        bizMessageNotifyRecord.setReadTime(DateUtils.getNowDate());
        bizMessageNotifyRecord.setUpdateTime(DateUtils.getNowDate());

        bizMessageNotifyRecordService.updateBizMessageNotifyRecordByIds(bizMessageNotifyRecord);
        bizMessageNotifyRecord = bizMessageNotifyRecordService.selectBizMessageNotifyRecordById(id);
        modelMap.put("record", bizMessageNotifyRecord);
        return prefix + "/detail";
    }

    /**
     * 获取列表查询条件
     */
    private void getQueryOptions(BizMessageNotifyRecord bizMessageNotifyRecord, Map<String, Object> paramMap) {
        String keyword = Convert.toStr(paramMap.get("keyword"), "");
        if (StringUtils.isNotEmpty(keyword)) {
            bizMessageNotifyRecord.getParams().put("keyword", keyword);
        }

        String businessType = Convert.toStr(paramMap.get("business_type"), "");
        if (StringUtils.isNotEmpty(businessType)) {
            bizMessageNotifyRecord.setBusinessType(businessType);
        }

        String messageType = Convert.toStr(paramMap.get("message_type"), "");
        if (StringUtils.isNotEmpty(messageType)) {
            bizMessageNotifyRecord.setMessageType(messageType);
        }

        Boolean isRead = Convert.toBool(paramMap.get("is_read"), null);
        if (isRead != null) {
            bizMessageNotifyRecord.setIsRead(isRead);
        }

        String sendTimeBegin = Convert.toStr(paramMap.get("sendTimeBegin"), "");
        if (StringUtils.isNotEmpty(sendTimeBegin)) {
            bizMessageNotifyRecord.getParams().put("sendTimeBegin", sendTimeBegin);
        }

        String sendTimeEnd = Convert.toStr(paramMap.get("sendTimeEnd"), "");
        if (StringUtils.isNotEmpty(sendTimeEnd)) {
            bizMessageNotifyRecord.getParams().put("sendTimeEnd", sendTimeEnd);
        }
    }

    /**
     * 查询消息通知记录列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BizMessageNotifyRecord bizMessageNotifyRecord, @RequestParam Map<String, Object> paramMap) {
        if (bizMessageNotifyRecord == null) {
            bizMessageNotifyRecord = new BizMessageNotifyRecord();
        }
        
        bizMessageNotifyRecord.setReceiverId(getSysUser().getUserId());

        getQueryOptions(bizMessageNotifyRecord, paramMap);
        startPage();
        List<BizMessageNotifyRecord> list = bizMessageNotifyRecordService.selectBizMessageNotifyRecordList(bizMessageNotifyRecord);
        return getDataTable(list);
    }
 
    /**
     * 批量阅读消息通知
     */
    @Log(title = "批量阅读消息通知", businessType = BusinessType.UPDATE)
    @PostMapping("/batch_read")
    @ResponseBody
    public AjaxResult batchRead(BizMessageNotifyRecord bizMessageNotifyRecord)
    {
        Date currentDate = DateUtils.getNowDate();
        bizMessageNotifyRecord.setIsRead(true);
        bizMessageNotifyRecord.setReadTime(currentDate);
        bizMessageNotifyRecord.setUpdateTime(currentDate);

        return toAjax(bizMessageNotifyRecordService.updateBizMessageNotifyRecordByIds(bizMessageNotifyRecord));
    }
}
